const { query } = require('../models/database');
const cron = require('node-cron');

/**
 * Data Retention Service
 * Handles automatic cleanup of location data based on user preferences and policies
 */
class DataRetentionService {
  constructor() {
    this.isRunning = false;
    this.cleanupJob = null;
  }

  /**
   * Start the data retention service
   */
  start() {
    if (this.isRunning) {
      console.log('📅 Data retention service is already running');
      return;
    }

    // Run cleanup daily at 2 AM
    this.cleanupJob = cron.schedule('0 2 * * *', async () => {
      await this.performCleanup();
    }, {
      scheduled: true,
      timezone: 'Asia/Riyadh'
    });

    this.isRunning = true;
    console.log('📅 Data retention service started - cleanup scheduled daily at 2 AM');

    // Run initial cleanup
    this.performCleanup();
  }

  /**
   * Stop the data retention service
   */
  stop() {
    if (this.cleanupJob) {
      this.cleanupJob.stop();
      this.cleanupJob = null;
    }
    this.isRunning = false;
    console.log('📅 Data retention service stopped');
  }

  /**
   * Perform comprehensive data cleanup
   */
  async performCleanup() {
    try {
      console.log('📅 Starting data retention cleanup...');
      
      const results = {
        locationUpdates: 0,
        activityLogs: 0,
        sessions: 0,
        adminLogs: 0,
        consentRecords: 0
      };

      // Clean up location updates based on user retention preferences
      results.locationUpdates = await this.cleanupLocationUpdates();
      
      // Clean up old activity logs
      results.activityLogs = await this.cleanupActivityLogs();
      
      // Clean up old sessions
      results.sessions = await this.cleanupOldSessions();
      
      // Clean up old admin access logs
      results.adminLogs = await this.cleanupAdminAccessLogs();
      
      // Clean up withdrawn consent records
      results.consentRecords = await this.cleanupConsentRecords();

      // Update session statuses
      await this.updateSessionStatuses();

      console.log('📅 Data retention cleanup completed:', results);
      
      // Log cleanup activity
      await this.logCleanupActivity(results);

    } catch (error) {
      console.error('❌ Error during data retention cleanup:', error);
    }
  }

  /**
   * Clean up location updates based on user retention preferences
   */
  async cleanupLocationUpdates() {
    try {
      const result = await query(`
        DELETE FROM user_location_updates 
        WHERE id IN (
          SELECT ulu.id 
          FROM user_location_updates ulu
          LEFT JOIN user_location_consent ulc ON ulu.user_id = ulc.user_id 
            AND ulc.consent_given = true 
            AND ulc.consent_type IN ('browser_geolocation', 'full_tracking')
          WHERE 
            -- Delete if no consent or consent withdrawn
            (ulc.id IS NULL OR ulc.consent_given = false)
            OR 
            -- Delete if older than retention period
            (ulu.created_at < NOW() - INTERVAL '1 day' * COALESCE(ulc.data_retention_days, 90))
            OR
            -- Delete if older than 1 year (maximum retention)
            (ulu.created_at < NOW() - INTERVAL '365 days')
        )
      `);

      return result.rowCount || 0;
    } catch (error) {
      console.error('Error cleaning up location updates:', error);
      return 0;
    }
  }

  /**
   * Clean up old activity logs
   */
  async cleanupActivityLogs() {
    try {
      // Keep activity logs for maximum 2 years
      const result = await query(`
        DELETE FROM user_activity_log 
        WHERE created_at < NOW() - INTERVAL '730 days'
      `);

      return result.rowCount || 0;
    } catch (error) {
      console.error('Error cleaning up activity logs:', error);
      return 0;
    }
  }

  /**
   * Clean up old offline sessions
   */
  async cleanupOldSessions() {
    try {
      // Delete offline sessions older than 30 days
      const result = await query(`
        DELETE FROM user_sessions 
        WHERE status = 'offline' 
        AND (logout_time < NOW() - INTERVAL '30 days' OR last_activity < NOW() - INTERVAL '30 days')
      `);

      return result.rowCount || 0;
    } catch (error) {
      console.error('Error cleaning up old sessions:', error);
      return 0;
    }
  }

  /**
   * Clean up old admin access logs
   */
  async cleanupAdminAccessLogs() {
    try {
      // Keep admin access logs for 2 years for audit purposes
      const result = await query(`
        DELETE FROM admin_location_access_log 
        WHERE created_at < NOW() - INTERVAL '730 days'
      `);

      return result.rowCount || 0;
    } catch (error) {
      console.error('Error cleaning up admin access logs:', error);
      return 0;
    }
  }

  /**
   * Clean up old consent records
   */
  async cleanupConsentRecords() {
    try {
      // Delete consent records that were withdrawn more than 1 year ago
      const result = await query(`
        DELETE FROM user_location_consent 
        WHERE consent_given = false 
        AND consent_withdrawn_date < NOW() - INTERVAL '365 days'
      `);

      return result.rowCount || 0;
    } catch (error) {
      console.error('Error cleaning up consent records:', error);
      return 0;
    }
  }

  /**
   * Update session statuses
   */
  async updateSessionStatuses() {
    try {
      // Mark sessions as idle if no activity for 15 minutes
      await query(`
        UPDATE user_sessions 
        SET status = 'idle', updated_at = NOW()
        WHERE status = 'active' 
        AND last_activity < NOW() - INTERVAL '15 minutes'
      `);

      // Mark sessions as offline if no activity for 1 hour
      await query(`
        UPDATE user_sessions 
        SET status = 'offline', logout_time = last_activity, updated_at = NOW()
        WHERE status IN ('active', 'idle') 
        AND last_activity < NOW() - INTERVAL '1 hour'
      `);

    } catch (error) {
      console.error('Error updating session statuses:', error);
    }
  }

  /**
   * Log cleanup activity for audit purposes
   */
  async logCleanupActivity(results) {
    try {
      await query(`
        INSERT INTO audit_logs (
          action, details, success, timestamp
        ) VALUES ($1, $2, $3, NOW())
      `, [
        'data_retention_cleanup',
        JSON.stringify({
          location_updates_deleted: results.locationUpdates,
          activity_logs_deleted: results.activityLogs,
          sessions_deleted: results.sessions,
          admin_logs_deleted: results.adminLogs,
          consent_records_deleted: results.consentRecords,
          cleanup_date: new Date().toISOString()
        }),
        true
      ]);
    } catch (error) {
      console.error('Error logging cleanup activity:', error);
    }
  }

  /**
   * Get data retention statistics
   */
  async getRetentionStatistics() {
    try {
      const stats = await query(`
        SELECT 
          (SELECT COUNT(*) FROM user_location_updates) as total_location_updates,
          (SELECT COUNT(*) FROM user_activity_log) as total_activity_logs,
          (SELECT COUNT(*) FROM user_sessions) as total_sessions,
          (SELECT COUNT(*) FROM admin_location_access_log) as total_admin_logs,
          (SELECT COUNT(*) FROM user_location_consent WHERE consent_given = true) as active_consents,
          (SELECT COUNT(*) FROM user_location_consent WHERE consent_given = false) as withdrawn_consents
      `);

      return stats.rows[0];
    } catch (error) {
      console.error('Error getting retention statistics:', error);
      return null;
    }
  }

  /**
   * Force cleanup for specific user
   */
  async cleanupUserData(userId) {
    try {
      console.log(`📅 Cleaning up data for user: ${userId}`);
      
      const results = {
        locationUpdates: 0,
        activityLogs: 0,
        sessions: 0
      };

      // Delete all location updates for user
      const locationResult = await query(
        'DELETE FROM user_location_updates WHERE user_id = $1',
        [userId]
      );
      results.locationUpdates = locationResult.rowCount || 0;

      // Delete activity logs older than 30 days for user
      const activityResult = await query(
        'DELETE FROM user_activity_log WHERE user_id = $1 AND created_at < NOW() - INTERVAL \'30 days\'',
        [userId]
      );
      results.activityLogs = activityResult.rowCount || 0;

      // Delete offline sessions for user
      const sessionResult = await query(
        'DELETE FROM user_sessions WHERE user_id = $1 AND status = \'offline\'',
        [userId]
      );
      results.sessions = sessionResult.rowCount || 0;

      console.log(`📅 User data cleanup completed for ${userId}:`, results);
      return results;

    } catch (error) {
      console.error(`Error cleaning up user data for ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Check if service is running
   */
  isServiceRunning() {
    return this.isRunning;
  }
}

module.exports = new DataRetentionService();
