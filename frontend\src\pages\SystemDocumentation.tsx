import React, { useState } from 'react';
// import { useAuth } from '../services/AuthContext'; // Commented out as not currently used
import UserFlowDiagram from '../components/UserFlowDiagram';
import UserPathTester from '../components/UserPathTester';
import ProgressIndicator, { DocumentSigningSteps, DocumentUploadSteps } from '../components/ProgressIndicator';

const SystemDocumentation: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'flows' | 'testing' | 'progress'>('flows');

  const tabs = [
    { id: 'flows', label: 'مخططات سير العمل', icon: '📊' },
    { id: 'testing', label: 'اختبار المسارات', icon: '🧪' },
    { id: 'progress', label: 'مؤشرات التقدم', icon: '📈' }
  ];

  return (
    <div className="max-w-7xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          توثيق النظام ومخططات سير العمل
        </h1>
        <p className="text-gray-600">
          دليل شامل لفهم واختبار مسارات المستخدمين في نظام التوقيع الإلكتروني
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-8">
        <nav className="-mb-px flex space-x-8 space-x-reverse">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="ml-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'flows' && (
        <div className="space-y-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <UserFlowDiagram userType="admin" />
            <UserFlowDiagram userType="user" />
          </div>

          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-xl font-bold mb-4">وصف مسارات العمل</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-lg font-semibold text-blue-600 mb-3">مسار المدير</h4>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 ml-2 flex-shrink-0"></span>
                    <span><strong>تسجيل الدخول:</strong> الوصول إلى النظام بصلاحيات المدير</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 ml-2 flex-shrink-0"></span>
                    <span><strong>لوحة التحكم:</strong> عرض إحصائيات شاملة ومهام معلقة</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 ml-2 flex-shrink-0"></span>
                    <span><strong>مراجعة المستندات:</strong> فحص المستندات المرفوعة من المستخدمين</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 ml-2 flex-shrink-0"></span>
                    <span><strong>التوقيع:</strong> إضافة التوقيع الرقمي للمستندات المعتمدة</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 ml-2 flex-shrink-0"></span>
                    <span><strong>إدارة المستخدمين:</strong> إضافة وتعديل صلاحيات المستخدمين</span>
                  </li>
                </ul>
              </div>

              <div>
                <h4 className="text-lg font-semibold text-green-600 mb-3">مسار المستخدم العادي</h4>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-green-500 rounded-full mt-2 ml-2 flex-shrink-0"></span>
                    <span><strong>تسجيل الدخول:</strong> الوصول إلى النظام بصلاحيات محدودة</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-green-500 rounded-full mt-2 ml-2 flex-shrink-0"></span>
                    <span><strong>لوحة التحكم:</strong> عرض حالة المستندات والإشعارات</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-green-500 rounded-full mt-2 ml-2 flex-shrink-0"></span>
                    <span><strong>رفع المستندات:</strong> تحميل المستندات للمراجعة والتوقيع</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-green-500 rounded-full mt-2 ml-2 flex-shrink-0"></span>
                    <span><strong>متابعة الحالة:</strong> تتبع حالة المستندات المرفوعة</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-green-500 rounded-full mt-2 ml-2 flex-shrink-0"></span>
                    <span><strong>تحميل النتائج:</strong> تحميل المستندات الموقعة</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'testing' && (
        <div className="space-y-8">
          <UserPathTester />
          
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-xl font-bold mb-4">دليل الاختبار</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-lg font-semibold mb-3">اختبارات المدير</h4>
                <div className="space-y-3 text-sm">
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <strong>اختبار إدارة المستخدمين:</strong>
                    <p className="mt-1">التحقق من قدرة المدير على عرض وإدارة قائمة المستخدمين</p>
                  </div>
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <strong>اختبار رفع التوقيع:</strong>
                    <p className="mt-1">التأكد من إمكانية رفع وحفظ التوقيعات الرقمية</p>
                  </div>
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <strong>اختبار توقيع المستندات:</strong>
                    <p className="mt-1">فحص عملية إضافة التوقيع للمستندات</p>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-lg font-semibold mb-3">اختبارات المستخدم</h4>
                <div className="space-y-3 text-sm">
                  <div className="p-3 bg-green-50 rounded-lg">
                    <strong>اختبار رفع المستندات:</strong>
                    <p className="mt-1">التحقق من قدرة المستخدم على رفع المستندات للمراجعة</p>
                  </div>
                  <div className="p-3 bg-green-50 rounded-lg">
                    <strong>اختبار عرض التاريخ:</strong>
                    <p className="mt-1">التأكد من إمكانية عرض تاريخ المستندات المرفوعة</p>
                  </div>
                  <div className="p-3 bg-green-50 rounded-lg">
                    <strong>اختبار الإعدادات:</strong>
                    <p className="mt-1">فحص إمكانية تعديل إعدادات الحساب الشخصي</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'progress' && (
        <div className="space-y-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-xl font-bold mb-4">مؤشر توقيع المستندات</h3>
              <ProgressIndicator steps={DocumentSigningSteps(2)} />
              <p className="text-sm text-gray-600 mt-4">
                يوضح هذا المؤشر مراحل عملية توقيع المستندات من الرفع إلى الاكتمال
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-xl font-bold mb-4">مؤشر رفع المستندات</h3>
              <ProgressIndicator steps={DocumentUploadSteps(3)} />
              <p className="text-sm text-gray-600 mt-4">
                يعرض مراحل عملية رفع المستندات من الاختيار إلى المعالجة النهائية
              </p>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-xl font-bold mb-4">فوائد مؤشرات التقدم</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h4 className="font-semibold mb-2">وضوح العملية</h4>
                <p className="text-sm text-gray-600">
                  يساعد المستخدمين على فهم مكانهم في العملية والخطوات المتبقية
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h4 className="font-semibold mb-2">تحسين التجربة</h4>
                <p className="text-sm text-gray-600">
                  يقلل من القلق والارتباك ويحسن من رضا المستخدمين
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                  </svg>
                </div>
                <h4 className="font-semibold mb-2">تتبع الأداء</h4>
                <p className="text-sm text-gray-600">
                  يساعد في تحديد نقاط الاختناق وتحسين كفاءة العمليات
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SystemDocumentation;
