# WhatsApp Notification System

## Overview
The system includes WhatsApp notifications for document signing events with Arabic text support, configurable recipients, retry logic, and comprehensive security measures.

## Features
- ✅ Arabic text support in notifications
- ✅ Configurable recipients via environment variables
- ✅ Retry logic for failed deliveries
- ✅ Security measures (no sensitive content in messages)
- ✅ Comprehensive audit logging
- ✅ Real-time notifications for document events

## Setup Instructions

### 1. Twilio WhatsApp Setup

#### Create Twilio Account
1. Go to [https://www.twilio.com](https://www.twilio.com)
2. Sign up and create a free account
3. Verify your phone number
4. Complete account setup

#### Get WhatsApp Sandbox Access
1. In Twi<PERSON> Console, go to "Messaging" → "Try it out" → "Send a WhatsApp message"
2. Note your sandbox number (format: `whatsapp:+***********`)
3. Send the join code from your WhatsApp to the sandbox number
4. Wait for confirmation message

#### Get Credentials
1. From Twilio Console Dashboard, copy:
   - Account SID
   - Auth Token
2. Note your WhatsApp sandbox number

### 2. Environment Configuration

Add to your `.env` file:
```env
# WhatsApp Notifications
TWILIO_ACCOUNT_SID=your_account_sid_here
TWILIO_AUTH_TOKEN=your_auth_token_here
TWILIO_WHATSAPP_FROM=whatsapp:+***********
TWILIO_WHATSAPP_TO=whatsapp:+966XXXXXXXXX

# Notification Settings
NOTIFICATIONS_ENABLED=true
NOTIFICATION_RETRY_ATTEMPTS=3
NOTIFICATION_RETRY_DELAY=5000
```

### 3. Testing

Run the notification test:
```bash
cd backend
node test-notifications.js
```

## Usage

### Automatic Notifications
The system automatically sends WhatsApp notifications for:
- Document uploads
- Document signing completion
- Document verification requests
- System alerts

### Manual Notifications
You can trigger notifications programmatically:
```javascript
const { sendWhatsAppNotification } = require('./src/services/notificationService');

await sendWhatsAppNotification(
  'Document signed successfully! 📄✅',
  process.env.TWILIO_WHATSAPP_TO
);
```

## Message Templates

### Document Upload
```
📄 مستند جديد تم رفعه
New document uploaded
Document: [filename]
Time: [timestamp]
```

### Document Signed
```
✅ تم توقيع المستند بنجاح
Document signed successfully
Document: [filename]
Signer: [user_name]
Serial: [serial_number]
```

### Verification Request
```
🔍 طلب تحقق من مستند
Document verification requested
Document: [filename]
Serial: [serial_number]
```

## Security Features

- **No Sensitive Data**: Messages contain only basic metadata
- **Audit Logging**: All notifications are logged with timestamps
- **Rate Limiting**: Prevents notification spam
- **Retry Logic**: Ensures delivery reliability
- **Environment-based Configuration**: Secure credential management

## Troubleshooting

### Common Issues

1. **Messages not sending**
   - Check Twilio credentials in `.env`
   - Verify WhatsApp sandbox setup
   - Check phone number format

2. **Arabic text not displaying**
   - Ensure UTF-8 encoding
   - Check Twilio account supports Unicode

3. **Rate limiting errors**
   - Reduce notification frequency
   - Check Twilio account limits

### Debug Mode
Enable debug logging:
```env
DEBUG_NOTIFICATIONS=true
```

## Production Considerations

1. **Upgrade to Twilio WhatsApp Business API** for production use
2. **Configure proper phone number verification**
3. **Set up monitoring for failed notifications**
4. **Implement notification preferences per user**
5. **Consider message templates for different languages**
