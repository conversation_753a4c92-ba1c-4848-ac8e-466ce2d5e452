import api from './api';
import fallbackAuthService from './fallbackAuthService';
import mobileDetection from '../utils/mobileDetection';

/**
 * Error Recovery Service
 * Handles error scenarios and provides recovery mechanisms
 * Manages device loss, authentication failure, and account recovery
 */

export interface ErrorContext {
  errorType: 'device_loss' | 'account_locked' | 'network_error' | 'server_error' | 'authentication_failure';
  errorCode?: string;
  errorMessage: string;
  userId?: string;
  email?: string;
  deviceInfo?: any;
  timestamp: string;
  retryCount: number;
  canRetry: boolean;
  suggestedActions: string[];
}

export interface RecoveryOption {
  type: 'retry' | 'fallback' | 'reset' | 'contact_support' | 'device_recovery';
  title: string;
  titleArabic: string;
  description: string;
  descriptionArabic: string;
  icon: string;
  priority: number; // 1=high, 2=medium, 3=low
  available: boolean;
  action: () => Promise<any>;
}

export interface DeviceLossRecovery {
  step: 'verification' | 'identity_check' | 'new_device_setup' | 'complete';
  verificationMethod: 'email' | 'sms' | 'security_questions';
  recoveryCode?: string;
  newDeviceInfo?: any;
}

class ErrorRecoveryService {
  private recoveryHistory: ErrorContext[] = [];
  private maxHistorySize = 50;

  /**
   * Analyze error and provide recovery context
   */
  analyzeError(error: any, context: Partial<ErrorContext> = {}): ErrorContext {
    const errorContext: ErrorContext = {
      errorType: this.categorizeError(error),
      errorCode: error.code || error.name,
      errorMessage: error.message || 'خطأ غير معروف',
      timestamp: new Date().toISOString(),
      retryCount: context.retryCount || 0,
      canRetry: this.canRetryError(error),
      suggestedActions: this.getSuggestedActions(error),
      ...context
    };

    // Add to history
    this.addToHistory(errorContext);

    return errorContext;
  }

  /**
   * Categorize error type
   */
  private categorizeError(error: any): ErrorContext['errorType'] {
    const errorMessage = error.message?.toLowerCase() || '';
    const errorName = error.name?.toLowerCase() || '';

    if (errorMessage.includes('device') || errorMessage.includes('جهاز')) {
      return 'device_loss';
    }

    if (errorMessage.includes('locked') || errorMessage.includes('مؤقت')) {
      return 'account_locked';
    }

    if (errorMessage.includes('network') || errorMessage.includes('شبكة')) {
      return 'network_error';
    }

    if (error.status >= 500) {
      return 'server_error';
    }

    // Default to authentication failure for login/auth related errors
    return 'authentication_failure';
  }

  /**
   * Check if error can be retried
   */
  private canRetryError(error: any): boolean {
    const nonRetryableErrors = [
      'NotSupportedError',
      'SecurityError',
      'InvalidStateError'
    ];

    return !nonRetryableErrors.includes(error.name);
  }

  /**
   * Get suggested actions for error
   */
  private getSuggestedActions(error: any): string[] {
    const actions: string[] = [];
    const errorType = this.categorizeError(error);

    switch (errorType) {
      case 'authentication_failure':
        actions.push('تأكد من صحة البريد الإلكتروني وكلمة المرور');
        actions.push('جرب إعادة تعيين كلمة المرور إذا نسيتها');
        actions.push('تأكد من اتصالك بالإنترنت');
        break;

      case 'device_loss':
        actions.push('استخدم جهاز آخر مسجل');
        actions.push('استخدم استرداد الحساب بالبريد الإلكتروني');
        actions.push('تواصل مع الدعم الفني');
        break;

      case 'account_locked':
        actions.push('انتظر انتهاء فترة الحظر');
        actions.push('استخدم استرداد الحساب');
        actions.push('تواصل مع الدعم الفني');
        break;

      case 'network_error':
        actions.push('تحقق من اتصال الإنترنت');
        actions.push('أعد المحاولة بعد قليل');
        actions.push('جرب شبكة أخرى');
        break;

      case 'server_error':
        actions.push('أعد المحاولة بعد قليل');
        actions.push('تحقق من حالة الخدمة');
        actions.push('تواصل مع الدعم الفني');
        break;
    }

    return actions;
  }

  /**
   * Get recovery options for error context
   */
  async getRecoveryOptions(errorContext: ErrorContext): Promise<RecoveryOption[]> {
    const options: RecoveryOption[] = [];

    // Retry option
    if (errorContext.canRetry && errorContext.retryCount < 3) {
      options.push({
        type: 'retry',
        title: 'Retry',
        titleArabic: 'إعادة المحاولة',
        description: 'Try the authentication again',
        descriptionArabic: 'جرب المصادقة مرة أخرى',
        icon: '🔄',
        priority: 1,
        available: true,
        action: async () => {
          // This would be handled by the calling component
          return { type: 'retry' };
        }
      });
    }

    // Fallback authentication options
    if (errorContext.errorType === 'authentication_failure') {
      const fallbackMethods = await fallbackAuthService.getAvailableMethods(errorContext.userId);

      fallbackMethods.forEach(method => {
        if (method.enabled) {
          options.push({
            type: 'fallback',
            title: method.name,
            titleArabic: method.nameArabic,
            description: `Use ${method.name} instead`,
            descriptionArabic: `استخدم ${method.nameArabic} بدلاً من ذلك`,
            icon: method.icon,
            priority: 2,
            available: true,
            action: async () => {
              return { type: 'fallback', method: method.type };
            }
          });
        }
      });
    }

    // Device recovery option
    if (errorContext.errorType === 'device_loss') {
      options.push({
        type: 'device_recovery',
        title: 'Device Recovery',
        titleArabic: 'استرداد الجهاز',
        description: 'Recover access using another device or method',
        descriptionArabic: 'استرد الوصول باستخدام جهاز آخر أو طريقة أخرى',
        icon: '📱',
        priority: 1,
        available: true,
        action: async () => {
          return { type: 'device_recovery' };
        }
      });
    }

    // Reset option for locked accounts
    if (errorContext.errorType === 'account_locked') {
      options.push({
        type: 'reset',
        title: 'Reset Account',
        titleArabic: 'إعادة تعيين الحساب',
        description: 'Reset your account security settings',
        descriptionArabic: 'أعد تعيين إعدادات أمان حسابك',
        icon: '🔓',
        priority: 2,
        available: true,
        action: async () => {
          return { type: 'reset' };
        }
      });
    }

    // Contact support option (always available)
    options.push({
      type: 'contact_support',
      title: 'Contact Support',
      titleArabic: 'تواصل مع الدعم',
      description: 'Get help from our support team',
      descriptionArabic: 'احصل على مساعدة من فريق الدعم',
      icon: '🆘',
      priority: 3,
      available: true,
      action: async () => {
        return { type: 'contact_support' };
      }
    });

    return options.sort((a, b) => a.priority - b.priority);
  }

  /**
   * Handle device loss recovery
   */
  async handleDeviceLossRecovery(email: string): Promise<DeviceLossRecovery> {
    try {
      // Start device recovery process
      const response = await api.post('/auth/device-recovery/start', {
        email,
        deviceInfo: mobileDetection.getCapabilities()
      });

      if (response.data.success) {
        return {
          step: 'verification',
          verificationMethod: response.data.verificationMethod || 'email'
        };
      } else {
        throw new Error(response.data.message || 'فشل في بدء عملية استرداد الجهاز');
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'فشل في بدء عملية استرداد الجهاز');
    }
  }

  /**
   * Verify device recovery code
   */
  async verifyDeviceRecovery(email: string, recoveryCode: string): Promise<DeviceLossRecovery> {
    try {
      const response = await api.post('/auth/device-recovery/verify', {
        email,
        recoveryCode,
        newDeviceInfo: mobileDetection.getCapabilities()
      });

      if (response.data.success) {
        return {
          step: 'new_device_setup',
          verificationMethod: 'email',
          recoveryCode,
          newDeviceInfo: response.data.deviceInfo
        };
      } else {
        throw new Error(response.data.message || 'رمز الاسترداد غير صحيح');
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'فشل في التحقق من رمز الاسترداد');
    }
  }

  /**
   * Complete device recovery
   */
  async completeDeviceRecovery(recoveryData: DeviceLossRecovery): Promise<any> {
    try {
      const response = await api.post('/auth/device-recovery/complete', {
        recoveryCode: recoveryData.recoveryCode,
        newDeviceInfo: recoveryData.newDeviceInfo
      });

      if (response.data.success) {
        return {
          step: 'complete',
          token: response.data.token,
          refreshToken: response.data.refreshToken,
          user: response.data.user
        };
      } else {
        throw new Error(response.data.message || 'فشل في إكمال استرداد الجهاز');
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'فشل في إكمال استرداد الجهاز');
    }
  }

  /**
   * Get error recovery statistics
   */
  getRecoveryStatistics(): any {
    const stats = {
      totalErrors: this.recoveryHistory.length,
      errorTypes: {} as Record<string, number>,
      successfulRecoveries: 0,
      averageRetryCount: 0
    };

    this.recoveryHistory.forEach(error => {
      stats.errorTypes[error.errorType] = (stats.errorTypes[error.errorType] || 0) + 1;
      stats.averageRetryCount += error.retryCount;
    });

    if (stats.totalErrors > 0) {
      stats.averageRetryCount = stats.averageRetryCount / stats.totalErrors;
    }

    return stats;
  }

  /**
   * Add error to history
   */
  private addToHistory(errorContext: ErrorContext): void {
    this.recoveryHistory.unshift(errorContext);
    
    // Keep only recent errors
    if (this.recoveryHistory.length > this.maxHistorySize) {
      this.recoveryHistory = this.recoveryHistory.slice(0, this.maxHistorySize);
    }
  }

  /**
   * Clear recovery history
   */
  clearHistory(): void {
    this.recoveryHistory = [];
  }

  /**
   * Get recent errors
   */
  getRecentErrors(limit: number = 10): ErrorContext[] {
    return this.recoveryHistory.slice(0, limit);
  }

  /**
   * Check if user needs progressive enhancement
   */
  async checkProgressiveEnhancement(userId: string): Promise<string[]> {
    try {
      const suggestions = await fallbackAuthService.getProgressiveEnhancementSuggestions(userId);
      return suggestions;
    } catch (error) {
      console.error('Error getting progressive enhancement suggestions:', error);
      return [];
    }
  }
}

export default new ErrorRecoveryService();
