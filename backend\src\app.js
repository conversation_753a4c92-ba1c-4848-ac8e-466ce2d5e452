const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const multer = require('multer');
const { uploadErrorHandler, memoryMonitor, uploadTimeout } = require('./middleware/errorHandler');
const {
  rateLimitConfigs,
  sessionCleanup,
  resourceMonitor,
  createUploadSizeLimit
} = require('./middleware/resourceManager');
const {
  sanitizeRequestBody,
  sanitizeQueryParams,
  validateRequestHeaders,
  validateFileUpload,
  detectSQLInjection
} = require('./middleware/securityValidation');
const { createCSPMiddleware, handleCSPViolation } = require('./config/csp');
const corsConfig = require('./config/corsConfig');
require('dotenv').config();

const authRoutes = require('./routes/auth');
const fallbackAuthRoutes = require('./routes/fallbackAuth');
const signatureRoutes = require('./routes/signatures');
const documentRoutes = require('./routes/documents');
const userRoutes = require('./routes/users');
const setupRoutes = require('./routes/setup');
const notificationRoutes = require('./routes/notifications');

const analyticsRoutes = require('./routes/analytics');
const locationRoutes = require('./routes/location');

const app = express();

// Enhanced Security middleware with comprehensive headers
app.use(helmet({
  // Disable helmet's CSP to use our custom implementation
  contentSecurityPolicy: false,

  // HTTP Strict Transport Security
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: process.env.NODE_ENV === 'production'
  },

  // X-Frame-Options
  frameguard: {
    action: 'deny'
  },

  // X-Content-Type-Options
  noSniff: true,

  // X-XSS-Protection
  xssFilter: true,

  // Referrer Policy
  referrerPolicy: {
    policy: 'strict-origin-when-cross-origin'
  },

  // Hide X-Powered-By header
  hidePoweredBy: true,

  // Permissions Policy (formerly Feature Policy)
  permissionsPolicy: {
    camera: [],
    microphone: [],
    geolocation: [],
    payment: [],
    usb: [],
    magnetometer: [],
    gyroscope: [],
    accelerometer: []
  }
}));

// Apply custom CSP middleware
app.use(createCSPMiddleware({
  reportOnly: process.env.CSP_REPORT_ONLY === 'true',
  enableReporting: process.env.NODE_ENV === 'production'
}));

// Enhanced CORS configuration with security features
app.use(...corsConfig.getMiddlewareStack());
app.use(morgan('combined'));

// Security validation middleware (applied early)
app.use(validateRequestHeaders);
app.use(sanitizeQueryParams);
app.use(detectSQLInjection);

// Enhanced resource management and rate limiting
app.use(rateLimitConfigs.general);
app.use(sessionCleanup);
app.use(resourceMonitor);

// Memory monitoring for all requests
app.use(memoryMonitor);

// Dynamic upload size limiting based on available memory
app.use(createUploadSizeLimit());

// Upload timeout for file upload routes
app.use('/api/documents', uploadTimeout(15 * 60 * 1000)); // 15 minutes for documents
app.use('/api/signatures', uploadTimeout(5 * 60 * 1000)); // 5 minutes for signatures

// Body parsing middleware - configured for large file uploads
app.use(express.json({
  limit: '500mb', // Increased for large documents
  parameterLimit: 50000,
  extended: true
}));
app.use(express.urlencoded({
  extended: true,
  limit: '500mb',
  parameterLimit: 50000
}));

// Apply request body sanitization after parsing
app.use(sanitizeRequestBody({
  allowHtml: false,
  maxStringLength: 10000,
  maxObjectDepth: 10
}));

// Health check endpoint
app.get('/health', (req, res) => {
  const memUsage = process.memoryUsage();
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: {
      used: Math.round(memUsage.heapUsed / 1024 / 1024) + 'MB',
      total: Math.round(memUsage.heapTotal / 1024 / 1024) + 'MB'
    },
    message: 'نظام التوقيع الإلكتروني يعمل بشكل طبيعي'
  });
});

// Simple status endpoint for basic checks
app.get('/status', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    message: 'نظام التوقيع الإلكتروني يعمل بشكل طبيعي'
  });
});

// Routes with specific rate limiting
app.use('/api/auth', rateLimitConfigs.auth, authRoutes);
app.use('/api/fallback', rateLimitConfigs.auth, fallbackAuthRoutes);
app.use('/api/signatures', rateLimitConfigs.upload, signatureRoutes);
app.use('/api/documents', rateLimitConfigs.documents, documentRoutes);
app.use('/api/users', rateLimitConfigs.admin, userRoutes);
app.use('/api/setup', rateLimitConfigs.admin, setupRoutes);
app.use('/api/notifications', rateLimitConfigs.general, notificationRoutes);

app.use('/api/analytics', rateLimitConfigs.general, analyticsRoutes);
app.use('/api/location', rateLimitConfigs.general, locationRoutes);

// Security endpoints
app.post('/api/security/csp-report', express.json({ type: 'application/csp-report' }), handleCSPViolation);

// Use enhanced error handling middleware
app.use(uploadErrorHandler);

// Handle frontend routes - redirect to frontend server in development
const frontendRoutes = ['/dashboard', '/documents', '/history', '/signature-upload', '/document-signing', '/sign', '/signing-confirmation', '/signing-error'];
app.get(frontendRoutes, (req, res) => {
  const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
  res.redirect(`${frontendUrl}${req.path}`);
});

// 404 handler for API routes
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

module.exports = app;
