const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001/api';

async function testCurrentSession() {
  try {
    console.log('🔍 Testing current session issue...\n');

    // Since you're already logged in, let's test with the working admin account
    console.log('1. <NAME_EMAIL> (known working account)...');
    
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });

    const { token, user } = loginResponse.data;
    console.log(`✅ Login successful!`);
    console.log(`   User: ${user.email}`);
    console.log(`   Role: ${user.role}`);

    // Test the signatures endpoint
    console.log('\n2. Testing signatures endpoint...');
    try {
      const signaturesResponse = await axios.get(`${API_BASE_URL}/signatures`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log(`✅ Signatures fetch successful!`);
      console.log(`   Signatures count: ${signaturesResponse.data.signatures.length}`);
      
    } catch (sigError) {
      console.log(`❌ Signatures fetch failed:`);
      console.log(`   Status: ${sigError.response?.status}`);
      console.log(`   Message: ${sigError.response?.data?.message || sigError.message}`);
      
      // This will help us see if the issue is with the permission system
      if (sigError.response?.status === 403) {
        console.log(`   🔍 403 error suggests permission system issue`);
        console.log(`   🔍 Even though user has admin role: ${user.role}`);
      }
    }

    // Test profile endpoint to see role
    console.log('\n3. Testing profile endpoint...');
    const profileResponse = await axios.get(`${API_BASE_URL}/auth/profile`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log(`✅ Profile fetch successful!`);
    console.log(`   Role from profile: ${profileResponse.data.user.role}`);

    console.log('\n🎯 Summary:');
    console.log(`   Login role: ${user.role}`);
    console.log(`   Profile role: ${profileResponse.data.user.role}`);
    console.log(`   The issue is likely in the requirePermission middleware`);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    }
  }
}

// Run the test
testCurrentSession();
