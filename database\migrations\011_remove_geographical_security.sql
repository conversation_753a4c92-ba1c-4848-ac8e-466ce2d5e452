-- Remove Over-Engineered Geographical Security System
-- This migration removes complex geographical security tables and columns
-- Keeps only basic location logging functionality
-- Author: Augment Agent
-- Date: 2025-07-18

-- Drop complex geographical security tables
DROP TABLE IF EXISTS geographical_security_alerts CASCADE;
DROP TABLE IF EXISTS notification_logs CASCADE;
DROP TABLE IF EXISTS user_location_history CASCADE;

-- Remove geographical security columns from users table
ALTER TABLE users DROP COLUMN IF EXISTS geographical_security_enabled;
ALTER TABLE users DROP COLUMN IF EXISTS geographical_consent_given;
ALTER TABLE users DROP COLUMN IF EXISTS geographical_consent_date;
ALTER TABLE users DROP COLUMN IF EXISTS trusted_countries;
ALTER TABLE users DROP COLUMN IF EXISTS alert_preferences;
ALTER TABLE users DROP COLUMN IF EXISTS notification_preferences;

-- Note: biometric_auth_logs table has been removed as part of biometric cleanup

-- Create simple location_logs table for basic IP geolocation logging (optional)
CREATE TABLE IF NOT EXISTS location_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    ip_address INET,
    country_code CHAR(2),
    country_name VARCHAR(100),
    region VARCHAR(100),
    city VARCHAR(100),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    source VARCHAR(50), -- 'ipgeolocation.io', 'ip-api.com', etc.
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_location_logs_user_id ON location_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_location_logs_created_at ON location_logs(created_at);

-- Add table comment
COMMENT ON TABLE location_logs IS 'Simple location logging for basic IP geolocation tracking';

-- Drop any remaining geographical security functions
DROP FUNCTION IF EXISTS update_location_history_timestamp() CASCADE;

-- Clean up any remaining indexes related to geographical security
DROP INDEX IF EXISTS idx_user_location_history_user_id;
DROP INDEX IF EXISTS idx_user_location_history_country;
DROP INDEX IF EXISTS idx_user_location_history_last_seen;
DROP INDEX IF EXISTS idx_user_location_history_trust_level;
DROP INDEX IF EXISTS idx_geographical_alerts_user_id;
DROP INDEX IF EXISTS idx_geographical_alerts_status;
DROP INDEX IF EXISTS idx_geographical_alerts_severity;
DROP INDEX IF EXISTS idx_geographical_alerts_type;
DROP INDEX IF EXISTS idx_geographical_alerts_created;
-- Note: biometric_auth_logs indexes already removed as part of biometric cleanup
DROP INDEX IF EXISTS idx_notification_logs_alert_id;
DROP INDEX IF EXISTS idx_notification_logs_user_id;
DROP INDEX IF EXISTS idx_notification_logs_created;
