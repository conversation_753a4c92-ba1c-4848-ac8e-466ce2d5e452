// Unified test environment configuration
const setupTestEnvironment = () => {
  // Environment variables
  process.env.NODE_ENV = 'test';
  process.env.JWT_SECRET = 'test-secret-key-for-testing-only';
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

  // Global mocks
  global.window = {
    location: { protocol: 'https:', hostname: 'localhost' },
    navigator: {
      userAgent: 'test-user-agent'
    }
  };

  // Database test mode
  process.env.DB_NAME = 'esign_test';
  process.env.DISABLE_RATE_LIMITING = 'true';

  // Mock database if real database is not available
  const shouldMockDatabase = process.env.MOCK_DATABASE === 'true';
  if (shouldMockDatabase) {
    const { mockDatabase } = require('./init-test-db');
    jest.doMock('../../src/models/database', () => ({
      query: mockDatabase.query,
      pool: mockDatabase
    }));
  }
  
  // Mock console methods to reduce test noise
  global.console = {
    ...console,
    log: jest.fn(),
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  };
};

// Setup function to be called before each test suite
const setupTestSuite = async () => {
  // Clear any existing timers
  jest.clearAllTimers();
  
  // Reset all mocks
  jest.clearAllMocks();
  
  // Setup fresh environment
  setupTestEnvironment();
};

// Cleanup function to be called after each test suite
const cleanupTestSuite = async () => {
  // Clear any remaining timers
  jest.clearAllTimers();

  // Close any open servers
  try {
    const { closeAllServers } = require('./test-app');
    await closeAllServers();
  } catch (error) {
    // Ignore if module doesn't exist
  }

  // Cleanup middleware intervals

  try {
    const resourceManager = require('../../src/middleware/resourceManager');
    if (resourceManager.cleanup) {
      resourceManager.cleanup();
    }
  } catch (error) {
    // Ignore if module doesn't exist
  }

  // Force garbage collection if available
  if (global.gc) {
    global.gc();
  }
};

// Initialize test environment immediately
setupTestEnvironment();

// Global cleanup for all tests
if (typeof afterAll !== 'undefined') {
  afterAll(async () => {
    await cleanupTestSuite();
  });
}

module.exports = {
  setupTestEnvironment,
  setupTestSuite,
  cleanupTestSuite
};
