import React, { useState, useEffect } from 'react';
import { useNetworkStatus } from '../contexts/NetworkContext';

interface NetworkStatusProps {
  className?: string;
  showWhenOnline?: boolean;
  autoHide?: boolean;
  autoHideDelay?: number;
}

const NetworkStatus: React.FC<NetworkStatusProps> = ({
  className = '',
  showWhenOnline = false,
  autoHide = true,
  autoHideDelay = 3000
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [showReconnected, setShowReconnected] = useState(false);

  const {
    isOnline,
    isConnecting,
    connectionQuality,
    retryCount,
    lastConnected,
    manualRetry
  } = useNetworkStatus();

  // Handle connection state changes
  useEffect(() => {
    if (!isOnline) {
      setIsVisible(true);
      setShowReconnected(false);
    } else if (isOnline && isVisible && !showReconnected) {
      setShowReconnected(true);
      if (autoHide) {
        setTimeout(() => {
          setIsVisible(false);
          setShowReconnected(false);
        }, autoHideDelay);
      }
    }
  }, [isOnline, isVisible, showReconnected, autoHide, autoHideDelay]);

  useEffect(() => {
    if (!isOnline) {
      setIsVisible(true);
    } else if (showWhenOnline) {
      setIsVisible(true);
    }
  }, [isOnline, showWhenOnline]);

  const getStatusColor = () => {
    if (!isOnline) return 'bg-red-500';
    if (isConnecting) return 'bg-yellow-500';
    if (connectionQuality === 'poor') return 'bg-orange-500';
    return 'bg-green-500';
  };

  const getStatusText = () => {
    if (!isOnline) return 'غير متصل';
    if (isConnecting) return 'جاري الاتصال...';
    if (connectionQuality === 'poor') return 'اتصال ضعيف';
    return 'متصل';
  };

  const getStatusIcon = () => {
    if (!isOnline) {
      return (
        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-12.728 12.728m0-12.728l12.728 12.728M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z" />
        </svg>
      );
    }

    if (isConnecting) {
      return (
        <svg className="w-5 h-5 text-white animate-spin" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      );
    }

    if (connectionQuality === 'poor') {
      return (
        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      );
    }

    return (
      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
      </svg>
    );
  };

  if (!isVisible) return null;

  return (
    <div className={`fixed top-4 left-1/2 transform -translate-x-1/2 z-50 ${className}`}>
      <div className={`${getStatusColor()} text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-3 space-x-reverse transition-all duration-300`}>
        <div className="flex items-center space-x-2 space-x-reverse">
          {getStatusIcon()}
          <span className="font-medium text-sm">
            {showReconnected ? 'تم استعادة الاتصال' : getStatusText()}
          </span>
        </div>

        {!isOnline && (
          <div className="flex items-center space-x-2 space-x-reverse">
            {retryCount > 0 && (
              <span className="text-xs opacity-75">
                محاولة {retryCount}
              </span>
            )}
            <button
              onClick={manualRetry}
              disabled={isConnecting}
              className="bg-white bg-opacity-20 hover:bg-opacity-30 disabled:opacity-50 px-2 py-1 rounded text-xs font-medium transition-colors"
            >
              {isConnecting ? 'جاري المحاولة...' : 'إعادة المحاولة'}
            </button>
          </div>
        )}

        {lastConnected && !isOnline && (
          <div className="text-xs opacity-75">
            آخر اتصال: {lastConnected.toLocaleTimeString('ar-SA')}
          </div>
        )}

        <button
          onClick={() => setIsVisible(false)}
          className="text-white hover:text-gray-200 transition-colors"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  );
};

// Toast notification for network status changes
interface NetworkToastProps {
  duration?: number;
}

export const NetworkToast: React.FC<NetworkToastProps> = ({ duration = 4000 }) => {
  const [toasts, setToasts] = useState<Array<{
    id: string;
    type: 'offline' | 'online' | 'poor';
    message: string;
    timestamp: number;
  }>>([]);

  const { isOnline, connectionQuality } = useNetworkStatus();

  // Track previous online state to detect changes
  const [prevOnlineState, setPrevOnlineState] = useState(isOnline);

  useEffect(() => {
    if (prevOnlineState !== isOnline) {
      if (!isOnline) {
        const toast = {
          id: `toast_${Date.now()}`,
          type: 'offline' as const,
          message: 'فقدان الاتصال بالإنترنت',
          timestamp: Date.now()
        };
        setToasts(prev => [...prev, toast]);
      } else {
        const toast = {
          id: `toast_${Date.now()}`,
          type: 'online' as const,
          message: 'تم استعادة الاتصال بالإنترنت',
          timestamp: Date.now()
        };
        setToasts(prev => [...prev, toast]);
      }
      setPrevOnlineState(isOnline);
    }
  }, [isOnline, prevOnlineState]);

  useEffect(() => {
    if (connectionQuality === 'poor' && isOnline) {
      const toast = {
        id: `toast_${Date.now()}`,
        type: 'poor' as const,
        message: 'جودة الاتصال ضعيفة',
        timestamp: Date.now()
      };
      setToasts(prev => [...prev, toast]);
    }
  }, [connectionQuality, isOnline]);

  useEffect(() => {
    // Reduce cleanup frequency to every 5 seconds instead of every second
    const timer = setInterval(() => {
      const now = Date.now();
      setToasts(prev => {
        const filtered = prev.filter(toast => now - toast.timestamp < duration);
        // Only update state if there's actually a change to prevent unnecessary re-renders
        return filtered.length !== prev.length ? filtered : prev;
      });
    }, 5000);

    return () => clearInterval(timer);
  }, [duration]);

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  const getToastColor = (type: string) => {
    switch (type) {
      case 'offline': return 'bg-red-500';
      case 'online': return 'bg-green-500';
      case 'poor': return 'bg-orange-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="fixed bottom-4 right-4 z-50 space-y-2">
      {toasts.map((toast) => (
        <div
          key={toast.id}
          className={`${getToastColor(toast.type)} text-white px-4 py-3 rounded-lg shadow-lg flex items-center justify-between min-w-64 animate-slide-in-right`}
        >
          <span className="text-sm font-medium">{toast.message}</span>
          <button
            onClick={() => removeToast(toast.id)}
            className="text-white hover:text-gray-200 transition-colors mr-2"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      ))}
    </div>
  );
};

export default NetworkStatus;
