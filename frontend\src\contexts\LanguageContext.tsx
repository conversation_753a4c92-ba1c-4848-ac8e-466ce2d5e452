import React, { createContext, useContext, ReactNode } from 'react';

// Arabic translations for the entire application
export const translations = {
  // Navigation
  nav: {
    title: 'نظام التوقيع الإلكتروني',
    dashboard: 'لوحة التحكم',
    signatureUpload: 'رفع التوقيع',
    documentSigning: 'التوقيع المباشر',
    history: 'السجل',
    logout: 'تسجيل الخروج'
  },
  navigation: {
    dashboard: 'لوحة التحكم',
    signatureUpload: 'رفع التوقيع',
    documentSigning: 'التوقيع المباشر',
    history: 'السجل',
    settings: 'الإعدادات',
    security: 'الأمان'
  },

  // Authentication
  auth: {
    login: 'تسجيل الدخول',
    register: 'إنشاء حساب جديد',
    email: 'البريد الإلكتروني',
    password: 'كلمة المرور',
    confirmPassword: 'تأكيد كلمة المرور',
    loginButton: 'دخول',
    registerButton: 'إنشاء حساب',
    loggingIn: 'جاري تسجيل الدخول...',
    creatingAccount: 'جاري إنشاء الحساب...',
    noAccount: 'ليس لديك حساب؟',
    hasAccount: 'لديك حساب بالفعل؟',
    registerHere: 'سجل هنا',
    loginHere: 'سجل دخولك هنا',
    passwordRequirement: 'يجب أن تحتوي كلمة المرور على 8 أحرف على الأقل مع أرقام وحروف'
  },



  // Dashboard
  dashboard: {
    welcome: 'مرحباً بك مرة أخرى',
    subtitle: 'إدارة التوقيعات والمستندات من لوحة التحكم',
    totalSignatures: 'إجمالي التوقيعات',
    signedDocuments: 'المستندات الموقعة',
    status: 'الحالة',
    active: 'نشط',
    quickActions: 'الإجراءات السريعة',
    uploadSignature: 'رفع توقيع جديد',
    signDocument: 'توقيع مستند',
    viewHistory: 'عرض السجل',
    recentDocuments: 'المستندات الأخيرة',
    noDocuments: 'لا توجد مستندات موقعة بعد',
    startSigning: 'ابدأ بتوقيع مستندك الأول!'
  },

  // Signature Upload
  signatureUpload: {
    title: 'رفع التوقيع',
    subtitle: 'قم برفع صورة توقيعك لاستخدامها في توقيع المستندات',
    dropZone: 'اسحب ملف التوقيع هنا، أو',
    browse: 'تصفح',
    supportedFormats: 'يدعم ملفات PNG و JPG و JPEG و SVG حتى 5 ميجابايت',
    uploading: 'جاري رفع التوقيع...',
    yourSignatures: 'توقيعاتك',
    noSignatures: 'لم يتم رفع أي توقيعات بعد',
    uploadFirst: 'قم برفع توقيعك الأول لبدء توقيع المستندات',
    uploadedOn: 'تم الرفع في',
    fileSize: 'حجم الملف',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا التوقيع؟'
  },

  // Document Signing
  documentSigning: {
    title: 'التوقيع المباشر للمستندات',
    subtitle: 'قم برفع مستند PDF وتوقيعه إلكترونياً بشكل مباشر',
    selectDocument: 'اختر مستند PDF',
    selectSignature: 'اختر التوقيع',
    chooseSignature: 'اختر توقيعاً',
    signaturePosition: 'موضع التوقيع (اختياري)',
    xCoordinate: 'الإحداثي السيني (X)',
    yCoordinate: 'الإحداثي الصادي (Y)',
    signDocument: 'توقيع المستند',
    signing: 'جاري التوقيع...',
    instructions: 'كيفية توقيع المستند',
    step1: 'قم برفع مستند PDF عن طريق السحب والإفلات أو النقر على تصفح',
    step2: 'اضبط موضع التوقيع إذا لزم الأمر (اختياري)',
    step3: 'انقر على "توقيع المستند" لتضمين توقيعك',
    step4: 'سيتم حفظ المستند الموقع برقم تسلسلي فريد',
    step5: 'سيتم استخدام أول توقيع متاح تلقائياً',
    note: 'ملاحظة',
    noteText: 'يحصل كل مستند موقع على رقم تسلسلي فريد للتحقق وأغراض التدقيق.'
  },

  // History
  history: {
    title: 'سجل المستندات',
    subtitle: 'عرض جميع المستندات التي قمت بتوقيعها',
    document: 'المستند',
    serialNumber: 'الرقم التسلسلي',
    signedDate: 'تاريخ التوقيع',
    fileSize: 'حجم الملف',
    actions: 'الإجراءات',
    download: 'تحميل',
    view: 'عرض',
    noDocuments: 'لا توجد مستندات موقعة',
    startSigning: 'ابدأ بتوقيع مستندك الأول',
    loading: 'جاري التحميل...',
    page: 'الصفحة',
    of: 'من',
    previous: 'السابق',
    next: 'التالي'
  },

  // Common
  common: {
    loading: 'جاري التحميل...',
    error: 'خطأ',
    success: 'نجح',
    cancel: 'إلغاء',
    confirm: 'تأكيد',
    save: 'حفظ',
    edit: 'تعديل',
    delete: 'حذف',
    close: 'إغلاق',
    back: 'رجوع',
    next: 'التالي',
    previous: 'السابق',
    submit: 'إرسال',
    reset: 'إعادة تعيين',
    search: 'بحث',
    filter: 'تصفية',
    sort: 'ترتيب',
    refresh: 'تحديث',

    // Messages
    preferencesUpdated: 'تم تحديث التفضيلات بنجاح',

    // Errors
    userCancelled: 'تم إلغاء العملية من قبل المستخدم',
    networkError: 'خطأ في الشبكة',
    serverError: 'خطأ في الخادم',

    // Help
    help: 'المساعدة',
    helpInfo1: 'كلمة المرور محفوظة بأمان',
    helpInfo2: 'يمكنك تغيير كلمة المرور في أي وقت',
    helpInfo3: 'استخدم كلمة مرور قوية لحماية حسابك',
    helpInfo4: 'تواصل مع الدعم الفني عند الحاجة'
  },

  // Error Messages
  errors: {
    required: 'هذا الحقل مطلوب',
    invalidEmail: 'البريد الإلكتروني غير صحيح',
    passwordMismatch: 'كلمات المرور غير متطابقة',
    passwordTooShort: 'كلمة المرور يجب أن تكون 8 أحرف على الأقل',
    fileTooLarge: 'حجم الملف كبير جداً',
    invalidFileType: 'نوع الملف غير مدعوم',
    uploadFailed: 'فشل في رفع الملف',
    networkError: 'خطأ في الشبكة',
    serverError: 'خطأ في الخادم',
    unauthorized: 'غير مخول للوصول',
    notFound: 'غير موجود',
    signatureMissing: 'يرجى اختيار توقيع',
    documentMissing: 'يرجى اختيار مستند للتوقيع',
    signingFailed: 'فشل في توقيع المستند',
    deleteFailed: 'فشل في الحذف',
    loadFailed: 'فشل في التحميل'
  },

  // Success Messages
  success: {
    loginSuccess: 'تم تسجيل الدخول بنجاح',
    registerSuccess: 'تم إنشاء الحساب بنجاح',
    signatureUploaded: 'تم رفع التوقيع بنجاح',
    documentSigned: 'تم توقيع المستند بنجاح',
    signatureDeleted: 'تم حذف التوقيع بنجاح',
    documentDownloaded: 'تم تحميل المستند بنجاح'
  },

  // File validation
  fileValidation: {
    pdfOnly: 'يرجى اختيار ملف PDF',
    imageOnly: 'يرجى اختيار ملف صورة (PNG, JPG, JPEG, SVG)',
    maxSize: 'يجب أن يكون حجم الملف أقل من',
    mb: 'ميجابايت'
  }
};

interface LanguageContextType {
  t: typeof translations;
  isRTL: boolean;
  language: string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

interface LanguageProviderProps {
  children: ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const value = {
    t: translations,
    isRTL: true,
    language: 'ar'
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};
