-- Migration: Fallback Authentication Methods
-- Description: Add support for PIN, pattern, and email recovery authentication
-- Provides alternative authentication methods

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Add fallback authentication columns to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS pin_hash TEXT,
ADD COLUMN IF NOT EXISTS pin_enabled BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS pin_hint VARCHAR(100),
ADD COLUMN IF NOT EXISTS failed_pin_attempts INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS pin_locked_until TIMESTAMP,
ADD COLUMN IF NOT EXISTS last_pin_used TIMESTAMP,

ADD COLUMN IF NOT EXISTS pattern_hash TEXT,
ADD COLUMN IF NOT EXISTS pattern_enabled BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS pattern_hint VARCHAR(100),
ADD COLUMN IF NOT EXISTS failed_pattern_attempts INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS pattern_locked_until TIMESTAMP,
ADD COLUMN IF NOT EXISTS last_pattern_used TIMESTAMP,

ADD COLUMN IF NOT EXISTS email_recovery_enabled BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS failed_recovery_attempts INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS recovery_locked_until TIMESTAMP;

-- Recovery codes table for email/SMS recovery
CREATE TABLE IF NOT EXISTS recovery_codes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    code VARCHAR(10) NOT NULL, -- Recovery code (6-8 digits)
    type VARCHAR(20) NOT NULL, -- 'email', 'sms'
    attempts INTEGER DEFAULT 0, -- Failed verification attempts
    expires_at TIMESTAMP NOT NULL, -- Code expiration time
    ip_address INET, -- IP address that requested the code
    user_agent TEXT, -- User agent that requested the code
    used_at TIMESTAMP, -- When code was successfully used
    created_at TIMESTAMP DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT check_code_type CHECK (type IN ('email', 'sms')),
    CONSTRAINT check_attempts_positive CHECK (attempts >= 0),
    CONSTRAINT check_code_format CHECK (code ~ '^[0-9]{4,8}$'),
    
    -- Unique constraint to prevent multiple active codes per user per type
    UNIQUE (user_id, type)
);

-- Fallback authentication logs table
CREATE TABLE IF NOT EXISTS fallback_auth_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    auth_method VARCHAR(20) NOT NULL, -- 'pin', 'pattern', 'email_recovery', 'sms_recovery'
    event_type VARCHAR(20) NOT NULL, -- 'setup', 'authentication', 'failure', 'lockout'
    event_status VARCHAR(20) NOT NULL, -- 'success', 'failure', 'error'
    ip_address INET,
    user_agent TEXT,
    device_info JSONB,
    failure_reason VARCHAR(100), -- Reason for failure
    attempts_count INTEGER, -- Number of attempts made
    session_id VARCHAR(255), -- Session identifier
    timestamp TIMESTAMP DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT check_auth_method CHECK (auth_method IN ('pin', 'pattern', 'email_recovery', 'sms_recovery')),
    CONSTRAINT check_event_type CHECK (event_type IN ('setup', 'authentication', 'failure', 'lockout', 'unlock')),
    CONSTRAINT check_event_status CHECK (event_status IN ('success', 'failure', 'error'))
);

-- Progressive enhancement suggestions table
CREATE TABLE IF NOT EXISTS auth_enhancement_suggestions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    suggestion_type VARCHAR(50) NOT NULL, -- 'setup_pin', 'setup_pattern', 'enable_2fa'
    priority INTEGER DEFAULT 1, -- 1=high, 2=medium, 3=low
    title VARCHAR(200) NOT NULL,
    description TEXT,
    action_url VARCHAR(500),
    dismissed BOOLEAN DEFAULT false,
    dismissed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP, -- When suggestion expires
    
    -- Constraints
    CONSTRAINT check_priority CHECK (priority BETWEEN 1 AND 3),
    CONSTRAINT unique_user_suggestion UNIQUE (user_id, suggestion_type)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_recovery_codes_user_type ON recovery_codes(user_id, type);
CREATE INDEX IF NOT EXISTS idx_recovery_codes_expires ON recovery_codes(expires_at);
CREATE INDEX IF NOT EXISTS idx_recovery_codes_code ON recovery_codes(code);

CREATE INDEX IF NOT EXISTS idx_fallback_auth_logs_user_id ON fallback_auth_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_fallback_auth_logs_method ON fallback_auth_logs(auth_method);
CREATE INDEX IF NOT EXISTS idx_fallback_auth_logs_timestamp ON fallback_auth_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_fallback_auth_logs_event_type ON fallback_auth_logs(event_type);

CREATE INDEX IF NOT EXISTS idx_auth_suggestions_user_id ON auth_enhancement_suggestions(user_id);
CREATE INDEX IF NOT EXISTS idx_auth_suggestions_priority ON auth_enhancement_suggestions(priority);
CREATE INDEX IF NOT EXISTS idx_auth_suggestions_dismissed ON auth_enhancement_suggestions(dismissed) WHERE dismissed = false;

CREATE INDEX IF NOT EXISTS idx_users_pin_enabled ON users(pin_enabled) WHERE pin_enabled = true;
CREATE INDEX IF NOT EXISTS idx_users_pattern_enabled ON users(pattern_enabled) WHERE pattern_enabled = true;
CREATE INDEX IF NOT EXISTS idx_users_pin_locked ON users(pin_locked_until) WHERE pin_locked_until IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_users_pattern_locked ON users(pattern_locked_until) WHERE pattern_locked_until IS NOT NULL;

-- Create functions for fallback authentication management

-- Function to check if user has any fallback methods enabled
CREATE OR REPLACE FUNCTION has_fallback_methods(user_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM users 
        WHERE id = user_uuid 
        AND (pin_enabled = true OR pattern_enabled = true OR email_recovery_enabled = true)
    );
END;
$$ LANGUAGE plpgsql;

-- Function to get user's enabled fallback methods
CREATE OR REPLACE FUNCTION get_user_fallback_methods(user_uuid UUID)
RETURNS TABLE (
    method VARCHAR(20),
    enabled BOOLEAN,
    last_used TIMESTAMP,
    hint VARCHAR(100)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'pin'::VARCHAR(20) as method,
        COALESCE(u.pin_enabled, false) as enabled,
        u.last_pin_used as last_used,
        u.pin_hint as hint
    FROM users u WHERE u.id = user_uuid
    UNION ALL
    SELECT 
        'pattern'::VARCHAR(20) as method,
        COALESCE(u.pattern_enabled, false) as enabled,
        u.last_pattern_used as last_used,
        u.pattern_hint as hint
    FROM users u WHERE u.id = user_uuid
    UNION ALL
    SELECT 
        'email'::VARCHAR(20) as method,
        COALESCE(u.email_recovery_enabled, true) as enabled,
        NULL::TIMESTAMP as last_used,
        NULL::VARCHAR(100) as hint
    FROM users u WHERE u.id = user_uuid;
END;
$$ LANGUAGE plpgsql;

-- Function to log fallback authentication events
CREATE OR REPLACE FUNCTION log_fallback_auth_event(
    p_user_id UUID,
    p_auth_method VARCHAR(20),
    p_event_type VARCHAR(20),
    p_event_status VARCHAR(20),
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_device_info JSONB DEFAULT NULL,
    p_failure_reason VARCHAR(100) DEFAULT NULL,
    p_attempts_count INTEGER DEFAULT NULL,
    p_session_id VARCHAR(255) DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    log_id UUID;
BEGIN
    INSERT INTO fallback_auth_logs (
        user_id, auth_method, event_type, event_status, ip_address,
        user_agent, device_info, failure_reason, attempts_count, session_id
    ) VALUES (
        p_user_id, p_auth_method, p_event_type, p_event_status, p_ip_address,
        p_user_agent, p_device_info, p_failure_reason, p_attempts_count, p_session_id
    ) RETURNING id INTO log_id;
    
    RETURN log_id;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up expired recovery codes
CREATE OR REPLACE FUNCTION cleanup_expired_recovery_codes()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM recovery_codes WHERE expires_at < NOW();
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log cleanup activity
    INSERT INTO logs (action, details) 
    VALUES ('RECOVERY_CODES_CLEANUP', jsonb_build_object(
        'deleted_count', deleted_count,
        'timestamp', NOW()
    ));
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to generate progressive enhancement suggestions
CREATE OR REPLACE FUNCTION generate_auth_suggestions(user_uuid UUID)
RETURNS void AS $$
DECLARE
    user_record RECORD;
BEGIN
    -- Get user information
    SELECT * INTO user_record FROM users WHERE id = user_uuid;

    IF NOT FOUND THEN
        RETURN;
    END IF;

    -- Clear existing suggestions
    DELETE FROM auth_enhancement_suggestions WHERE user_id = user_uuid;
    
    -- Suggest PIN setup if not enabled
    IF NOT COALESCE(user_record.pin_enabled, false) THEN
        INSERT INTO auth_enhancement_suggestions (
            user_id, suggestion_type, priority, title, description, action_url
        ) VALUES (
            user_uuid, 'setup_pin', 2,
            'إعداد رقم سري',
            'أضف رقم سري كطريقة احتياطية للدخول',
            '/settings/pin'
        );
    END IF;
    
    -- Suggest pattern setup if not enabled
    IF NOT COALESCE(user_record.pattern_enabled, false) THEN
        INSERT INTO auth_enhancement_suggestions (
            user_id, suggestion_type, priority, title, description, action_url
        ) VALUES (
            user_uuid, 'setup_pattern', 3,
            'إعداد نمط',
            'أضف نمط كطريقة احتياطية للدخول',
            '/settings/pattern'
        );
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_fallback_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply trigger to users table for fallback columns
CREATE TRIGGER trigger_update_users_fallback_updated_at
    BEFORE UPDATE OF pin_hash, pin_enabled, pattern_hash, pattern_enabled, email_recovery_enabled ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_fallback_updated_at();

-- Add table comments for documentation
COMMENT ON TABLE recovery_codes IS 'Temporary recovery codes for email/SMS authentication';
COMMENT ON TABLE fallback_auth_logs IS 'Audit log for fallback authentication events';
COMMENT ON TABLE auth_enhancement_suggestions IS 'Progressive enhancement suggestions for users';

COMMENT ON COLUMN users.pin_hash IS 'Hashed PIN for fallback authentication';
COMMENT ON COLUMN users.pattern_hash IS 'Hashed pattern for fallback authentication';
COMMENT ON COLUMN users.email_recovery_enabled IS 'Whether email recovery is enabled for this user';

COMMENT ON COLUMN recovery_codes.code IS 'Numeric recovery code (4-8 digits)';
COMMENT ON COLUMN recovery_codes.type IS 'Recovery method type (email, sms)';
COMMENT ON COLUMN recovery_codes.attempts IS 'Number of failed verification attempts';

-- Create a scheduled cleanup job placeholder (requires pg_cron extension)
-- This would typically be configured separately in production
-- SELECT cron.schedule('recovery-codes-cleanup', '0 */6 * * *', 'SELECT cleanup_expired_recovery_codes();');
