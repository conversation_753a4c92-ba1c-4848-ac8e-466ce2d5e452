import { useEffect, useRef, useState, useCallback } from 'react';
import performanceOptimizer, { PerformanceMetrics } from '../utils/performanceOptimizer';

/**
 * React Hook for Performance Monitoring
 * Provides performance tracking and optimization for biometric components
 */

interface PerformanceHookOptions {
  componentName: string;
  enableMetrics: boolean;
  enableCaching: boolean;
  enableOptimization: boolean;
  reportInterval: number; // in milliseconds
}

interface PerformanceHookReturn {
  metrics: PerformanceMetrics;
  isOptimized: boolean;
  cacheHit: boolean;
  measureAsync: <T>(name: string, fn: () => Promise<T>) => Promise<T>;
  measureSync: <T>(name: string, fn: () => T) => T;
  optimizedFetch: (url: string, options?: RequestInit) => Promise<Response>;
  setCache: <T>(key: string, data: T, ttl?: number) => void;
  getCache: <T>(key: string) => T | null;
  generateReport: () => string;
  startProfiling: () => void;
  stopProfiling: () => void;
}

export const usePerformanceMonitoring = (
  options: Partial<PerformanceHookOptions> = {}
): PerformanceHookReturn => {
  const defaultOptions: PerformanceHookOptions = {
    componentName: 'BiometricComponent',
    enableMetrics: true,
    enableCaching: true,
    enableOptimization: true,
    reportInterval: 5000 // 5 seconds
  };

  const config = { ...defaultOptions, ...options };
  
  const [metrics, setMetrics] = useState<PerformanceMetrics>(performanceOptimizer.getMetrics());
  const [isOptimized, setIsOptimized] = useState(false);
  const [cacheHit, setCacheHit] = useState(false);
  const [isProfiling, setIsProfiling] = useState(false);
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const mountTimeRef = useRef<number>(Date.now());
  const renderCountRef = useRef<number>(0);

  // Update metrics periodically
  useEffect(() => {
    if (!config.enableMetrics) return;

    const updateMetrics = () => {
      const currentMetrics = performanceOptimizer.getMetrics();
      setMetrics(currentMetrics);
      
      // Check if performance is optimized
      const isPerformant = 
        currentMetrics.authenticationTime < 2000 && // Less than 2 seconds
        currentMetrics.renderTime < 100 && // Less than 100ms
        currentMetrics.cacheHitRate > 0.7; // More than 70% cache hit rate
      
      setIsOptimized(isPerformant);
    };

    // Initial update
    updateMetrics();

    // Set up interval
    intervalRef.current = setInterval(updateMetrics, config.reportInterval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [config.enableMetrics, config.reportInterval]);

  // Track component lifecycle
  useEffect(() => {
    const componentMountTime = Date.now() - mountTimeRef.current;
    
    if (config.enableMetrics) {
      performance.mark(`${config.componentName}-mount`);
      console.log(`📊 ${config.componentName} mounted in ${componentMountTime}ms`);
    }

    return () => {
      if (config.enableMetrics) {
        performance.mark(`${config.componentName}-unmount`);
        const totalLifetime = Date.now() - mountTimeRef.current;
        console.log(`📊 ${config.componentName} lifetime: ${totalLifetime}ms, renders: ${renderCountRef.current}`);
      }
    };
  }, [config.componentName, config.enableMetrics]);

  // Track renders
  useEffect(() => {
    renderCountRef.current++;
    
    if (config.enableMetrics) {
      performance.mark(`${config.componentName}-render-${renderCountRef.current}`);
    }
  });

  // Optimized async measurement
  const measureAsync = useCallback(async <T>(
    name: string, 
    fn: () => Promise<T>
  ): Promise<T> => {
    if (!config.enableMetrics) {
      return fn();
    }

    return performanceOptimizer.measureAsync(`${config.componentName}-${name}`, fn);
  }, [config.componentName, config.enableMetrics]);

  // Optimized sync measurement
  const measureSync = useCallback(<T>(
    name: string, 
    fn: () => T
  ): T => {
    if (!config.enableMetrics) {
      return fn();
    }

    return performanceOptimizer.measureSync(`${config.componentName}-${name}`, fn);
  }, [config.componentName, config.enableMetrics]);

  // Optimized fetch with caching
  const optimizedFetch = useCallback(async (
    url: string, 
    options: RequestInit = {}
  ): Promise<Response> => {
    if (!config.enableOptimization) {
      return fetch(url, options);
    }

    const response = await performanceOptimizer.optimizedFetch(url, options);
    
    // Check if this was a cache hit
    const wasCacheHit = response.headers.get('x-cache-hit') === 'true';
    setCacheHit(wasCacheHit);
    
    return response;
  }, [config.enableOptimization]);

  // Cache management
  const setCache = useCallback(<T>(
    key: string, 
    data: T, 
    ttl?: number
  ): void => {
    if (!config.enableCaching) return;
    
    const cacheKey = `${config.componentName}-${key}`;
    performanceOptimizer.setCache(cacheKey, data, ttl);
  }, [config.componentName, config.enableCaching]);

  const getCache = useCallback(<T>(key: string): T | null => {
    if (!config.enableCaching) return null;
    
    const cacheKey = `${config.componentName}-${key}`;
    const cached = performanceOptimizer.getCache<T>(cacheKey);
    
    if (cached) {
      setCacheHit(true);
    }
    
    return cached;
  }, [config.componentName, config.enableCaching]);

  // Generate performance report
  const generateReport = useCallback((): string => {
    const baseReport = performanceOptimizer.generateReport();
    const componentStats = `
🧩 Component: ${config.componentName}
⏱️  Mount Time: ${Date.now() - mountTimeRef.current}ms
🔄 Render Count: ${renderCountRef.current}
📈 Optimized: ${isOptimized ? 'Yes' : 'No'}
💾 Cache Hit: ${cacheHit ? 'Yes' : 'No'}
    `;
    
    return baseReport + '\n' + componentStats;
  }, [config.componentName, isOptimized, cacheHit]);

  // Profiling controls
  const startProfiling = useCallback(() => {
    if (!isProfiling) {
      setIsProfiling(true);
      performance.mark(`${config.componentName}-profile-start`);
      console.log(`🔍 Started profiling ${config.componentName}`);
    }
  }, [config.componentName, isProfiling]);

  const stopProfiling = useCallback(() => {
    if (isProfiling) {
      setIsProfiling(false);
      performance.mark(`${config.componentName}-profile-end`);
      performance.measure(
        `${config.componentName}-profile`,
        `${config.componentName}-profile-start`,
        `${config.componentName}-profile-end`
      );
      console.log(`🔍 Stopped profiling ${config.componentName}`);
      console.log(generateReport());
    }
  }, [config.componentName, isProfiling, generateReport]);

  return {
    metrics,
    isOptimized,
    cacheHit,
    measureAsync,
    measureSync,
    optimizedFetch,
    setCache,
    getCache,
    generateReport,
    startProfiling,
    stopProfiling
  };
};

export default usePerformanceMonitoring;
