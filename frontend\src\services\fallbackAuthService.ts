import api from './api';

/**
 * Fallback Authentication Service
 * Provides alternative authentication methods
 * Includes password, PIN, pattern, and email recovery options
 */

export interface FallbackMethod {
  type: 'password' | 'pin' | 'pattern' | 'email' | 'sms';
  name: string;
  nameArabic: string;
  icon: string;
  available: boolean;
  enabled: boolean;
  lastUsed?: string;
  setupRequired: boolean;
}

export interface FallbackAuthResult {
  success: boolean;
  method: string;
  token?: string;
  refreshToken?: string;
  user?: any;
  message?: string;
  requiresSetup?: boolean;
  nextMethod?: string;
}

export interface PinSetupData {
  pin: string;
  confirmPin: string;
  hint?: string;
}

export interface PatternSetupData {
  pattern: number[];
  confirmPattern: number[];
  hint?: string;
}

export interface EmailRecoveryData {
  email: string;
  recoveryCode?: string;
}

class FallbackAuthService {
  private availableMethods: FallbackMethod[] = [
    {
      type: 'password',
      name: 'Password',
      nameArabic: 'كلمة المرور',
      icon: '🔑',
      available: true,
      enabled: true,
      setupRequired: false
    },
    {
      type: 'pin',
      name: 'PIN',
      nameArabic: 'رقم سري',
      icon: '🔢',
      available: true,
      enabled: false,
      setupRequired: true
    },
    {
      type: 'pattern',
      name: 'Pattern',
      nameArabic: 'نمط',
      icon: '⚫',
      available: true,
      enabled: false,
      setupRequired: true
    },
    {
      type: 'email',
      name: 'Email Recovery',
      nameArabic: 'استرداد بالبريد',
      icon: '📧',
      available: true,
      enabled: true,
      setupRequired: false
    }
  ];

  /**
   * Get available fallback methods for user
   */
  async getAvailableMethods(userId?: string): Promise<FallbackMethod[]> {
    try {
      if (userId) {
        // Get user-specific method availability
        const response = await api.get(`/auth/fallback-methods/${userId}`);
        if (response.data.success) {
          return response.data.methods;
        }
      }
      
      // Return default methods
      return this.availableMethods.filter(method => method.available);
    } catch (error) {
      console.error('Error getting fallback methods:', error);
      return this.availableMethods.filter(method => method.available);
    }
  }

  /**
   * Authenticate using password
   */
  async authenticateWithPassword(email: string, password: string): Promise<FallbackAuthResult> {
    try {
      const response = await api.post('/auth/login', {
        email,
        password,
        fallbackMethod: 'password'
      });

      if (response.data.success) {
        return {
          success: true,
          method: 'password',
          token: response.data.token,
          refreshToken: response.data.refreshToken,
          user: response.data.user,
          message: 'تم تسجيل الدخول بنجاح'
        };
      } else {
        return {
          success: false,
          method: 'password',
          message: response.data.message || 'فشل في تسجيل الدخول'
        };
      }
    } catch (error: any) {
      return {
        success: false,
        method: 'password',
        message: error.response?.data?.message || 'خطأ في الشبكة'
      };
    }
  }

  /**
   * Setup PIN for user
   */
  async setupPin(pinData: PinSetupData): Promise<FallbackAuthResult> {
    try {
      if (pinData.pin !== pinData.confirmPin) {
        return {
          success: false,
          method: 'pin',
          message: 'الرقم السري غير متطابق'
        };
      }

      if (pinData.pin.length < 4 || pinData.pin.length > 8) {
        return {
          success: false,
          method: 'pin',
          message: 'الرقم السري يجب أن يكون بين 4 و 8 أرقام'
        };
      }

      const response = await api.post('/auth/setup-pin', {
        pin: pinData.pin,
        hint: pinData.hint
      });

      return {
        success: response.data.success,
        method: 'pin',
        message: response.data.success ? 'تم إعداد الرقم السري بنجاح' : response.data.message
      };
    } catch (error: any) {
      return {
        success: false,
        method: 'pin',
        message: error.response?.data?.message || 'فشل في إعداد الرقم السري'
      };
    }
  }

  /**
   * Authenticate using PIN
   */
  async authenticateWithPin(email: string, pin: string): Promise<FallbackAuthResult> {
    try {
      const response = await api.post('/auth/login-pin', {
        email,
        pin,
        fallbackMethod: 'pin'
      });

      if (response.data.success) {
        return {
          success: true,
          method: 'pin',
          token: response.data.token,
          refreshToken: response.data.refreshToken,
          user: response.data.user,
          message: 'تم تسجيل الدخول بنجاح'
        };
      } else {
        return {
          success: false,
          method: 'pin',
          message: response.data.message || 'الرقم السري غير صحيح'
        };
      }
    } catch (error: any) {
      return {
        success: false,
        method: 'pin',
        message: error.response?.data?.message || 'فشل في تسجيل الدخول'
      };
    }
  }

  /**
   * Setup pattern for user
   */
  async setupPattern(patternData: PatternSetupData): Promise<FallbackAuthResult> {
    try {
      if (JSON.stringify(patternData.pattern) !== JSON.stringify(patternData.confirmPattern)) {
        return {
          success: false,
          method: 'pattern',
          message: 'النمط غير متطابق'
        };
      }

      if (patternData.pattern.length < 4) {
        return {
          success: false,
          method: 'pattern',
          message: 'النمط يجب أن يحتوي على 4 نقاط على الأقل'
        };
      }

      const response = await api.post('/auth/setup-pattern', {
        pattern: patternData.pattern,
        hint: patternData.hint
      });

      return {
        success: response.data.success,
        method: 'pattern',
        message: response.data.success ? 'تم إعداد النمط بنجاح' : response.data.message
      };
    } catch (error: any) {
      return {
        success: false,
        method: 'pattern',
        message: error.response?.data?.message || 'فشل في إعداد النمط'
      };
    }
  }

  /**
   * Authenticate using pattern
   */
  async authenticateWithPattern(email: string, pattern: number[]): Promise<FallbackAuthResult> {
    try {
      const response = await api.post('/auth/login-pattern', {
        email,
        pattern,
        fallbackMethod: 'pattern'
      });

      if (response.data.success) {
        return {
          success: true,
          method: 'pattern',
          token: response.data.token,
          refreshToken: response.data.refreshToken,
          user: response.data.user,
          message: 'تم تسجيل الدخول بنجاح'
        };
      } else {
        return {
          success: false,
          method: 'pattern',
          message: response.data.message || 'النمط غير صحيح'
        };
      }
    } catch (error: any) {
      return {
        success: false,
        method: 'pattern',
        message: error.response?.data?.message || 'فشل في تسجيل الدخول'
      };
    }
  }

  /**
   * Send email recovery code
   */
  async sendEmailRecovery(email: string): Promise<FallbackAuthResult> {
    try {
      const response = await api.post('/auth/send-recovery', {
        email,
        method: 'email'
      });

      return {
        success: response.data.success,
        method: 'email',
        message: response.data.success 
          ? 'تم إرسال رمز الاسترداد إلى بريدك الإلكتروني' 
          : response.data.message
      };
    } catch (error: any) {
      return {
        success: false,
        method: 'email',
        message: error.response?.data?.message || 'فشل في إرسال رمز الاسترداد'
      };
    }
  }

  /**
   * Verify email recovery code and authenticate
   */
  async verifyEmailRecovery(email: string, recoveryCode: string): Promise<FallbackAuthResult> {
    try {
      const response = await api.post('/auth/verify-recovery', {
        email,
        recoveryCode,
        fallbackMethod: 'email'
      });

      if (response.data.success) {
        return {
          success: true,
          method: 'email',
          token: response.data.token,
          refreshToken: response.data.refreshToken,
          user: response.data.user,
          message: 'تم تسجيل الدخول بنجاح'
        };
      } else {
        return {
          success: false,
          method: 'email',
          message: response.data.message || 'رمز الاسترداد غير صحيح'
        };
      }
    } catch (error: any) {
      return {
        success: false,
        method: 'email',
        message: error.response?.data?.message || 'فشل في التحقق من رمز الاسترداد'
      };
    }
  }

  /**
   * Get progressive enhancement suggestions
   */
  async getProgressiveEnhancementSuggestions(userId: string): Promise<string[]> {
    try {
      const methods = await this.getAvailableMethods(userId);
      const suggestions: string[] = [];

      // Check which methods are not set up
      const unsetupMethods = methods.filter(method => 
        method.setupRequired && !method.enabled
      );

      if (unsetupMethods.length > 0) {
        suggestions.push('قم بإعداد طرق مصادقة إضافية لمزيد من الأمان');
        
        unsetupMethods.forEach(method => {
          suggestions.push(`إعداد ${method.nameArabic} كطريقة احتياطية`);
        });
      }



      return suggestions;
    } catch (error) {
      console.error('Error getting enhancement suggestions:', error);
      return [];
    }
  }

  /**
   * Check if fallback is needed based on authentication failure
   */
  shouldUseFallback(authError: string): boolean {
    const fallbackTriggers = [
      'NotAllowedError',
      'NotSupportedError',
      'SecurityError',
      'InvalidStateError',
      'غير مدعوم',
      'غير متاح',
      'فشل',
      'إلغاء'
    ];

    return fallbackTriggers.some(trigger =>
      authError.includes(trigger)
    );
  }

  /**
   * Get recommended fallback method based on context
   */
  getRecommendedFallbackMethod(
    availableMethods: FallbackMethod[], 
    context: 'mobile' | 'desktop' | 'unknown' = 'unknown'
  ): FallbackMethod | null {
    const enabledMethods = availableMethods.filter(method => method.enabled);
    
    if (enabledMethods.length === 0) {
      return availableMethods.find(method => method.type === 'password') || null;
    }

    // Prioritize based on context
    if (context === 'mobile') {
      // On mobile, prefer PIN or pattern
      return enabledMethods.find(method => method.type === 'pin') ||
             enabledMethods.find(method => method.type === 'pattern') ||
             enabledMethods.find(method => method.type === 'password') ||
             enabledMethods[0];
    } else {
      // On desktop, prefer password
      return enabledMethods.find(method => method.type === 'password') ||
             enabledMethods[0];
    }
  }
}

export default new FallbackAuthService();
