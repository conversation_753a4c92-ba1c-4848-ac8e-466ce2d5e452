/**
 * Performance Optimizer for Biometric Authentication
 * Optimizes speed, caching, and user experience
 */

export interface PerformanceMetrics {
  challengeGenerationTime: number;
  authenticationTime: number;
  networkLatency: number;
  renderTime: number;
  memoryUsage: number;
  cacheHitRate: number;
}

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
}

export interface OptimizationConfig {
  enableCaching: boolean;
  cacheSize: number;
  cacheTTL: number;
  enablePreloading: boolean;
  enableLazyLoading: boolean;
  enableCompression: boolean;
  maxRetries: number;
  timeoutMs: number;
}

class PerformanceOptimizer {
  private cache = new Map<string, CacheEntry<any>>();
  private metrics: PerformanceMetrics = {
    challengeGenerationTime: 0,
    authenticationTime: 0,
    networkLatency: 0,
    renderTime: 0,
    memoryUsage: 0,
    cacheHitRate: 0
  };
  
  private config: OptimizationConfig = {
    enableCaching: true,
    cacheSize: 100,
    cacheTTL: 5 * 60 * 1000, // 5 minutes
    enablePreloading: true,
    enableLazyLoading: true,
    enableCompression: true,
    maxRetries: 3,
    timeoutMs: 30000
  };

  private performanceObserver?: PerformanceObserver;
  private cleanupInterval: number = 60000; // 1 minute

  constructor(config?: Partial<OptimizationConfig>) {
    this.config = { ...this.config, ...config };
    this.initializePerformanceMonitoring();
    this.startCacheCleanup();
  }

  /**
   * Initialize performance monitoring
   */
  private initializePerformanceMonitoring(): void {
    if ('PerformanceObserver' in window) {
      this.performanceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.name.includes('biometric')) {
            this.updateMetrics(entry);
          }
        });
      });

      this.performanceObserver.observe({ 
        entryTypes: ['measure', 'navigation', 'resource'] 
      });
    }
  }

  /**
   * Update performance metrics
   */
  private updateMetrics(entry: PerformanceEntry): void {
    if (entry.name.includes('challenge')) {
      this.metrics.challengeGenerationTime = entry.duration;
    } else if (entry.name.includes('auth')) {
      this.metrics.authenticationTime = entry.duration;
    } else if (entry.name.includes('network')) {
      this.metrics.networkLatency = entry.duration;
    } else if (entry.name.includes('render')) {
      this.metrics.renderTime = entry.duration;
    }
  }

  /**
   * Measure function execution time
   */
  async measureAsync<T>(
    name: string, 
    fn: () => Promise<T>
  ): Promise<T> {
    const startTime = performance.now();
    performance.mark(`${name}-start`);
    
    try {
      const result = await fn();
      const endTime = performance.now();
      
      performance.mark(`${name}-end`);
      performance.measure(name, `${name}-start`, `${name}-end`);
      
      console.log(`⚡ ${name}: ${(endTime - startTime).toFixed(2)}ms`);
      return result;
    } catch (error) {
      performance.mark(`${name}-error`);
      throw error;
    }
  }

  /**
   * Cache management
   */
  setCache<T>(key: string, data: T, ttl?: number): void {
    if (!this.config.enableCaching) return;

    // Clean cache if it's full
    if (this.cache.size >= this.config.cacheSize) {
      this.cleanOldestEntries();
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.config.cacheTTL,
      accessCount: 0
    });
  }

  getCache<T>(key: string): T | null {
    if (!this.config.enableCaching) return null;

    const entry = this.cache.get(key);
    if (!entry) return null;

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    // Update access count and timestamp
    entry.accessCount++;
    entry.timestamp = Date.now();
    
    // Update cache hit rate
    this.updateCacheHitRate(true);
    
    return entry.data;
  }

  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Preload critical resources
   */
  async preloadCriticalResources(): Promise<void> {
    if (!this.config.enablePreloading) return;

    const criticalResources = [
      '/api/biometric/capabilities',
      '/api/fallback/methods'
    ];

    const preloadPromises = criticalResources.map(async (url) => {
      try {
        const response = await fetch(url, {
          method: 'HEAD',
          credentials: 'include'
        });
        
        if (response.ok) {
          console.log(`✅ Preloaded: ${url}`);
        }
      } catch (error) {
        console.warn(`⚠️ Failed to preload: ${url}`, error);
      }
    });

    await Promise.allSettled(preloadPromises);
  }

  /**
   * Optimize network requests
   */
  async optimizedFetch(
    url: string, 
    options: RequestInit = {}
  ): Promise<Response> {
    const cacheKey = `fetch-${url}-${JSON.stringify(options)}`;
    
    // Check cache first
    const cached = this.getCache<Response>(cacheKey);
    if (cached && options.method === 'GET') {
      return cached.clone();
    }

    // Add compression headers
    const optimizedOptions: RequestInit = {
      ...options,
      headers: {
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'max-age=300',
        ...options.headers
      }
    };

    // Add timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      controller.abort();
    }, this.config.timeoutMs);

    optimizedOptions.signal = controller.signal;

    try {
      const response = await this.measureAsync(
        `network-${url}`,
        () => fetch(url, optimizedOptions)
      );

      clearTimeout(timeoutId);

      // Cache successful GET requests
      if (response.ok && options.method === 'GET') {
        this.setCache(cacheKey, response.clone());
      }

      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  /**
   * Optimize component rendering
   */
  optimizeRender<T>(
    componentName: string,
    renderFn: () => T
  ): T {
    return this.measureSync(`render-${componentName}`, renderFn);
  }

  /**
   * Measure synchronous function execution
   */
  measureSync<T>(name: string, fn: () => T): T {
    const startTime = performance.now();
    const result = fn();
    const endTime = performance.now();
    
    console.log(`⚡ ${name}: ${(endTime - startTime).toFixed(2)}ms`);
    return result;
  }

  /**
   * Debounce function calls
   */
  debounce<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let timeoutId: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(...args), delay);
    };
  }

  /**
   * Throttle function calls
   */
  throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  /**
   * Lazy load components
   */
  async lazyLoad<T>(
    importFn: () => Promise<{ default: T }>
  ): Promise<T> {
    if (!this.config.enableLazyLoading) {
      const module = await importFn();
      return module.default;
    }

    return this.measureAsync('lazy-load', async () => {
      const module = await importFn();
      return module.default;
    });
  }

  /**
   * Optimize images and assets
   */
  optimizeImageLoading(img: HTMLImageElement): void {
    // Add loading optimization
    img.loading = 'lazy';
    img.decoding = 'async';
    
    // Add intersection observer for better lazy loading
    if ('IntersectionObserver' in window) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const image = entry.target as HTMLImageElement;
            if (image.dataset.src) {
              image.src = image.dataset.src;
              image.removeAttribute('data-src');
              observer.unobserve(image);
            }
          }
        });
      });
      
      observer.observe(img);
    }
  }

  /**
   * Memory usage monitoring
   */
  getMemoryUsage(): number {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      this.metrics.memoryUsage = memory.usedJSHeapSize / memory.totalJSHeapSize;
      return this.metrics.memoryUsage;
    }
    return 0;
  }

  /**
   * Clean up old cache entries
   */
  private cleanOldestEntries(): void {
    const entries = Array.from(this.cache.entries());
    
    // Sort by timestamp (oldest first)
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
    
    // Remove oldest 25% of entries
    const toRemove = Math.floor(entries.length * 0.25);
    for (let i = 0; i < toRemove; i++) {
      this.cache.delete(entries[i][0]);
    }
  }

  /**
   * Start periodic cache cleanup
   */
  private startCacheCleanup(): void {
    setInterval(() => {
      const now = Date.now();
      const entriesToDelete: string[] = [];

      // Collect entries to delete
      this.cache.forEach((entry, key) => {
        if (now - entry.timestamp > entry.ttl) {
          entriesToDelete.push(key);
        }
      });

      // Delete expired entries
      entriesToDelete.forEach(key => {
        this.cache.delete(key);
      });

      // Log cleanup if entries were removed
      if (entriesToDelete.length > 0) {
        console.log(`Cache cleanup: removed ${entriesToDelete.length} expired entries`);
      }
    }, this.cleanupInterval);
  }

  /**
   * Alternative cache cleanup implementation using Array.from
   */
  private startCacheCleanupAlternative(): void {
    setInterval(() => {
      const now = Date.now();
      const entries = Array.from(this.cache.entries());

      for (const [key, entry] of entries) {
        if (now - entry.timestamp > entry.ttl) {
          this.cache.delete(key);
        }
      }
    }, 60000); // Clean every minute
  }

  /**
   * Update cache hit rate
   */
  private updateCacheHitRate(hit: boolean): void {
    // Simple moving average
    this.metrics.cacheHitRate = (this.metrics.cacheHitRate * 0.9) + (hit ? 0.1 : 0);
  }

  /**
   * Get performance metrics
   */
  getMetrics(): PerformanceMetrics {
    this.metrics.memoryUsage = this.getMemoryUsage();
    return { ...this.metrics };
  }

  /**
   * Generate performance report
   */
  generateReport(): string {
    const metrics = this.getMetrics();
    
    return `
🚀 Performance Report
━━━━━━━━━━━━━━━━━━━━
⏱️  Challenge Generation: ${metrics.challengeGenerationTime.toFixed(2)}ms
🔐 Authentication Time: ${metrics.authenticationTime.toFixed(2)}ms
🌐 Network Latency: ${metrics.networkLatency.toFixed(2)}ms
🎨 Render Time: ${metrics.renderTime.toFixed(2)}ms
💾 Memory Usage: ${(metrics.memoryUsage * 100).toFixed(1)}%
📊 Cache Hit Rate: ${(metrics.cacheHitRate * 100).toFixed(1)}%
🗂️  Cache Size: ${this.cache.size} entries
━━━━━━━━━━━━━━━━━━━━
    `.trim();
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
    }
    this.clearCache();
  }
}

// Export singleton instance
export const performanceOptimizer = new PerformanceOptimizer();
export default performanceOptimizer;
