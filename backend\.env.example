NODE_ENV=development
PORT=3001
DB_HOST=localhost
DB_PORT=5432
DB_NAME=esign_arabic
DB_USER=postgres
DB_PASSWORD=password

# Security Configuration - CRITICAL: Change these in production!
JWT_SECRET=generate-strong-random-secret-minimum-32-characters-change-in-production
JWT_REFRESH_SECRET=different-strong-secret-for-refresh-tokens-change-in-production
ENCRYPTION_KEY=exactly-32-character-encryption-key

# CORS and Security Settings
FRONTEND_URL=http://localhost:3000
ALLOWED_ORIGINS=http://localhost:3000,https://localhost:3000
FORCE_HTTPS=false
SECURE_COOKIES=false

# Production Security Settings (set to true in production)
ENABLE_HSTS=false
CSP_REPORT_ONLY=false

# Arabic-only system configuration
DEFAULT_LANGUAGE=ar
DEFAULT_TEXT_DIRECTION=rtl
SYSTEM_LOCALE=ar_SA
FONT_FAMILY=Almarai

# WhatsApp Notification Configuration
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_WHATSAPP_FROM=whatsapp:+***********
WHATSAPP_NOTIFICATIONS_ENABLED=true
WHATSAPP_ADMIN_NUMBERS=+************,+************
WHATSAPP_RETRY_ATTEMPTS=3
WHATSAPP_RETRY_DELAY=5000
