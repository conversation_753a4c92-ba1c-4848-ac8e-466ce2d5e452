import React, { useState, useEffect } from 'react';
import {
  ExclamationTriangleIcon,
  LockClosedIcon,
  ClockIcon,
  ShieldCheckIcon,
  EyeIcon
} from '@heroicons/react/24/outline';

interface SecurityAlertProps {
  type: 'lockout' | 'attempts' | 'warning' | 'success' | 'error';
  message: string;
  attemptsRemaining?: number;
  lockedUntil?: string;
  onDismiss?: () => void;
  className?: string;
}

const SecurityAlert: React.FC<SecurityAlertProps> = ({
  type,
  message,
  attemptsRemaining,
  lockedUntil,
  onDismiss,
  className = ''
}) => {
  const [timeLeft, setTimeLeft] = useState<string>('');
  const [isVisible, setIsVisible] = useState(true);

  // Calculate time remaining for lockout
  useEffect(() => {
    if (type === 'lockout' && lockedUntil) {
      const updateTimeLeft = () => {
        const now = new Date().getTime();
        const lockoutEnd = new Date(lockedUntil).getTime();
        const difference = lockoutEnd - now;

        if (difference > 0) {
          const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
          const seconds = Math.floor((difference % (1000 * 60)) / 1000);
          setTimeLeft(`${minutes}:${seconds.toString().padStart(2, '0')}`);
        } else {
          setTimeLeft('');
          setIsVisible(false);
        }
      };

      updateTimeLeft();
      const interval = setInterval(updateTimeLeft, 1000);

      return () => clearInterval(interval);
    }
  }, [type, lockedUntil]);

  const getAlertConfig = () => {
    switch (type) {
      case 'lockout':
        return {
          icon: <LockClosedIcon className="w-5 h-5" />,
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          textColor: 'text-red-800',
          iconColor: 'text-red-500',
          title: 'تم قفل الحساب'
        };
      case 'attempts':
        return {
          icon: <ExclamationTriangleIcon className="w-5 h-5" />,
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          textColor: 'text-yellow-800',
          iconColor: 'text-yellow-500',
          title: 'تحذير أمني'
        };
      case 'warning':
        return {
          icon: <ShieldCheckIcon className="w-5 h-5" />,
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
          textColor: 'text-orange-800',
          iconColor: 'text-orange-500',
          title: 'تنبيه أمني'
        };
      case 'success':
        return {
          icon: <ShieldCheckIcon className="w-5 h-5" />,
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          textColor: 'text-green-800',
          iconColor: 'text-green-500',
          title: 'نجح الأمان'
        };
      default:
        return {
          icon: <ExclamationTriangleIcon className="w-5 h-5" />,
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          textColor: 'text-red-800',
          iconColor: 'text-red-500',
          title: 'خطأ'
        };
    }
  };

  const config = getAlertConfig();

  if (!isVisible) return null;

  return (
    <div className={`${config.bgColor} ${config.borderColor} border rounded-lg p-4 mb-4 ${className}`} dir="rtl">
      <div className="flex items-start">
        <div className={`${config.iconColor} ml-3 mt-0.5`}>
          {config.icon}
        </div>
        <div className="flex-1">
          <h3 className={`text-sm font-semibold ${config.textColor} mb-1`}>
            {config.title}
          </h3>
          <p className={`text-sm ${config.textColor} mb-2`}>
            {message}
          </p>

          {/* Account lockout specific information */}
          {type === 'lockout' && timeLeft && (
            <div className={`${config.bgColor} border ${config.borderColor} rounded p-3 mb-3`}>
              <div className="flex items-center mb-2">
                <ClockIcon className={`w-4 h-4 ${config.iconColor} ml-2`} />
                <span className={`text-sm font-medium ${config.textColor}`}>
                  الوقت المتبقي للإلغاء القفل:
                </span>
              </div>
              <div className={`text-lg font-mono font-bold ${config.textColor} text-center`}>
                {timeLeft}
              </div>
            </div>
          )}

          {/* Attempts remaining information */}
          {type === 'attempts' && typeof attemptsRemaining === 'number' && (
            <div className="flex items-center mb-2">
              <EyeIcon className={`w-4 h-4 ${config.iconColor} ml-2`} />
              <span className={`text-sm ${config.textColor}`}>
                المحاولات المتبقية: <strong>{attemptsRemaining}</strong>
              </span>
            </div>
          )}

          {/* Security tips */}
          {(type === 'lockout' || type === 'attempts') && (
            <div className={`mt-3 p-3 ${config.bgColor} border ${config.borderColor} rounded`}>
              <h4 className={`text-sm font-medium ${config.textColor} mb-2`}>
                نصائح أمنية:
              </h4>
              <ul className={`text-xs ${config.textColor} space-y-1`}>
                <li>• تأكد من صحة كلمة المرور</li>
                <li>• تحقق من أن Caps Lock غير مفعل</li>
                <li>• استخدم المصادقة البيومترية إذا كانت متاحة</li>
                {type === 'lockout' && (
                  <li>• انتظر انتهاء فترة القفل قبل المحاولة مرة أخرى</li>
                )}
              </ul>
            </div>
          )}

          {/* Action buttons */}
          <div className="flex justify-end mt-3 space-x-2 space-x-reverse">
            {onDismiss && (
              <button
                onClick={onDismiss}
                className={`text-xs px-3 py-1 rounded ${config.textColor} hover:bg-opacity-20 hover:bg-current transition-colors`}
              >
                إغلاق
              </button>
            )}
            {type === 'lockout' && (
              <button
                onClick={() => window.location.reload()}
                className={`text-xs px-3 py-1 rounded ${config.textColor} hover:bg-opacity-20 hover:bg-current transition-colors`}
                disabled={!!timeLeft}
              >
                {timeLeft ? 'انتظر...' : 'إعادة المحاولة'}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SecurityAlert;
