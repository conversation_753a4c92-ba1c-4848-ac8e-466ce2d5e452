const { query } = require('../models/database');
const geolocationService = require('../services/geolocationService');
const { v4: uuidv4 } = require('uuid');

/**
 * Location Tracking Controller
 * Handles user location tracking, session management, and admin monitoring
 */

/**
 * Update user location (for real-time tracking)
 */
const updateUserLocation = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { 
      latitude, 
      longitude, 
      accuracy, 
      altitude, 
      heading, 
      speed, 
      source = 'gps',
      sessionToken 
    } = req.body;

    // Validate required fields
    if (!latitude || !longitude) {
      return res.status(400).json({
        success: false,
        message: 'خطأ: الإحداثيات مطلوبة'
      });
    }

    // Get or create session
    let session;
    if (sessionToken) {
      const sessionResult = await query(
        'SELECT * FROM user_sessions WHERE session_token = $1 AND user_id = $2',
        [sessionToken, userId]
      );
      session = sessionResult.rows[0];
    }

    if (!session) {
      // Create new session
      const newSessionToken = uuidv4();
      const ipAddress = req.ip || req.connection.remoteAddress;
      const userAgent = req.get('User-Agent') || '';
      
      // Get location details from IP if GPS location is not available
      let locationData = null;
      if (source === 'ip') {
        locationData = await geolocationService.getLocationFromIP(ipAddress);
      }

      const sessionResult = await query(
        `INSERT INTO user_sessions (
          user_id, session_token, status, current_latitude, current_longitude,
          current_accuracy, location_source, location_consent_given,
          ip_address, user_agent, country_code, country_name, region_name, city_name
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14) RETURNING *`,
        [
          userId, newSessionToken, 'active', latitude, longitude, accuracy,
          source, true, ipAddress, userAgent,
          locationData?.country_code, locationData?.country_name,
          locationData?.region, locationData?.city
        ]
      );
      session = sessionResult.rows[0];
    } else {
      // Update existing session
      await query(
        `UPDATE user_sessions SET 
          current_latitude = $1, current_longitude = $2, current_accuracy = $3,
          location_source = $4, location_last_updated = NOW(), last_activity = NOW(),
          status = 'active', updated_at = NOW()
        WHERE id = $5`,
        [latitude, longitude, accuracy, source, session.id]
      );
    }

    // Insert location update record
    await query(
      `INSERT INTO user_location_updates (
        user_id, session_id, latitude, longitude, accuracy, altitude,
        heading, speed, source, confidence_level
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
      [userId, session.id, latitude, longitude, accuracy, altitude, heading, speed, source, 95]
    );

    // Log activity
    await query(
      `INSERT INTO user_activity_log (
        user_id, session_id, activity_type, activity_description,
        latitude, longitude, ip_address, user_agent
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
      [
        userId, session.id, 'location_update', 
        `Location updated via ${source}`,
        latitude, longitude, req.ip, req.get('User-Agent')
      ]
    );

    res.json({
      success: true,
      message: 'تم تحديث الموقع بنجاح',
      data: {
        sessionToken: session.session_token,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error updating user location:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تحديث الموقع',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get current user location and session info
 */
const getCurrentLocation = async (req, res) => {
  try {
    const userId = req.user.userId;

    const result = await query(
      `SELECT 
        us.current_latitude, us.current_longitude, us.current_accuracy,
        us.location_source, us.location_last_updated, us.status,
        us.country_name, us.city_name, us.session_token
      FROM user_sessions us
      WHERE us.user_id = $1 AND us.status IN ('active', 'idle')
      ORDER BY us.last_activity DESC
      LIMIT 1`,
      [userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'لا توجد جلسة نشطة'
      });
    }

    const session = result.rows[0];
    res.json({
      success: true,
      message: 'تم جلب الموقع الحالي بنجاح',
      data: {
        latitude: session.current_latitude,
        longitude: session.current_longitude,
        accuracy: session.current_accuracy,
        source: session.location_source,
        lastUpdated: session.location_last_updated,
        status: session.status,
        country: session.country_name,
        city: session.city_name,
        sessionToken: session.session_token
      }
    });

  } catch (error) {
    console.error('Error getting current location:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب الموقع الحالي',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Update location consent
 */
const updateLocationConsent = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { 
      consentGiven, 
      consentType = 'browser_geolocation',
      dataRetentionDays = 90,
      allowAdminMonitoring = true,
      allowLocationHistory = true
    } = req.body;

    // Insert or update consent record
    await query(
      `INSERT INTO user_location_consent (
        user_id, consent_given, consent_type, data_retention_days,
        allow_admin_monitoring, allow_location_history,
        consent_ip_address, consent_user_agent, privacy_policy_version
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      ON CONFLICT (user_id, consent_type) 
      DO UPDATE SET 
        consent_given = $2,
        data_retention_days = $4,
        allow_admin_monitoring = $5,
        allow_location_history = $6,
        consent_date = CASE WHEN $2 = true THEN NOW() ELSE user_location_consent.consent_date END,
        consent_withdrawn_date = CASE WHEN $2 = false THEN NOW() ELSE NULL END,
        updated_at = NOW()`,
      [
        userId, consentGiven, consentType, dataRetentionDays,
        allowAdminMonitoring, allowLocationHistory,
        req.ip, req.get('User-Agent'), '1.0'
      ]
    );

    // Update current session consent status
    await query(
      'UPDATE user_sessions SET location_consent_given = $1 WHERE user_id = $2 AND status IN (\'active\', \'idle\')',
      [consentGiven, userId]
    );

    // Log activity
    await query(
      `INSERT INTO user_activity_log (
        user_id, activity_type, activity_description, ip_address, user_agent
      ) VALUES ($1, $2, $3, $4, $5)`,
      [
        userId, 'settings_change',
        `Location consent ${consentGiven ? 'granted' : 'withdrawn'} for ${consentType}`,
        req.ip, req.get('User-Agent')
      ]
    );

    res.json({
      success: true,
      message: consentGiven ? 'تم منح الموافقة على تتبع الموقع' : 'تم سحب الموافقة على تتبع الموقع',
      data: {
        consentGiven,
        consentType,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error updating location consent:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تحديث موافقة الموقع',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get user's location consent status
 */
const getLocationConsent = async (req, res) => {
  try {
    const userId = req.user.userId;

    const result = await query(
      `SELECT consent_given, consent_type, consent_date, data_retention_days,
              allow_admin_monitoring, allow_location_history
       FROM user_location_consent 
       WHERE user_id = $1 
       ORDER BY consent_date DESC`,
      [userId]
    );

    res.json({
      success: true,
      message: 'تم جلب حالة الموافقة بنجاح',
      data: result.rows
    });

  } catch (error) {
    console.error('Error getting location consent:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب حالة الموافقة',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  updateUserLocation,
  getCurrentLocation,
  updateLocationConsent,
  getLocationConsent
};
