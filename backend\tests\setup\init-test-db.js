const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config();

// Database connection for initialization
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: 'postgres', // Connect to default postgres database first
  password: process.env.DB_PASSWORD || 'password',
  port: process.env.DB_PORT || 5432,
});

async function initializeTestDatabase() {
  const client = await pool.connect();
  
  try {
    console.log('Initializing test database...');
    
    const testDbName = 'esign_test';
    
    // Drop test database if it exists (for clean slate)
    console.log(`Dropping existing test database: ${testDbName}`);
    await client.query(`DROP DATABASE IF EXISTS ${testDbName}`);
    
    // Create test database
    console.log(`Creating test database: ${testDbName}`);
    await client.query(`CREATE DATABASE ${testDbName}`);
    
    // Release the client
    client.release();
    
    // Connect to the test database
    const testPool = new Pool({
      user: process.env.DB_USER || 'postgres',
      host: process.env.DB_HOST || 'localhost',
      database: testDbName,
      password: process.env.DB_PASSWORD || 'password',
      port: process.env.DB_PORT || 5432,
    });
    
    const testClient = await testPool.connect();
    
    // Run initial schema migration
    console.log('Running initial schema migration...');
    const initialSchemaSQL = fs.readFileSync(
      path.join(__dirname, '..', '..', '..', 'database', 'migrations', '001_initial_schema.sql'),
      'utf8'
    );
    
    await testClient.query(initialSchemaSQL);
    
    // Run Arabic-only conversion migration
    console.log('Running Arabic-only conversion migration...');
    const arabicOnlySQL = fs.readFileSync(
      path.join(__dirname, '..', '..', '..', 'database', 'migrations', '002_arabic_only_conversion.sql'),
      'utf8'
    );
    
    await testClient.query(arabicOnlySQL);
    
    // Run user roles migration
    console.log('Running user roles migration...');
    const userRolesSQL = fs.readFileSync(
      path.join(__dirname, '..', '..', '..', 'database', 'migrations', '003_add_user_roles.sql'),
      'utf8'
    );
    
    await testClient.query(userRolesSQL);
    
    // Insert test data for Arabic templates
    console.log('Inserting Arabic test data...');
    await testClient.query(`
      INSERT INTO arabic_templates (template_type, arabic_text, english_text) VALUES
      ('document_signed', 'تم توقيع المستند بنجاح', 'Document signed successfully'),
      ('signature_uploaded', 'تم رفع التوقيع بنجاح', 'Signature uploaded successfully'),
      ('invalid_credentials', 'البريد الإلكتروني أو كلمة المرور غير صحيحة', 'Invalid email or password'),
      ('email_required', 'البريد الإلكتروني وكلمة المرور مطلوبان', 'Email and password are required'),
      ('invalid_email', 'تنسيق البريد الإلكتروني غير صالح', 'Invalid email format'),
      ('email_exists', 'البريد الإلكتروني مسجل بالفعل', 'Email already registered'),
      ('account_created', 'تم إنشاء الحساب بنجاح', 'Account created successfully'),
      ('login_success', 'تم تسجيل الدخول بنجاح', 'Login successful'),
      ('signature_required', 'معرف التوقيع مطلوب', 'Signature ID required'),
      ('invalid_pdf', 'ملف PDF غير صحيح', 'Invalid PDF file'),
      ('file_type_unsupported', 'نوع الملف غير مدعوم', 'File type not supported'),
      ('unauthorized', 'غير مخول', 'Unauthorized')
      ON CONFLICT (template_type) DO NOTHING;
    `);
    
    console.log('Test database initialization completed successfully!');
    
    testClient.release();
    await testPool.end();
    
  } catch (error) {
    console.error('Test database initialization error:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

// Mock database functions for tests that don't need real database
const mockDatabase = {
  query: jest.fn().mockImplementation((sql, params) => {
    // Mock user creation
    if (sql.includes('INSERT INTO users')) {
      return Promise.resolve({
        rows: [{
          id: 'test-user-id-' + Date.now(),
          email: params[0],
          password_hash: params[1],
          role: params[2] || 'user',
          language: params[3] || 'ar',
          text_direction: params[4] || 'rtl',
          permissions: params[5] || JSON.stringify(['read', 'write', 'arabic_support'])
        }]
      });
    }

    // Mock user selection
    if (sql.includes('SELECT') && sql.includes('users')) {
      return Promise.resolve({
        rows: [{
          id: 'test-user-id',
          email: '<EMAIL>',
          language: 'ar',
          text_direction: 'rtl',
          permissions: JSON.stringify(['read', 'write', 'arabic_support'])
        }]
      });
    }

    // Mock arabic templates
    if (sql.includes('arabic_templates')) {
      return Promise.resolve({
        rows: [{
          template_type: 'document_signed',
          arabic_text: 'تم توقيع المستند بنجاح',
          english_text: 'Document signed successfully'
        }]
      });
    }

    // Mock delete operations
    if (sql.includes('DELETE')) {
      return Promise.resolve({ rows: [], rowCount: 1 });
    }

    // Default mock response
    return Promise.resolve({ rows: [] });
  }),
  connect: jest.fn().mockResolvedValue({
    query: jest.fn().mockResolvedValue({ rows: [] }),
    release: jest.fn()
  }),
  end: jest.fn().mockResolvedValue()
};

// Export both real and mock functions
module.exports = { 
  initializeTestDatabase,
  mockDatabase
};

// Run initialization if this file is executed directly
if (require.main === module) {
  initializeTestDatabase()
    .then(() => {
      console.log('Test database initialization completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Test database initialization failed:', error);
      process.exit(1);
    });
}
