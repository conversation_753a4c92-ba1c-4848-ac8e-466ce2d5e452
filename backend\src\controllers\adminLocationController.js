const { query } = require('../models/database');

/**
 * Admin Location Monitoring Controller
 * Handles admin access to user location data and monitoring
 */

/**
 * Get all active user sessions with locations
 */
const getActiveUserSessions = async (req, res) => {
  try {
    const adminUserId = req.user.userId;
    const { 
      status = 'all', // 'active', 'idle', 'offline', 'all'
      search = '',
      limit = 50,
      offset = 0
    } = req.query;

    // Log admin access
    await query(
      `INSERT INTO admin_location_access_log (
        admin_user_id, access_type, access_description, data_type, ip_address, user_agent
      ) VALUES ($1, $2, $3, $4, $5, $6)`,
      [
        adminUserId, 'view_location', 'Viewed active user sessions',
        'session_data', req.ip, req.get('User-Agent')
      ]
    );

    let statusFilter = '';
    let params = [limit, offset];
    let paramIndex = 3;

    if (status !== 'all') {
      statusFilter = `AND us.status = $${paramIndex}`;
      params.push(status);
      paramIndex++;
    }

    if (search) {
      statusFilter += ` AND (u.email ILIKE $${paramIndex} OR u.full_name ILIKE $${paramIndex})`;
      params.push(`%${search}%`);
    }

    const result = await query(
      `SELECT 
        us.id as session_id,
        us.user_id,
        u.email,
        u.full_name,
        u.role,
        us.status,
        us.last_activity,
        us.login_time,
        us.current_latitude,
        us.current_longitude,
        us.current_accuracy,
        us.location_source,
        us.location_consent_given,
        us.location_last_updated,
        us.ip_address,
        us.device_type,
        us.browser_name,
        us.country_name,
        us.city_name,
        us.timezone,
        EXTRACT(EPOCH FROM (NOW() - us.last_activity)) as seconds_since_activity,
        COUNT(ulu.id) as location_updates_count
      FROM user_sessions us
      JOIN users u ON us.user_id = u.id
      LEFT JOIN user_location_updates ulu ON us.id = ulu.session_id
      WHERE 1=1 ${statusFilter}
      GROUP BY us.id, u.id, u.email, u.full_name, u.role
      ORDER BY us.last_activity DESC
      LIMIT $1 OFFSET $2`,
      params
    );

    // Get total count
    const countResult = await query(
      `SELECT COUNT(DISTINCT us.id) as total
      FROM user_sessions us
      JOIN users u ON us.user_id = u.id
      WHERE 1=1 ${statusFilter.replace(/\$\d+/g, (match) => {
        const num = parseInt(match.substring(1));
        return num <= 2 ? match : `$${num - 2}`;
      })}`,
      params.slice(2)
    );

    res.json({
      success: true,
      message: 'تم جلب جلسات المستخدمين بنجاح',
      data: {
        sessions: result.rows,
        pagination: {
          total: parseInt(countResult.rows[0].total),
          limit: parseInt(limit),
          offset: parseInt(offset),
          hasMore: parseInt(offset) + parseInt(limit) < parseInt(countResult.rows[0].total)
        }
      }
    });

  } catch (error) {
    console.error('Error getting active user sessions:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب جلسات المستخدمين',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get user location history
 */
const getUserLocationHistory = async (req, res) => {
  try {
    const adminUserId = req.user.userId;
    const { userId } = req.params;
    const { 
      startDate,
      endDate,
      limit = 100,
      offset = 0
    } = req.query;

    // Validate user exists
    const userCheck = await query('SELECT id, email FROM users WHERE id = $1', [userId]);
    if (userCheck.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    // Log admin access
    await query(
      `INSERT INTO admin_location_access_log (
        admin_user_id, accessed_user_id, access_type, access_description, 
        data_type, date_range_start, date_range_end, ip_address, user_agent
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
      [
        adminUserId, userId, 'view_timeline', 'Viewed user location history',
        'location_history', startDate || null, endDate || null, req.ip, req.get('User-Agent')
      ]
    );

    let dateFilter = '';
    let params = [userId, limit, offset];
    let paramIndex = 4;

    if (startDate) {
      dateFilter += ` AND ulu.timestamp >= $${paramIndex}`;
      params.push(startDate);
      paramIndex++;
    }

    if (endDate) {
      dateFilter += ` AND ulu.timestamp <= $${paramIndex}`;
      params.push(endDate);
    }

    const result = await query(
      `SELECT 
        ulu.id,
        ulu.latitude,
        ulu.longitude,
        ulu.accuracy,
        ulu.altitude,
        ulu.heading,
        ulu.speed,
        ulu.source,
        ulu.timestamp,
        ulu.country_name,
        ulu.city_name,
        ulu.address,
        ulu.confidence_level,
        us.session_token,
        us.device_type,
        us.browser_name
      FROM user_location_updates ulu
      LEFT JOIN user_sessions us ON ulu.session_id = us.id
      WHERE ulu.user_id = $1 ${dateFilter}
      ORDER BY ulu.timestamp DESC
      LIMIT $2 OFFSET $3`,
      params
    );

    // Get total count
    const countParams = params.slice(0, -2); // Remove limit and offset
    const countResult = await query(
      `SELECT COUNT(*) as total
      FROM user_location_updates ulu
      WHERE ulu.user_id = $1 ${dateFilter}`,
      countParams
    );

    res.json({
      success: true,
      message: 'تم جلب تاريخ المواقع بنجاح',
      data: {
        user: userCheck.rows[0],
        locations: result.rows,
        pagination: {
          total: parseInt(countResult.rows[0].total),
          limit: parseInt(limit),
          offset: parseInt(offset),
          hasMore: parseInt(offset) + parseInt(limit) < parseInt(countResult.rows[0].total)
        }
      }
    });

  } catch (error) {
    console.error('Error getting user location history:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب تاريخ المواقع',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get user activity timeline
 */
const getUserActivityTimeline = async (req, res) => {
  try {
    const adminUserId = req.user.userId;
    const { userId } = req.params;
    const { 
      startDate,
      endDate,
      activityType = 'all',
      limit = 100,
      offset = 0
    } = req.query;

    // Validate user exists
    const userCheck = await query('SELECT id, email FROM users WHERE id = $1', [userId]);
    if (userCheck.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    // Log admin access
    await query(
      `INSERT INTO admin_location_access_log (
        admin_user_id, accessed_user_id, access_type, access_description, 
        data_type, date_range_start, date_range_end, ip_address, user_agent
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
      [
        adminUserId, userId, 'view_activity', 'Viewed user activity timeline',
        'activity_timeline', startDate || null, endDate || null, req.ip, req.get('User-Agent')
      ]
    );

    let filters = '';
    let params = [userId, limit, offset];
    let paramIndex = 4;

    if (startDate) {
      filters += ` AND ual.timestamp >= $${paramIndex}`;
      params.push(startDate);
      paramIndex++;
    }

    if (endDate) {
      filters += ` AND ual.timestamp <= $${paramIndex}`;
      params.push(endDate);
      paramIndex++;
    }

    if (activityType !== 'all') {
      filters += ` AND ual.activity_type = $${paramIndex}`;
      params.push(activityType);
    }

    const result = await query(
      `SELECT 
        ual.id,
        ual.activity_type,
        ual.activity_description,
        ual.activity_data,
        ual.latitude,
        ual.longitude,
        ual.location_accuracy,
        ual.ip_address,
        ual.page_url,
        ual.timestamp,
        ual.duration_seconds,
        us.device_type,
        us.browser_name
      FROM user_activity_log ual
      LEFT JOIN user_sessions us ON ual.session_id = us.id
      WHERE ual.user_id = $1 ${filters}
      ORDER BY ual.timestamp DESC
      LIMIT $2 OFFSET $3`,
      params
    );

    // Get total count
    const countParams = params.slice(0, -2); // Remove limit and offset
    const countResult = await query(
      `SELECT COUNT(*) as total
      FROM user_activity_log ual
      WHERE ual.user_id = $1 ${filters}`,
      countParams
    );

    res.json({
      success: true,
      message: 'تم جلب سجل الأنشطة بنجاح',
      data: {
        user: userCheck.rows[0],
        activities: result.rows,
        pagination: {
          total: parseInt(countResult.rows[0].total),
          limit: parseInt(limit),
          offset: parseInt(offset),
          hasMore: parseInt(offset) + parseInt(limit) < parseInt(countResult.rows[0].total)
        }
      }
    });

  } catch (error) {
    console.error('Error getting user activity timeline:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب سجل الأنشطة',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Export user location data
 */
const exportLocationData = async (req, res) => {
  try {
    const adminUserId = req.user.userId;
    const {
      format = 'csv',
      status = 'all',
      search = '',
      startDate,
      endDate,
      includeLocationHistory = false
    } = req.query;

    // Log admin access
    await query(
      `INSERT INTO admin_location_access_log (
        admin_user_id, access_type, access_description, data_type,
        date_range_start, date_range_end, ip_address, user_agent
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
      [
        adminUserId, 'export_data', 'Exported location data',
        'location_export', startDate || null, endDate || null, req.ip, req.get('User-Agent')
      ]
    );

    let statusFilter = '';
    let params = [];
    let paramIndex = 1;

    if (status !== 'all') {
      statusFilter = `AND us.status = $${paramIndex}`;
      params.push(status);
      paramIndex++;
    }

    if (search) {
      statusFilter += ` AND (u.email ILIKE $${paramIndex} OR u.full_name ILIKE $${paramIndex})`;
      params.push(`%${search}%`);
      paramIndex++;
    }

    if (startDate) {
      statusFilter += ` AND us.created_at >= $${paramIndex}`;
      params.push(startDate);
      paramIndex++;
    }

    if (endDate) {
      statusFilter += ` AND us.created_at <= $${paramIndex}`;
      params.push(endDate);
    }

    const result = await query(
      `SELECT
        u.email,
        u.full_name,
        us.status,
        us.last_activity,
        us.login_time,
        us.logout_time,
        us.current_latitude,
        us.current_longitude,
        us.current_accuracy,
        us.location_source,
        us.location_consent_given,
        us.location_last_updated,
        us.ip_address,
        us.device_type,
        us.browser_name,
        us.country_name,
        us.region_name,
        us.city_name,
        us.timezone,
        COUNT(ulu.id) as location_updates_count
      FROM user_sessions us
      JOIN users u ON us.user_id = u.id
      LEFT JOIN user_location_updates ulu ON us.id = ulu.session_id
      WHERE 1=1 ${statusFilter}
      GROUP BY us.id, u.id, u.email, u.full_name
      ORDER BY us.last_activity DESC`,
      params
    );

    if (format === 'csv') {
      const csvHeaders = [
        'البريد الإلكتروني',
        'الاسم الكامل',
        'الحالة',
        'آخر نشاط',
        'وقت تسجيل الدخول',
        'وقت تسجيل الخروج',
        'خط العرض',
        'خط الطول',
        'دقة الموقع',
        'مصدر الموقع',
        'موافقة الموقع',
        'آخر تحديث للموقع',
        'عنوان IP',
        'نوع الجهاز',
        'المتصفح',
        'الدولة',
        'المنطقة',
        'المدينة',
        'المنطقة الزمنية',
        'عدد تحديثات الموقع'
      ];

      const csvRows = result.rows.map(row => [
        row.email,
        row.full_name || '',
        row.status,
        row.last_activity,
        row.login_time,
        row.logout_time || '',
        row.current_latitude || '',
        row.current_longitude || '',
        row.current_accuracy || '',
        row.location_source || '',
        row.location_consent_given ? 'نعم' : 'لا',
        row.location_last_updated || '',
        row.ip_address || '',
        row.device_type || '',
        row.browser_name || '',
        row.country_name || '',
        row.region_name || '',
        row.city_name || '',
        row.timezone || '',
        row.location_updates_count
      ]);

      const csvContent = [csvHeaders, ...csvRows]
        .map(row => row.map(field => `"${field}"`).join(','))
        .join('\n');

      res.setHeader('Content-Type', 'text/csv; charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename="user-locations-${new Date().toISOString().split('T')[0]}.csv"`);
      res.send('\ufeff' + csvContent); // Add BOM for proper UTF-8 encoding
    } else {
      res.json({
        success: true,
        message: 'تم تصدير البيانات بنجاح',
        data: result.rows
      });
    }

  } catch (error) {
    console.error('Error exporting location data:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تصدير البيانات',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Export user activity timeline
 */
const exportUserActivityTimeline = async (req, res) => {
  try {
    const adminUserId = req.user.userId;
    const { userId } = req.params;
    const {
      format = 'csv',
      startDate,
      endDate,
      activityType = 'all'
    } = req.query;

    // Validate user exists
    const userCheck = await query('SELECT id, email FROM users WHERE id = $1', [userId]);
    if (userCheck.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    // Log admin access
    await query(
      `INSERT INTO admin_location_access_log (
        admin_user_id, accessed_user_id, access_type, access_description,
        data_type, date_range_start, date_range_end, ip_address, user_agent
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
      [
        adminUserId, userId, 'export_data', 'Exported user activity timeline',
        'activity_export', startDate || null, endDate || null, req.ip, req.get('User-Agent')
      ]
    );

    let filters = '';
    let params = [userId];
    let paramIndex = 2;

    if (startDate) {
      filters += ` AND ual.timestamp >= $${paramIndex}`;
      params.push(startDate);
      paramIndex++;
    }

    if (endDate) {
      filters += ` AND ual.timestamp <= $${paramIndex}`;
      params.push(endDate);
      paramIndex++;
    }

    if (activityType !== 'all') {
      filters += ` AND ual.activity_type = $${paramIndex}`;
      params.push(activityType);
    }

    const result = await query(
      `SELECT
        ual.activity_type,
        ual.activity_description,
        ual.latitude,
        ual.longitude,
        ual.location_accuracy,
        ual.ip_address,
        ual.page_url,
        ual.timestamp,
        ual.duration_seconds,
        us.device_type,
        us.browser_name
      FROM user_activity_log ual
      LEFT JOIN user_sessions us ON ual.session_id = us.id
      WHERE ual.user_id = $1 ${filters}
      ORDER BY ual.timestamp DESC`,
      params
    );

    if (format === 'csv') {
      const csvHeaders = [
        'نوع النشاط',
        'وصف النشاط',
        'خط العرض',
        'خط الطول',
        'دقة الموقع',
        'عنوان IP',
        'رابط الصفحة',
        'الوقت',
        'المدة (ثواني)',
        'نوع الجهاز',
        'المتصفح'
      ];

      const csvRows = result.rows.map(row => [
        row.activity_type,
        row.activity_description || '',
        row.latitude || '',
        row.longitude || '',
        row.location_accuracy || '',
        row.ip_address || '',
        row.page_url || '',
        row.timestamp,
        row.duration_seconds || '',
        row.device_type || '',
        row.browser_name || ''
      ]);

      const csvContent = [csvHeaders, ...csvRows]
        .map(row => row.map(field => `"${field}"`).join(','))
        .join('\n');

      res.setHeader('Content-Type', 'text/csv; charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename="user-activity-${userId}-${new Date().toISOString().split('T')[0]}.csv"`);
      res.send('\ufeff' + csvContent); // Add BOM for proper UTF-8 encoding
    } else {
      res.json({
        success: true,
        message: 'تم تصدير البيانات بنجاح',
        data: {
          user: userCheck.rows[0],
          activities: result.rows
        }
      });
    }

  } catch (error) {
    console.error('Error exporting user activity timeline:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تصدير البيانات',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getActiveUserSessions,
  getUserLocationHistory,
  getUserActivityTimeline,
  exportLocationData,
  exportUserActivityTimeline
};
