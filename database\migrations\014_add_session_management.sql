-- Migration: Add session management tables
-- Description: Add comprehensive session tracking and security management

-- Create user sessions table
CREATE TABLE IF NOT EXISTS user_sessions (
    session_id VARCHAR(64) PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    invalidated_at TIMESTAMP NULL,
    invalidation_reason VARCHAR(50) NULL,
    is_active BOOLEAN DEFAULT true,
    device_info JSONB DEFAULT '{}',
    login_count INTEGER DEFAULT 1,
    
    -- Constraints
    CONSTRAINT chk_session_dates CHECK (expires_at > created_at),
    CONSTRAINT chk_invalidation CHECK (
        (is_active = false AND invalidated_at IS NOT NULL) OR 
        (is_active = true AND invalidated_at IS NULL)
    )
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON user_sessions(is_active, last_activity);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires ON user_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_active ON user_sessions(user_id, is_active);

-- Create session activity log table
CREATE TABLE IF NOT EXISTS session_activities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id VARCHAR(64) REFERENCES user_sessions(session_id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    activity_type VARCHAR(50) NOT NULL,
    activity_details JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for session activities
CREATE INDEX IF NOT EXISTS idx_session_activities_session ON session_activities(session_id);
CREATE INDEX IF NOT EXISTS idx_session_activities_user ON session_activities(user_id);
CREATE INDEX IF NOT EXISTS idx_session_activities_type ON session_activities(activity_type);
CREATE INDEX IF NOT EXISTS idx_session_activities_created ON session_activities(created_at);

-- Create function to log session activity
CREATE OR REPLACE FUNCTION log_session_activity(
    p_session_id VARCHAR(64),
    p_user_id UUID,
    p_activity_type VARCHAR(50),
    p_activity_details JSONB DEFAULT '{}',
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    activity_id UUID;
BEGIN
    INSERT INTO session_activities (
        session_id, user_id, activity_type, activity_details, 
        ip_address, user_agent
    ) VALUES (
        p_session_id, p_user_id, p_activity_type, p_activity_details,
        p_ip_address, p_user_agent
    ) RETURNING id INTO activity_id;
    
    RETURN activity_id;
END;
$$ LANGUAGE plpgsql;

-- Create function to clean up old session activities
CREATE OR REPLACE FUNCTION cleanup_old_session_activities(
    p_days_to_keep INTEGER DEFAULT 90
)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM session_activities 
    WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '1 day' * p_days_to_keep;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    IF deleted_count > 0 THEN
        INSERT INTO logs (action, details) 
        VALUES ('SESSION_ACTIVITIES_CLEANUP', jsonb_build_object('deleted_count', deleted_count));
    END IF;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to get session statistics
CREATE OR REPLACE FUNCTION get_session_statistics(
    p_user_id UUID DEFAULT NULL,
    p_hours_back INTEGER DEFAULT 24
)
RETURNS TABLE(
    total_sessions BIGINT,
    active_sessions BIGINT,
    expired_sessions BIGINT,
    unique_users BIGINT,
    avg_session_duration INTERVAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_sessions,
        COUNT(*) FILTER (WHERE is_active = true) as active_sessions,
        COUNT(*) FILTER (WHERE is_active = false AND invalidation_reason = 'EXPIRED') as expired_sessions,
        COUNT(DISTINCT us.user_id) as unique_users,
        AVG(COALESCE(invalidated_at, CURRENT_TIMESTAMP) - created_at) as avg_session_duration
    FROM user_sessions us
    WHERE 
        (p_user_id IS NULL OR us.user_id = p_user_id) AND
        us.created_at > CURRENT_TIMESTAMP - INTERVAL '1 hour' * p_hours_back;
END;
$$ LANGUAGE plpgsql;

-- Create function to detect suspicious session activity
CREATE OR REPLACE FUNCTION detect_suspicious_sessions()
RETURNS TABLE(
    user_id UUID,
    session_count BIGINT,
    different_ips BIGINT,
    suspicious_score INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        us.user_id,
        COUNT(*) as session_count,
        COUNT(DISTINCT (device_info->>'ip')) as different_ips,
        CASE 
            WHEN COUNT(*) > 10 THEN 3  -- Too many sessions
            WHEN COUNT(DISTINCT (device_info->>'ip')) > 5 THEN 2  -- Too many IPs
            WHEN COUNT(*) > 5 THEN 1   -- Moderate activity
            ELSE 0
        END as suspicious_score
    FROM user_sessions us
    WHERE 
        us.created_at > CURRENT_TIMESTAMP - INTERVAL '24 hours' AND
        us.is_active = true
    GROUP BY us.user_id
    HAVING COUNT(*) > 3 OR COUNT(DISTINCT (device_info->>'ip')) > 2
    ORDER BY suspicious_score DESC, session_count DESC;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically log session creation
CREATE OR REPLACE FUNCTION trigger_log_session_creation()
RETURNS TRIGGER AS $$
BEGIN
    PERFORM log_session_activity(
        NEW.session_id,
        NEW.user_id,
        'SESSION_CREATED',
        jsonb_build_object(
            'expires_at', NEW.expires_at,
            'device_info', NEW.device_info
        ),
        (NEW.device_info->>'ip')::INET,
        NEW.device_info->>'userAgent'
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for session creation logging
DROP TRIGGER IF EXISTS trigger_session_creation_log ON user_sessions;
CREATE TRIGGER trigger_session_creation_log
    AFTER INSERT ON user_sessions
    FOR EACH ROW
    EXECUTE FUNCTION trigger_log_session_creation();

-- Create trigger to log session invalidation
CREATE OR REPLACE FUNCTION trigger_log_session_invalidation()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.is_active = true AND NEW.is_active = false THEN
        PERFORM log_session_activity(
            NEW.session_id,
            NEW.user_id,
            'SESSION_INVALIDATED',
            jsonb_build_object(
                'reason', NEW.invalidation_reason,
                'duration', NEW.invalidated_at - NEW.created_at
            )
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for session invalidation logging
DROP TRIGGER IF EXISTS trigger_session_invalidation_log ON user_sessions;
CREATE TRIGGER trigger_session_invalidation_log
    AFTER UPDATE ON user_sessions
    FOR EACH ROW
    EXECUTE FUNCTION trigger_log_session_invalidation();

-- Add comments for documentation
COMMENT ON TABLE user_sessions IS 'Tracks user sessions with security features';
COMMENT ON TABLE session_activities IS 'Logs all session-related activities for audit purposes';

COMMENT ON COLUMN user_sessions.session_id IS 'Unique session identifier (64-character hex string)';
COMMENT ON COLUMN user_sessions.device_info IS 'JSON containing device and browser information';
COMMENT ON COLUMN user_sessions.invalidation_reason IS 'Reason for session invalidation (EXPIRED, LOGOUT, CONCURRENT_LIMIT, etc.)';

COMMENT ON FUNCTION log_session_activity(VARCHAR, UUID, VARCHAR, JSONB, INET, TEXT) IS 'Logs session activity for audit purposes';
COMMENT ON FUNCTION cleanup_old_session_activities(INTEGER) IS 'Cleans up old session activity records';
COMMENT ON FUNCTION get_session_statistics(UUID, INTEGER) IS 'Gets session statistics for monitoring';
COMMENT ON FUNCTION detect_suspicious_sessions() IS 'Detects potentially suspicious session patterns';

-- Create indexes for better performance on large datasets
CREATE INDEX IF NOT EXISTS idx_user_sessions_composite ON user_sessions(user_id, is_active, last_activity);
CREATE INDEX IF NOT EXISTS idx_session_activities_composite ON session_activities(user_id, activity_type, created_at);

-- Create partial indexes for active sessions only
CREATE INDEX IF NOT EXISTS idx_user_sessions_active_only ON user_sessions(user_id, last_activity) WHERE is_active = true;
