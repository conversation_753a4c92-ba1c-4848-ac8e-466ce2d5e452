import React, { useState, useRef, useCallback } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import fallbackAuthService from '../services/fallbackAuthService';
import mobileDetection from '../utils/mobileDetection';

interface PatternAuthComponentProps {
  mode: 'setup' | 'authenticate';
  email?: string;
  onSuccess?: (result: any) => void;
  onError?: (error: string) => void;
  onCancel?: () => void;
  className?: string;
}

interface Point {
  x: number;
  y: number;
}

const PatternAuthComponent: React.FC<PatternAuthComponentProps> = ({
  mode,
  email,
  onSuccess,
  onError,
  onCancel,
  className = ''
}) => {
  const { t } = useLanguage();
  const [pattern, setPattern] = useState<number[]>([]);
  const [confirmPattern, setConfirmPattern] = useState<number[]>([]);
  const [hint, setHint] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showHint, setShowHint] = useState(false);
  const [step, setStep] = useState<'pattern' | 'confirm' | 'hint'>('pattern');
  const [isDrawing, setIsDrawing] = useState(false);
  
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const gridSize = 3;
  const dotRadius = 20;
  const lineWidth = 4;

  // Calculate dot positions
  const getDotPositions = useCallback((canvasWidth: number, canvasHeight: number): Point[] => {
    const positions: Point[] = [];
    const spacing = Math.min(canvasWidth, canvasHeight) / 4;
    const startX = (canvasWidth - spacing * 2) / 2;
    const startY = (canvasHeight - spacing * 2) / 2;

    for (let row = 0; row < gridSize; row++) {
      for (let col = 0; col < gridSize; col++) {
        positions.push({
          x: startX + col * spacing,
          y: startY + row * spacing
        });
      }
    }
    return positions;
  }, []);

  // Draw the pattern grid
  const drawGrid = useCallback((ctx: CanvasRenderingContext2D, positions: Point[], currentPattern: number[]) => {
    const canvas = ctx.canvas;
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw dots
    positions.forEach((pos, index) => {
      ctx.beginPath();
      ctx.arc(pos.x, pos.y, dotRadius, 0, 2 * Math.PI);
      
      if (currentPattern.includes(index)) {
        ctx.fillStyle = '#3B82F6'; // Blue for selected dots
        ctx.strokeStyle = '#1D4ED8';
      } else {
        ctx.fillStyle = '#E5E7EB'; // Gray for unselected dots
        ctx.strokeStyle = '#9CA3AF';
      }
      
      ctx.fill();
      ctx.lineWidth = 2;
      ctx.stroke();
    });

    // Draw lines between connected dots
    if (currentPattern.length > 1) {
      ctx.beginPath();
      ctx.strokeStyle = '#3B82F6';
      ctx.lineWidth = lineWidth;
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';

      const firstPos = positions[currentPattern[0]];
      ctx.moveTo(firstPos.x, firstPos.y);

      for (let i = 1; i < currentPattern.length; i++) {
        const pos = positions[currentPattern[i]];
        ctx.lineTo(pos.x, pos.y);
      }
      
      ctx.stroke();
    }
  }, []);

  // Get dot index from coordinates
  const getDotIndex = useCallback((x: number, y: number, positions: Point[]): number => {
    for (let i = 0; i < positions.length; i++) {
      const pos = positions[i];
      const distance = Math.sqrt((x - pos.x) ** 2 + (y - pos.y) ** 2);
      if (distance <= dotRadius) {
        return i;
      }
    }
    return -1;
  }, []);

  // Handle mouse/touch events
  const handleStart = useCallback((e: React.MouseEvent | React.TouchEvent) => {
    if (isLoading) return;
    
    setIsDrawing(true);
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;
    const x = clientX - rect.left;
    const y = clientY - rect.top;

    const positions = getDotPositions(canvas.width, canvas.height);
    const dotIndex = getDotIndex(x, y, positions);

    if (dotIndex !== -1) {
      const currentPattern = step === 'confirm' ? confirmPattern : pattern;
      if (!currentPattern.includes(dotIndex)) {
        const newPattern = [...currentPattern, dotIndex];
        if (step === 'confirm') {
          setConfirmPattern(newPattern);
        } else {
          setPattern(newPattern);
        }
        mobileDetection.triggerHapticFeedback('warning');
      }
    }
  }, [isLoading, step, pattern, confirmPattern, getDotPositions, getDotIndex]);

  const handleMove = useCallback((e: React.MouseEvent | React.TouchEvent) => {
    if (!isDrawing || isLoading) return;
    
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;
    const x = clientX - rect.left;
    const y = clientY - rect.top;

    const positions = getDotPositions(canvas.width, canvas.height);
    const dotIndex = getDotIndex(x, y, positions);

    if (dotIndex !== -1) {
      const currentPattern = step === 'confirm' ? confirmPattern : pattern;
      if (!currentPattern.includes(dotIndex)) {
        const newPattern = [...currentPattern, dotIndex];
        if (step === 'confirm') {
          setConfirmPattern(newPattern);
        } else {
          setPattern(newPattern);
        }
        mobileDetection.triggerHapticFeedback('warning');
      }
    }
  }, [isDrawing, isLoading, step, pattern, confirmPattern, getDotPositions, getDotIndex]);

  const handleEnd = useCallback(() => {
    setIsDrawing(false);
  }, []);

  // Redraw canvas when pattern changes
  React.useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const positions = getDotPositions(canvas.width, canvas.height);
    const currentPattern = step === 'confirm' ? confirmPattern : pattern;
    drawGrid(ctx, positions, currentPattern);
  }, [pattern, confirmPattern, step, drawGrid, getDotPositions]);

  // Handle setup flow
  const handleSetup = async () => {
    if (step === 'pattern') {
      if (pattern.length < 4) {
        onError?.('النمط يجب أن يحتوي على 4 نقاط على الأقل');
        return;
      }
      setStep('confirm');
      return;
    }

    if (step === 'confirm') {
      if (confirmPattern.length < 4) {
        onError?.('تأكيد النمط يجب أن يحتوي على 4 نقاط على الأقل');
        return;
      }
      if (JSON.stringify(pattern) !== JSON.stringify(confirmPattern)) {
        onError?.('النمط غير متطابق');
        setConfirmPattern([]);
        return;
      }
      setStep('hint');
      return;
    }

    if (step === 'hint') {
      try {
        setIsLoading(true);
        const result = await fallbackAuthService.setupPattern({
          pattern,
          confirmPattern,
          hint: hint.trim() || undefined
        });

        if (result.success) {
          mobileDetection.triggerHapticFeedback('success');
          onSuccess?.(result);
        } else {
          mobileDetection.triggerHapticFeedback('error');
          onError?.(result.message || 'فشل في إعداد النمط');
        }
      } catch (error: any) {
        mobileDetection.triggerHapticFeedback('error');
        onError?.(error.message || 'فشل في إعداد النمط');
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Handle authentication
  const handleAuthenticate = async () => {
    if (!email) {
      onError?.('البريد الإلكتروني مطلوب');
      return;
    }

    if (pattern.length < 4) {
      onError?.('النمط يجب أن يحتوي على 4 نقاط على الأقل');
      return;
    }

    try {
      setIsLoading(true);
      mobileDetection.triggerHapticFeedback('warning');

      const result = await fallbackAuthService.authenticateWithPattern(email, pattern);

      if (result.success) {
        mobileDetection.triggerHapticFeedback('success');
        onSuccess?.(result);
      } else {
        mobileDetection.triggerHapticFeedback('error');
        onError?.(result.message || 'النمط غير صحيح');
        setPattern([]);
      }
    } catch (error: any) {
      mobileDetection.triggerHapticFeedback('error');
      onError?.(error.message || 'فشل في المصادقة');
      setPattern([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle back button in setup flow
  const handleBack = () => {
    if (step === 'confirm') {
      setStep('pattern');
      setConfirmPattern([]);
    } else if (step === 'hint') {
      setStep('confirm');
      setHint('');
    }
  };

  // Clear pattern
  const clearPattern = () => {
    if (step === 'confirm') {
      setConfirmPattern([]);
    } else {
      setPattern([]);
    }
  };

  return (
    <div className={`pattern-auth-component ${className}`} style={{ direction: 'rtl' }}>
      <div className="bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto">
        <div className="text-center mb-6">
          <div className="text-4xl mb-4">⚫</div>
          <h3 className="text-xl font-bold font-['Almarai']">
            {mode === 'setup' ? 'إعداد النمط' : 'تسجيل الدخول بالنمط'}
          </h3>
          <p className="text-gray-600 mt-2 font-['Almarai']">
            {mode === 'setup' 
              ? step === 'pattern' ? 'ارسم نمط بربط 4 نقاط على الأقل'
                : step === 'confirm' ? 'أكد النمط بإعادة رسمه'
                : 'أضف تلميح اختياري (يساعدك على التذكر)'
              : 'ارسم النمط الخاص بك'
            }
          </p>
        </div>

        {(step === 'pattern' || step === 'confirm' || mode === 'authenticate') && (
          <div className="mb-6">
            <canvas
              ref={canvasRef}
              width={300}
              height={300}
              className="border border-gray-300 rounded-lg mx-auto block cursor-pointer"
              onMouseDown={handleStart}
              onMouseMove={handleMove}
              onMouseUp={handleEnd}
              onMouseLeave={handleEnd}
              onTouchStart={handleStart}
              onTouchMove={handleMove}
              onTouchEnd={handleEnd}
              style={{ touchAction: 'none' }}
            />
            
            <div className="text-center mt-4">
              <button
                onClick={clearPattern}
                disabled={isLoading}
                className="text-gray-500 hover:text-gray-700 text-sm font-['Almarai']"
              >
                مسح النمط
              </button>
            </div>

            <div className="text-center mt-2">
              <p className="text-sm text-gray-500 font-['Almarai']">
                النقاط المحددة: {step === 'confirm' ? confirmPattern.length : pattern.length}
              </p>
            </div>
          </div>
        )}

        {mode === 'setup' && (step === 'pattern' || step === 'confirm') && (
          <div className="flex gap-3 justify-center">
            <button
              onClick={handleSetup}
              disabled={
                (step === 'pattern' && pattern.length < 4) ||
                (step === 'confirm' && confirmPattern.length < 4) ||
                isLoading
              }
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-['Almarai'] disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {step === 'pattern' ? 'المتابعة' : 'تأكيد النمط'}
            </button>
            {step === 'confirm' && (
              <button
                onClick={handleBack}
                className="bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 transition-colors font-['Almarai']"
              >
                رجوع
              </button>
            )}
            <button
              onClick={onCancel}
              className="bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 transition-colors font-['Almarai']"
            >
              إلغاء
            </button>
          </div>
        )}

        {mode === 'setup' && step === 'hint' && (
          <div>
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2 font-['Almarai']">
                تلميح اختياري
              </label>
              <input
                type="text"
                value={hint}
                onChange={(e) => setHint(e.target.value)}
                placeholder="مثال: شكل الحرف الأول من اسمي"
                className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 font-['Almarai']"
                maxLength={100}
                disabled={isLoading}
              />
              <p className="text-xs text-gray-500 mt-1 font-['Almarai']">
                التلميح سيساعدك على تذكر النمط (اختياري)
              </p>
            </div>
            <div className="flex gap-3 justify-center">
              <button
                onClick={handleSetup}
                disabled={isLoading}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-['Almarai'] disabled:opacity-50"
              >
                {isLoading ? 'جاري الحفظ...' : 'حفظ النمط'}
              </button>
              <button
                onClick={handleBack}
                disabled={isLoading}
                className="bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 transition-colors font-['Almarai']"
              >
                رجوع
              </button>
            </div>
          </div>
        )}

        {mode === 'authenticate' && (
          <div>
            {showHint && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
                <p className="text-yellow-800 text-sm font-['Almarai']">
                  💡 تلميح: {hint || 'لا يوجد تلميح محفوظ'}
                </p>
              </div>
            )}

            <div className="flex gap-3 justify-center mb-4">
              <button
                onClick={handleAuthenticate}
                disabled={pattern.length < 4 || isLoading}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-['Almarai'] disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'جاري التحقق...' : 'تسجيل الدخول'}
              </button>
              <button
                onClick={onCancel}
                disabled={isLoading}
                className="bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 transition-colors font-['Almarai']"
              >
                إلغاء
              </button>
            </div>

            <div className="text-center">
              <button
                onClick={() => setShowHint(!showHint)}
                className="text-blue-600 hover:text-blue-800 text-sm font-['Almarai']"
              >
                {showHint ? 'إخفاء التلميح' : 'عرض التلميح'}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PatternAuthComponent;
