const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 255;
};

const validatePassword = (password) => {
  if (!password || typeof password !== 'string') {
    return {
      isValid: false,
      message: 'كلمة المرور مطلوبة'
    };
  }

  // Check length requirements
  if (password.length < 12) {
    return {
      isValid: false,
      message: 'يجب أن تكون كلمة المرور 12 حرفاً على الأقل'
    };
  }

  if (password.length > 128) {
    return {
      isValid: false,
      message: 'كلمة المرور طويلة جداً (الحد الأقصى 128 حرف)'
    };
  }

  // Check for uppercase letter
  if (!/[A-Z]/.test(password)) {
    return {
      isValid: false,
      message: 'يجب أن تحتوي كلمة المرور على حرف كبير واحد على الأقل'
    };
  }

  // Check for lowercase letter
  if (!/[a-z]/.test(password)) {
    return {
      isValid: false,
      message: 'يجب أن تحتوي كلمة المرور على حرف صغير واحد على الأقل'
    };
  }

  // Check for number
  if (!/\d/.test(password)) {
    return {
      isValid: false,
      message: 'يجب أن تحتوي كلمة المرور على رقم واحد على الأقل'
    };
  }

  // Check for special character
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    return {
      isValid: false,
      message: 'يجب أن تحتوي كلمة المرور على رمز خاص واحد على الأقل (!@#$%^&*)'
    };
  }

  // Check for common weak patterns
  const weakPatterns = [
    /(.)\1{2,}/, // Three or more consecutive identical characters
    /123456|654321|abcdef|qwerty|password|admin|user/i, // Common sequences
    /^(.{1,2})\1+$/ // Repeated short patterns
  ];

  for (const pattern of weakPatterns) {
    if (pattern.test(password)) {
      return {
        isValid: false,
        message: 'كلمة المرور ضعيفة جداً. يرجى اختيار كلمة مرور أكثر تعقيداً'
      };
    }
  }

  return {
    isValid: true,
    message: 'كلمة المرور قوية'
  };
};

// Legacy function for backward compatibility
const validatePasswordLegacy = (password) => {
  const result = validatePassword(password);
  return result.isValid;
};

const sanitizeInput = (input, options = {}) => {
  if (typeof input !== 'string') return input;

  const {
    allowHtml = false,
    maxLength = 1000,
    preserveNewlines = false
  } = options;

  // Trim whitespace
  let sanitized = input.trim();

  // Check length limit
  if (sanitized.length > maxLength) {
    sanitized = sanitized.substring(0, maxLength);
  }

  if (!allowHtml) {
    // Remove HTML tags and encode special characters
    sanitized = sanitized
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;')
      .replace(/&/g, '&amp;'); // This should be last to avoid double encoding
  }

  // Remove or preserve newlines
  if (!preserveNewlines) {
    sanitized = sanitized.replace(/[\r\n]/g, ' ');
  }

  // Remove null bytes and other control characters
  sanitized = sanitized.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');

  // Remove potential script injection patterns
  const dangerousPatterns = [
    /javascript:/gi,
    /vbscript:/gi,
    /onload=/gi,
    /onerror=/gi,
    /onclick=/gi,
    /onmouseover=/gi,
    /onfocus=/gi,
    /onblur=/gi,
    /onchange=/gi,
    /onsubmit=/gi,
    /data:text\/html/gi,
    /data:application\/javascript/gi
  ];

  dangerousPatterns.forEach(pattern => {
    sanitized = sanitized.replace(pattern, '');
  });

  return sanitized;
};

// Specialized sanitization functions
const sanitizeEmail = (email) => {
  if (typeof email !== 'string') return '';
  return email.toLowerCase().trim().replace(/[^\w@.-]/g, '');
};

const sanitizeFilename = (filename) => {
  if (typeof filename !== 'string') return '';
  return filename
    .trim()
    .replace(/[^\w.-]/g, '_') // Replace non-alphanumeric chars with underscore
    .replace(/_{2,}/g, '_') // Replace multiple underscores with single
    .substring(0, 255); // Limit length
};

const sanitizeNumeric = (input) => {
  if (typeof input === 'number') return input;
  if (typeof input !== 'string') return null;
  const numeric = input.replace(/[^\d.-]/g, '');
  const parsed = parseFloat(numeric);
  return isNaN(parsed) ? null : parsed;
};

const validateUUID = (uuid) => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
};

// Account security functions
const checkAccountLockout = async (email, query) => {
  try {
    const result = await query(
      'SELECT failed_login_attempts, locked_until FROM users WHERE email = $1',
      [email]
    );

    if (result.rows.length === 0) {
      return { isLocked: false, attemptsRemaining: 5 };
    }

    const user = result.rows[0];
    const now = new Date();

    // Check if account is currently locked
    if (user.locked_until && new Date(user.locked_until) > now) {
      return {
        isLocked: true,
        lockedUntil: user.locked_until,
        attemptsRemaining: 0
      };
    }

    // Reset failed attempts if lock period has expired
    if (user.locked_until && new Date(user.locked_until) <= now) {
      await query(
        'UPDATE users SET failed_login_attempts = 0, locked_until = NULL WHERE email = $1',
        [email]
      );
      return { isLocked: false, attemptsRemaining: 5 };
    }

    const attemptsRemaining = Math.max(0, 5 - (user.failed_login_attempts || 0));
    return {
      isLocked: false,
      attemptsRemaining,
      currentAttempts: user.failed_login_attempts || 0
    };

  } catch (error) {
    console.error('Error checking account lockout:', error);
    return { isLocked: false, attemptsRemaining: 5 };
  }
};

const recordFailedLogin = async (email, query) => {
  try {
    const result = await query(
      `UPDATE users
       SET failed_login_attempts = COALESCE(failed_login_attempts, 0) + 1,
           locked_until = CASE
             WHEN COALESCE(failed_login_attempts, 0) + 1 >= 5
             THEN NOW() + INTERVAL '30 minutes'
             ELSE locked_until
           END
       WHERE email = $1
       RETURNING failed_login_attempts, locked_until`,
      [email]
    );

    if (result.rows.length > 0) {
      const user = result.rows[0];
      return {
        attempts: user.failed_login_attempts,
        isLocked: user.locked_until && new Date(user.locked_until) > new Date(),
        lockedUntil: user.locked_until
      };
    }

    return { attempts: 0, isLocked: false };
  } catch (error) {
    console.error('Error recording failed login:', error);
    return { attempts: 0, isLocked: false };
  }
};

const recordSuccessfulLogin = async (email, query) => {
  try {
    await query(
      'UPDATE users SET failed_login_attempts = 0, locked_until = NULL WHERE email = $1',
      [email]
    );
  } catch (error) {
    console.error('Error recording successful login:', error);
  }
};

module.exports = {
  validateEmail,
  validatePassword,
  validatePasswordLegacy,
  sanitizeInput,
  sanitizeEmail,
  sanitizeFilename,
  sanitizeNumeric,
  validateUUID,
  checkAccountLockout,
  recordFailedLogin,
  recordSuccessfulLogin
};
