const { query } = require('./src/models/database');

async function checkTables() {
  try {
    console.log('Checking current database schema...');
    
    // Get all tables
    const tablesResult = await query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    console.log('\n=== Current Database Tables ===');
    tablesResult.rows.forEach(row => {
      console.log(`- ${row.table_name}`);
    });
    
    // Check for geographical security related tables
    const geoTables = tablesResult.rows.filter(row =>
      row.table_name.includes('geographical') ||
      row.table_name.includes('location') ||
      row.table_name.includes('security_alert') ||
      row.table_name.includes('user_location_history')
    );
    
    console.log('\n=== Geographical/Security Related Tables ===');
    if (geoTables.length > 0) {
      geoTables.forEach(row => {
        console.log(`⚠️  ${row.table_name} - May need review/removal`);
      });
    } else {
      console.log('✅ No complex geographical security tables found');
    }
    
    // Check for Microsoft auth related columns
    const usersResult = await query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      ORDER BY column_name
    `);
    
    console.log('\n=== Users Table Columns ===');
    const microsoftColumns = [];
    usersResult.rows.forEach(row => {
      console.log(`- ${row.column_name} (${row.data_type})`);
      if (row.column_name.toLowerCase().includes('microsoft') || 
          row.column_name.toLowerCase().includes('oauth') ||
          row.column_name.toLowerCase().includes('azure')) {
        microsoftColumns.push(row.column_name);
      }
    });
    
    if (microsoftColumns.length > 0) {
      console.log('\n⚠️  Microsoft/OAuth related columns found:');
      microsoftColumns.forEach(col => console.log(`   - ${col}`));
    } else {
      console.log('\n✅ No Microsoft/OAuth related columns found');
    }

    // Note: Authentication system uses standard email/password with JWT tokens
    console.log('\n=== Authentication System Status ===');
    console.log('✅ Authentication system uses standard email/password with JWT tokens');

  } catch (error) {
    console.error('Error checking database:', error.message);
  } finally {
    process.exit(0);
  }
}

checkTables();
