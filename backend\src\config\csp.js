/**
 * Content Security Policy Configuration
 * Comprehensive CSP settings for the e-signature application
 */

// Generate nonce for inline scripts (if needed)
const generateNonce = () => {
  return require('crypto').randomBytes(16).toString('base64');
};

// CSP configuration based on environment
const getCSPConfig = (env = 'development') => {
  const isProduction = env === 'production';
  const isDevelopment = env === 'development';

  // Base CSP configuration
  const baseConfig = {
    // Default source - only allow same origin
    defaultSrc: ["'self'"],

    // Script sources
    scriptSrc: [
      "'self'",
      // Allow inline scripts only in development with nonce
      ...(isDevelopment ? ["'unsafe-inline'"] : []),
      // Add specific script sources if needed
      // 'https://trusted-cdn.com'
    ],

    // Style sources - needed for CSS and fonts
    styleSrc: [
      "'self'",
      "'unsafe-inline'", // Required for React and dynamic styles
      "https://fonts.googleapis.com",
      "fonts.googleapis.com"
    ],

    // Font sources
    fontSrc: [
      "'self'",
      "https://fonts.gstatic.com",
      "fonts.gstatic.com",
      "data:" // For base64 encoded fonts
    ],

    // Image sources
    imgSrc: [
      "'self'",
      "data:", // For base64 images and signatures
      "blob:", // For PDF rendering and file previews
      ...(isDevelopment ? ["https:"] : []) // Allow HTTPS images in dev
    ],

    // Media sources
    mediaSrc: ["'self'"],

    // Object sources - disabled for security
    objectSrc: ["'none'"],

    // Frame sources - disabled to prevent clickjacking
    frameSrc: ["'none'"],

    // Child sources - for web workers (PDF.js)
    childSrc: [
      "'self'",
      "blob:" // For PDF.js workers
    ],

    // Worker sources - for web workers
    workerSrc: [
      "'self'",
      "blob:" // For PDF.js workers
    ],

    // Connect sources - for AJAX requests
    connectSrc: [
      "'self'",
      // Add frontend URL for CORS requests
      process.env.FRONTEND_URL || 'http://localhost:3000',
      ...(isDevelopment ? [
        'http://localhost:3000',
        'https://localhost:3000',
        'ws://localhost:3000', // For WebSocket connections in dev
        'wss://localhost:3000'
      ] : [])
    ],

    // Form action - where forms can be submitted
    formAction: ["'self'"],

    // Frame ancestors - prevent embedding in frames
    frameAncestors: ["'none'"],

    // Base URI - restrict base tag
    baseUri: ["'self'"],

    // Manifest source
    manifestSrc: ["'self'"],

    // Upgrade insecure requests in production
    ...(isProduction && { upgradeInsecureRequests: [] })
  };

  return baseConfig;
};

// CSP violation reporting configuration
const getCSPReportConfig = () => {
  return {
    reportUri: '/api/security/csp-report',
    reportTo: 'csp-endpoint'
  };
};

// Generate CSP header string
const generateCSPHeader = (config) => {
  const directives = [];

  for (const [directive, sources] of Object.entries(config)) {
    if (Array.isArray(sources)) {
      if (sources.length > 0) {
        const directiveName = directive.replace(/([A-Z])/g, '-$1').toLowerCase();
        directives.push(`${directiveName} ${sources.join(' ')}`);
      }
    } else if (sources === true) {
      const directiveName = directive.replace(/([A-Z])/g, '-$1').toLowerCase();
      directives.push(directiveName);
    }
  }

  return directives.join('; ');
};

// CSP middleware factory
const createCSPMiddleware = (options = {}) => {
  const {
    reportOnly = false,
    enableReporting = false,
    customDirectives = {}
  } = options;

  return (req, res, next) => {
    try {
      const env = process.env.NODE_ENV || 'development';
      let cspConfig = getCSPConfig(env);

      // Merge custom directives
      cspConfig = { ...cspConfig, ...customDirectives };

      // Add reporting if enabled
      if (enableReporting) {
        const reportConfig = getCSPReportConfig();
        cspConfig = { ...cspConfig, ...reportConfig };
      }

      // Generate CSP header
      const cspHeader = generateCSPHeader(cspConfig);
      const headerName = reportOnly ? 'Content-Security-Policy-Report-Only' : 'Content-Security-Policy';

      // Set CSP header
      res.setHeader(headerName, cspHeader);

      // Set Report-To header if reporting is enabled
      if (enableReporting && !reportOnly) {
        res.setHeader('Report-To', JSON.stringify({
          group: 'csp-endpoint',
          max_age: 10886400,
          endpoints: [{ url: '/api/security/csp-report' }]
        }));
      }

      next();
    } catch (error) {
      console.error('CSP middleware error:', error);
      next(); // Continue without CSP on error
    }
  };
};

// CSP violation report handler
const handleCSPViolation = async (req, res) => {
  try {
    const violation = req.body;
    
    // Log CSP violation
    console.warn('CSP Violation Report:', {
      documentUri: violation['document-uri'],
      violatedDirective: violation['violated-directive'],
      blockedUri: violation['blocked-uri'],
      sourceFile: violation['source-file'],
      lineNumber: violation['line-number'],
      columnNumber: violation['column-number'],
      userAgent: req.get('User-Agent'),
      timestamp: new Date().toISOString()
    });

    // Store violation in database if needed
    // await logSecurityViolation(violation, req);

    res.status(204).send(); // No content response
  } catch (error) {
    console.error('CSP violation handler error:', error);
    res.status(500).json({ error: 'Failed to process CSP violation report' });
  }
};

// Nonce generator for inline scripts
const nonceMiddleware = (req, res, next) => {
  res.locals.nonce = generateNonce();
  next();
};

module.exports = {
  getCSPConfig,
  generateCSPHeader,
  createCSPMiddleware,
  handleCSPViolation,
  nonceMiddleware,
  generateNonce
};
