-- Geographical Security System Migration
-- Implements comprehensive location-based security monitoring
-- Author: Augment Agent
-- Date: 2025-07-17

-- User Location History Table
-- Tracks user access patterns and location history
CREATE TABLE IF NOT EXISTS user_location_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Location identification
    location_hash VARCHAR(64) NOT NULL, -- SHA-256 hash of location data for uniqueness
    country_code CHAR(2) NOT NULL, -- ISO country code
    region_name VARCHAR(100), -- Region/state name
    city_name VARCHAR(100), -- City name
    
    -- Geographical coordinates
    latitude DECIMAL(10, 8), -- Latitude with high precision
    longitude DECIMAL(11, 8), -- Longitude with high precision
    accuracy_radius INTEGER, -- Accuracy radius in meters
    
    -- Network information
    isp_name VARCHAR(200), -- Internet Service Provider
    organization VARCHAR(200), -- Organization name
    asn INTEGER, -- Autonomous System Number
    
    -- Access tracking
    first_seen TIMES<PERSON><PERSON> DEFAULT NOW(), -- First time this location was seen
    last_seen TIMESTAMP DEFAULT NOW(), -- Last time this location was accessed
    access_count INTEGER DEFAULT 1, -- Number of times accessed from this location
    
    -- Trust and security
    trust_level VARCHAR(20) DEFAULT 'unknown', -- 'trusted', 'suspicious', 'blocked', 'unknown'
    is_vpn_proxy BOOLEAN DEFAULT FALSE, -- VPN/proxy detection
    detection_method VARCHAR(20) DEFAULT 'ip', -- 'ip', 'browser', 'hybrid'
    
    -- Metadata
    timezone VARCHAR(100), -- Timezone information
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT check_trust_level CHECK (trust_level IN ('trusted', 'suspicious', 'blocked', 'unknown')),
    CONSTRAINT check_detection_method CHECK (detection_method IN ('ip', 'browser', 'hybrid')),
    CONSTRAINT unique_user_location UNIQUE (user_id, location_hash)
);

-- Geographical Security Alerts Table
-- Manages security notifications and alerts
CREATE TABLE IF NOT EXISTS geographical_security_alerts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Alert classification
    alert_type VARCHAR(50) NOT NULL, -- 'new_country', 'impossible_travel', 'vpn_location_change', 'concurrent_locations'
    severity_level VARCHAR(20) NOT NULL, -- 'low', 'medium', 'high', 'critical'
    
    -- Alert details
    title VARCHAR(200) NOT NULL, -- Alert title in Arabic
    description TEXT NOT NULL, -- Detailed description in Arabic
    
    -- Location information
    current_location JSONB, -- Current location data
    previous_location JSONB, -- Previous location data (for comparison)
    
    -- Travel analysis (for impossible travel alerts)
    travel_distance_km INTEGER, -- Distance traveled in kilometers
    travel_time_minutes INTEGER, -- Time between locations in minutes
    calculated_speed_kmh INTEGER, -- Calculated travel speed
    
    -- Alert status and response
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'confirmed', 'denied', 'ignored', 'auto_resolved'
    user_response VARCHAR(20), -- 'confirmed', 'denied', 'ignored'
    user_response_at TIMESTAMP, -- When user responded
    admin_notes TEXT, -- Administrative notes
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT NOW(),
    resolved_at TIMESTAMP,
    
    -- Constraints
    CONSTRAINT check_alert_type CHECK (alert_type IN ('new_country', 'impossible_travel', 'vpn_location_change', 'concurrent_locations')),
    CONSTRAINT check_severity_level CHECK (severity_level IN ('low', 'medium', 'high', 'critical')),
    CONSTRAINT check_status CHECK (status IN ('pending', 'confirmed', 'denied', 'ignored', 'auto_resolved')),
    CONSTRAINT check_user_response CHECK (user_response IN ('confirmed', 'denied', 'ignored'))
);

-- Extend users table with geographical security preferences
ALTER TABLE users ADD COLUMN IF NOT EXISTS geographical_security_enabled BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS geographical_consent_given BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS geographical_consent_date TIMESTAMP;
ALTER TABLE users ADD COLUMN IF NOT EXISTS trusted_countries TEXT[]; -- Array of ISO country codes
ALTER TABLE users ADD COLUMN IF NOT EXISTS alert_preferences JSONB DEFAULT '{"new_country": true, "impossible_travel": true, "vpn_location_change": true, "concurrent_locations": true}';
ALTER TABLE users ADD COLUMN IF NOT EXISTS notification_preferences JSONB DEFAULT '{"email": true, "sms": false, "push": true}';

-- Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_user_location_history_user_id ON user_location_history(user_id);
CREATE INDEX IF NOT EXISTS idx_user_location_history_country ON user_location_history(country_code);
CREATE INDEX IF NOT EXISTS idx_user_location_history_last_seen ON user_location_history(last_seen);
CREATE INDEX IF NOT EXISTS idx_user_location_history_trust_level ON user_location_history(trust_level);

CREATE INDEX IF NOT EXISTS idx_geographical_alerts_user_id ON geographical_security_alerts(user_id);
CREATE INDEX IF NOT EXISTS idx_geographical_alerts_status ON geographical_security_alerts(status);
CREATE INDEX IF NOT EXISTS idx_geographical_alerts_severity ON geographical_security_alerts(severity_level);
CREATE INDEX IF NOT EXISTS idx_geographical_alerts_type ON geographical_security_alerts(alert_type);
CREATE INDEX IF NOT EXISTS idx_geographical_alerts_created ON geographical_security_alerts(created_at);

-- Note: biometric_auth_logs table indexes removed as part of biometric cleanup

-- Create function to update location history
CREATE OR REPLACE FUNCTION update_location_history_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic timestamp updates
CREATE TRIGGER trigger_update_location_history_timestamp
    BEFORE UPDATE ON user_location_history
    FOR EACH ROW
    EXECUTE FUNCTION update_location_history_timestamp();

-- Add table comments for documentation
COMMENT ON TABLE user_location_history IS 'Tracks user access patterns and geographical location history for security monitoring';
COMMENT ON TABLE geographical_security_alerts IS 'Manages geographical security alerts and user responses';

-- Notification Logs Table
-- Tracks notification attempts and results
CREATE TABLE IF NOT EXISTS notification_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    alert_id UUID REFERENCES geographical_security_alerts(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    channels_attempted JSONB NOT NULL, -- Array of attempted channels
    channels_successful JSONB NOT NULL, -- Array of successful channels
    errors JSONB, -- Array of error messages
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for notification logs
CREATE INDEX IF NOT EXISTS idx_notification_logs_alert_id ON notification_logs(alert_id);
CREATE INDEX IF NOT EXISTS idx_notification_logs_user_id ON notification_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_logs_created ON notification_logs(created_at);

-- Add column comments for clarity
COMMENT ON COLUMN user_location_history.location_hash IS 'SHA-256 hash of location data for unique identification';
COMMENT ON COLUMN user_location_history.trust_level IS 'Trust level assigned to this location based on user behavior';
COMMENT ON COLUMN geographical_security_alerts.travel_distance_km IS 'Distance between locations for impossible travel detection';
COMMENT ON COLUMN geographical_security_alerts.calculated_speed_kmh IS 'Calculated travel speed for impossibility analysis';
COMMENT ON TABLE notification_logs IS 'Tracks notification attempts and delivery results for security alerts';
