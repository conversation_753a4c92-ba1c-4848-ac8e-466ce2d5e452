const { query } = require('./src/models/database');

async function createMissingTables() {
  try {
    console.log('Creating missing database tables and columns...');
    
    // 1. Add language and text_direction columns to users table
    console.log('Adding language and text_direction columns to users table...');
    await query(`
      ALTER TABLE users 
      ADD COLUMN IF NOT EXISTS language VARCHAR(5) DEFAULT 'ar',
      ADD COLUMN IF NOT EXISTS text_direction VARCHAR(3) DEFAULT 'rtl'
    `);
    console.log('✅ Added language columns to users table');
    
    // 2. Create arabic_templates table
    console.log('Creating arabic_templates table...');
    await query(`
      CREATE TABLE IF NOT EXISTS arabic_templates (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        template_type VARCHAR(50) NOT NULL,
        template_name VARCHAR(100) NOT NULL,
        arabic_text TEXT NOT NULL,
        english_text TEXT,
        description TEXT,
        category VARCHAR(50) DEFAULT 'general',
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW(),
        UNIQUE(template_type, template_name)
      )
    `);
    console.log('✅ Created arabic_templates table');
    
    // 3. Insert default Arabic templates
    console.log('Inserting default Arabic templates...');
    const templates = [
      {
        type: 'document_signed',
        name: 'Document Signed Success',
        arabic: 'تم توقيع المستند بنجاح',
        english: 'Document signed successfully',
        description: 'Success message for document signing'
      },
      {
        type: 'signature_uploaded',
        name: 'Signature Upload Success',
        arabic: 'تم رفع التوقيع بنجاح',
        english: 'Signature uploaded successfully',
        description: 'Success message for signature upload'
      },
      {
        type: 'user_registered',
        name: 'User Registration Success',
        arabic: 'تم إنشاء الحساب بنجاح',
        english: 'Account created successfully',
        description: 'Success message for user registration'
      },
      {
        type: 'login_success',
        name: 'Login Success',
        arabic: 'تم تسجيل الدخول بنجاح',
        english: 'Login successful',
        description: 'Success message for login'
      },
      {
        type: 'invalid_credentials',
        name: 'Invalid Credentials',
        arabic: 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
        english: 'Invalid email or password',
        description: 'Error message for invalid credentials'
      },
      {
        type: 'unauthorized_access',
        name: 'Unauthorized Access',
        arabic: 'غير مخول للوصول',
        english: 'Unauthorized access',
        description: 'Error message for unauthorized access'
      },
      {
        type: 'file_type_error',
        name: 'Invalid File Type',
        arabic: 'نوع الملف غير مدعوم',
        english: 'File type not supported',
        description: 'Error message for invalid file types'
      },
      {
        type: 'duplicate_email',
        name: 'Duplicate Email',
        arabic: 'البريد الإلكتروني مسجل بالفعل',
        english: 'Email already registered',
        description: 'Error message for duplicate email registration'
      }
    ];
    
    for (const template of templates) {
      try {
        await query(`
          INSERT INTO arabic_templates (template_type, template_name, arabic_text, english_text, description)
          VALUES ($1, $2, $3, $4, $5)
          ON CONFLICT (template_type, template_name) DO UPDATE SET
            arabic_text = EXCLUDED.arabic_text,
            english_text = EXCLUDED.english_text,
            description = EXCLUDED.description,
            updated_at = NOW()
        `, [template.type, template.name, template.arabic, template.english, template.description]);
        console.log(`✅ Inserted template: ${template.type}`);
      } catch (error) {
        console.log(`⚠ Template ${template.type} already exists or error:`, error.message);
      }
    }
    
    // 4. Update existing users to have Arabic defaults
    console.log('Updating existing users with Arabic defaults...');
    await query(`
      UPDATE users 
      SET language = 'ar', text_direction = 'rtl' 
      WHERE language IS NULL OR text_direction IS NULL
    `);
    console.log('✅ Updated existing users with Arabic defaults');
    
    // 5. Create indexes for performance
    console.log('Creating indexes...');
    await query(`CREATE INDEX IF NOT EXISTS idx_arabic_templates_type ON arabic_templates(template_type)`);
    await query(`CREATE INDEX IF NOT EXISTS idx_arabic_templates_active ON arabic_templates(is_active) WHERE is_active = true`);
    await query(`CREATE INDEX IF NOT EXISTS idx_users_language ON users(language)`);
    console.log('✅ Created indexes');
    
    console.log('\n✅ All missing tables and columns created successfully!');
    
    // Verify the changes
    console.log('\nVerifying changes...');
    
    // Check if columns exist
    const columnsResult = await query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'users' AND column_name IN ('language', 'text_direction')
    `);
    console.log(`✅ Found ${columnsResult.rows.length}/2 language columns in users table`);
    
    // Check if arabic_templates table exists
    const tableResult = await query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'arabic_templates'
      )
    `);
    console.log(`✅ Arabic templates table exists: ${tableResult.rows[0].exists}`);
    
    // Check template count
    const templateCount = await query(`SELECT COUNT(*) FROM arabic_templates`);
    console.log(`✅ Found ${templateCount.rows[0].count} Arabic templates`);
    
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

createMissingTables();
