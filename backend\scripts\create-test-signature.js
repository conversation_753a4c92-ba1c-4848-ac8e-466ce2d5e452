const fs = require('fs');
const path = require('path');

// Create test-files directory if it doesn't exist
const testFilesDir = path.join(__dirname, 'test-files');
if (!fs.existsSync(testFilesDir)) {
  fs.mkdirSync(testFilesDir, { recursive: true });
}

// Create a minimal PNG file (1x1 transparent pixel)
const pngData = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');

// Write the test signature file
const signatureFilePath = path.join(testFilesDir, 'test-signature.png');
fs.writeFileSync(signatureFilePath, pngData);

console.log('✅ Test signature file created:', signatureFilePath);
console.log('✅ File size:', fs.statSync(signatureFilePath).size, 'bytes');

// Create a minimal PDF file for testing
const pdfData = Buffer.from('%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n174\n%%EOF');

const pdfFilePath = path.join(testFilesDir, 'test-document.pdf');
fs.writeFileSync(pdfFilePath, pdfData);

console.log('✅ Test PDF file created:', pdfFilePath);
console.log('✅ File size:', fs.statSync(pdfFilePath).size, 'bytes');