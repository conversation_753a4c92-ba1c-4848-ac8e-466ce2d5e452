const fs = require('fs').promises;
const path = require('path');

// Optional bidi-js import for bidirectional text processing
let bidi = null;
try {
  bidi = require('bidi-js');
} catch (error) {
  console.warn('bidi-js not available - bidirectional text processing disabled');
}

// Arabic font configuration - Using only Alma<PERSON>
const ARABIC_FONTS = {
  ALMARAI: {
    name: 'Almarai',
    path: path.join(__dirname, '../fonts/Almarai-Regular.ttf'),
    fallbackUrl: 'https://fonts.gstatic.com/s/almarai/v12/tsssAp1RZy0Q_q2-2wjunAcFawmu.ttf'
  }
};

// Arabic character ranges
const ARABIC_RANGES = [
  [0x0600, 0x06FF], // Arabic
  [0x0750, 0x077F], // Arabic Supplement
  [0x08A0, 0x08FF], // Arabic Extended-A
  [0xFB50, 0xFDFF], // Arabic Presentation Forms-A
  [0xFE70, 0xFEFF], // Arabic Presentation Forms-B
];

// Ensure fonts directory exists
const ensureFontsDirectory = async () => {
  const fontsDir = path.join(__dirname, '../fonts');
  try {
    await fs.access(fontsDir);
  } catch (error) {
    await fs.mkdir(fontsDir, { recursive: true });
  }
  return fontsDir;
};

// Check if text contains Arabic characters
const containsArabic = (text) => {
  if (!text || typeof text !== 'string') return false;

  for (let i = 0; i < text.length; i++) {
    const charCode = text.charCodeAt(i);
    for (const [start, end] of ARABIC_RANGES) {
      if (charCode >= start && charCode <= end) {
        return true;
      }
    }
  }
  return false;
};

// Detect text direction based on content
const detectTextDirection = (text) => {
  if (!text || typeof text !== 'string') return 'ltr';

  // If text contains Arabic characters, it's RTL
  if (containsArabic(text)) {
    return 'rtl';
  }

  // Default to LTR for non-Arabic text
  return 'ltr';
};

// Process bidirectional text using bidi-js
const processBidiText = (text, baseDirection = 'auto') => {
  if (!text) return text;

  // If bidi-js is not available, return original text
  if (!bidi) {
    return text;
  }

  try {
    // Auto-detect direction if not specified
    if (baseDirection === 'auto') {
      baseDirection = detectTextDirection(text);
    }

    // Process bidirectional text
    const bidiText = bidi(text, { dir: baseDirection });
    return bidiText;
  } catch (error) {
    console.warn('Bidi processing failed, using original text:', error);
    return text;
  }
};

// Download font if not exists
const downloadFont = async (fontConfig) => {
  try {
    await fs.access(fontConfig.path);
    return fontConfig.path; // Font already exists
  } catch (error) {
    console.log(`Downloading font: ${fontConfig.name}`);
    
    // For now, we'll use a placeholder approach
    // In production, you would download from fontConfig.fallbackUrl
    console.warn(`Font ${fontConfig.name} not found. Please manually download and place at ${fontConfig.path}`);
    return null;
  }
};

// Get appropriate font based on text content
const getAppropriateFont = async (text) => {
  const isArabicText = containsArabic(text);

  if (isArabicText) {
    const fontPath = await downloadFont(ARABIC_FONTS.ALMARAI);
    return {
      fontPath,
      fontName: ARABIC_FONTS.ALMARAI.name,
      isArabic: true
    };
  } else {
    // Return default font for non-Arabic text
    return {
      fontPath: null,
      fontName: 'Helvetica',
      isArabic: false
    };
  }
};

// Calculate text width for RTL positioning
const calculateTextWidth = (text, fontSize = 12) => {
  // Approximate calculation - in production you'd use actual font metrics
  const avgCharWidth = fontSize * 0.6;
  return text.length * avgCharWidth;
};

// Get text positioning for RTL (Arabic-only)
const getTextPosition = (text, x, y, pageWidth, fontSize = 12) => {
  // Always calculate RTL positioning for Arabic
  const textWidth = calculateTextWidth(text, fontSize);
  return {
    x: pageWidth - x - textWidth, // Adjust for RTL
    y: y,
    direction: 'rtl'
  };
};

// Create sample Almarai font (for testing when actual font isn't available)
const createSampleFonts = async () => {
  const fontsDir = await ensureFontsDirectory();

  // Create placeholder font file (in production, use actual Almarai font)
  const sampleFontContent = Buffer.from('SAMPLE_FONT_PLACEHOLDER');

  const fontConfig = ARABIC_FONTS.ALMARAI;
  try {
    await fs.access(fontConfig.path);
  } catch (error) {
    console.log(`Creating placeholder for ${fontConfig.name}`);
    // In production, download actual Almarai font here
    // await fs.writeFile(fontConfig.path, sampleFontContent);
  }
};

// Initialize Almarai font service
const initializeArabicFonts = async () => {
  try {
    await ensureFontsDirectory();
    await createSampleFonts();
    console.log('Almarai font service initialized');
    return true;
  } catch (error) {
    console.error('Failed to initialize Almarai font:', error);
    return false;
  }
};

module.exports = {
  containsArabic,
  detectTextDirection,
  processBidiText,
  getAppropriateFont,
  getTextPosition,
  calculateTextWidth,
  initializeArabicFonts,
  ARABIC_FONTS
};
