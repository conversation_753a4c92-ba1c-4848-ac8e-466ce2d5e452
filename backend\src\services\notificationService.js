const {
  sendBulkEmailNotification,
  getNotificationRecipients,
  logNotificationAttempt,
  getUserEmail,
  NOTIFICATION_CONFIG
} = require('./emailNotificationService');

const {
  generateDocumentSignedNotification,
  generateDocumentUploadedNotification,
  generateDocumentApprovedNotification,
  generateDocumentRejectedNotification,
  validateNotificationContent
} = require('./notificationContentService');

const { query } = require('../models/database');

/**
 * Send document signed notification
 */
const sendDocumentSignedNotification = async (documentData, userData) => {
  try {
    console.log('📧 Preparing document signed notification...', {
      documentId: documentData.id,
      userId: userData.id,
      enabled: NOTIFICATION_CONFIG.enabled
    });

    if (!NOTIFICATION_CONFIG.enabled) {
      console.log('Email notifications disabled - skipping');
      return { success: false, reason: 'disabled' };
    }

    // Check if user has email notifications enabled
    const userPrefs = await getUserNotificationPreferences(userData.id);
    if (!userPrefs.email_enabled || !userPrefs.document_signed) {
      console.log('User has disabled document signed notifications');
      return { success: false, reason: 'user_disabled' };
    }

    // Get notification recipients (user + admins)
    const recipients = await getNotificationRecipients(userData.id);

    if (recipients.length === 0) {
      console.log('No valid recipients found for email notification');
      return { success: false, reason: 'no_recipients' };
    }

    console.log(`Found ${recipients.length} notification recipients:`, recipients);

    // Generate notification content for user
    const userNotification = generateDocumentSignedNotification(documentData, userData, 'ar', false);

    // Validate content
    const validation = validateNotificationContent(userNotification);
    if (!validation.valid) {
      console.error('Invalid notification content:', validation.error);
      return { success: false, reason: 'invalid_content', error: validation.error };
    }

    // Send to user (if they have an email)
    const userEmail = await getUserEmail(userData.id);
    const results = [];

    if (userEmail && recipients.includes(userEmail)) {
      console.log('Sending user notification...');
      const userResult = await sendBulkEmailNotification([userEmail], userNotification.subject, userNotification.message);
      results.push({ type: 'user', ...userResult });
    }

    // Send admin notifications (if there are admin emails configured)
    const adminEmails = recipients.filter(email => email !== userEmail);
    if (adminEmails.length > 0) {
      console.log('Sending admin notifications...');
      const adminNotification = generateDocumentSignedNotification(documentData, userData, 'ar', true);
      const adminResult = await sendBulkEmailNotification(adminEmails, adminNotification.subject, adminNotification.message);
      results.push({ type: 'admin', ...adminResult });
    }

    // Calculate overall success
    const totalSent = results.reduce((sum, r) => sum + (r.totalSent || 0), 0);
    const totalFailed = results.reduce((sum, r) => sum + (r.totalFailed || 0), 0);
    const overallSuccess = totalSent > 0;

    // Log the notification attempt
    await logNotificationAttempt(
      userData.id,
      documentData.id,
      recipients,
      userNotification.message,
      {
        success: overallSuccess,
        totalSent,
        totalFailed,
        results
      }
    );

    console.log(`✅ Document signed notification completed:`, {
      success: overallSuccess,
      totalSent,
      totalFailed
    });

    return {
      success: overallSuccess,
      totalSent,
      totalFailed,
      results
    };

  } catch (error) {
    console.error('Error sending document signed notification:', error);
    
    // Log the failed attempt
    try {
      await logNotificationAttempt(
        userData.id,
        documentData.id,
        [],
        '',
        {
          success: false,
          error: error.message,
          stack: error.stack
        }
      );
    } catch (logError) {
      console.error('Failed to log notification error:', logError);
    }

    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Send document uploaded notification (optional)
 */
const sendDocumentUploadedNotification = async (documentData, userData) => {
  try {
    if (!NOTIFICATION_CONFIG.enabled) {
      return { success: false, reason: 'disabled' };
    }

    // Check if user has upload notifications enabled
    const userPrefs = await getUserNotificationPreferences(userData.id);
    if (!userPrefs.email_enabled || !userPrefs.document_uploaded) {
      return { success: false, reason: 'user_disabled' };
    }

    const recipients = await getNotificationRecipients(userData.id);
    if (recipients.length === 0) {
      return { success: false, reason: 'no_recipients' };
    }

    const notification = generateDocumentUploadedNotification(documentData, userData, 'ar');
    const validation = validateNotificationContent(notification);

    if (!validation.valid) {
      return { success: false, reason: 'invalid_content', error: validation.error };
    }

    const result = await sendBulkEmailNotification(recipients, notification.subject, notification.message);

    await logNotificationAttempt(
      userData.id,
      documentData.id,
      recipients,
      notification.message,
      result
    );

    return result;

  } catch (error) {
    console.error('Error sending document uploaded notification:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get user notification preferences
 */
const getUserNotificationPreferences = async (userId) => {
  try {
    const result = await query(
      `SELECT email_notifications_enabled, notification_preferences
       FROM users WHERE id = $1`,
      [userId]
    );

    if (result.rows.length === 0) {
      return {
        email_enabled: true, // Default to enabled
        document_signed: true,
        document_uploaded: false,
        admin_notifications: true
      };
    }

    const user = result.rows[0];
    const prefs = user.notification_preferences || {};

    return {
      email_enabled: user.email_notifications_enabled !== false,
      document_signed: prefs.document_signed !== false,
      document_uploaded: prefs.document_uploaded === true,
      admin_notifications: prefs.admin_notifications !== false
    };

  } catch (error) {
    console.error('Error getting user notification preferences:', error);
    return {
      email_enabled: true, // Default to enabled on error
      document_signed: true,
      document_uploaded: false,
      admin_notifications: true
    };
  }
};

/**
 * Get user email (alias for getUserEmail for backward compatibility)
 */
const getUserPhoneNumber = getUserEmail;

/**
 * Test notification system
 */
const testNotificationSystem = async (userId, testMessage = null) => {
  try {
    const recipients = await getNotificationRecipients(userId);
    
    if (recipients.length === 0) {
      return { success: false, reason: 'no_recipients' };
    }

    const message = testMessage || `🧪 اختبار نظام الإشعارات

هذه رسالة اختبار للتأكد من عمل نظام الإشعارات الإلكترونية بشكل صحيح.

التاريخ: ${new Date().toLocaleDateString('en-US')}
الوقت: ${new Date().toLocaleTimeString('en-US')}

نظام التوقيع الإلكتروني العربي 🇸🇦`;

    const result = await sendBulkEmailNotification(recipients, 'اختبار نظام الإشعارات', message);
    
    await logNotificationAttempt(
      userId,
      null, // No document for test
      recipients,
      message,
      { ...result, test: true }
    );

    return result;

  } catch (error) {
    console.error('Error testing notification system:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Send document approved notification to user
 */
const sendDocumentApprovedNotification = async (documentData, userData) => {
  try {
    console.log('📧 Preparing document approved notification...', {
      documentId: documentData.id,
      userId: userData.id,
      enabled: NOTIFICATION_CONFIG.enabled
    });

    if (!NOTIFICATION_CONFIG.enabled) {
      console.log('Email notifications disabled - skipping');
      return { success: false, reason: 'disabled' };
    }

    // Check if user has email notifications enabled
    const userPrefs = await getUserNotificationPreferences(userData.id);
    if (!userPrefs.email_enabled || !userPrefs.document_signed) {
      console.log('User has disabled document approved notifications');
      return { success: false, reason: 'user_disabled' };
    }

    // Get user's phone number
    const userPhone = await getUserPhoneNumber(userData.id);
    if (!userPhone) {
      console.log('User phone number not found');
      return { success: false, reason: 'no_phone' };
    }

    const recipients = [userPhone];
    console.log(`Found ${recipients.length} notification recipients:`, recipients);

    // Generate notification content for user
    const notification = generateDocumentApprovedNotification(documentData, userData, 'ar');

    // Validate content
    const validation = validateNotificationContent(notification);
    if (!validation.valid) {
      console.error('Invalid notification content:', validation.error);
      return { success: false, reason: 'invalid_content', error: validation.error };
    }

    const result = await sendBulkEmailNotification(recipients, notification.subject, notification.message);

    await logNotificationAttempt(
      userData.id,
      documentData.id,
      recipients,
      notification.message,
      result
    );

    return result;

  } catch (error) {
    console.error('Error sending document approved notification:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Send document rejected notification to user
 */
const sendDocumentRejectedNotification = async (documentData, userData, rejectionReason) => {
  try {
    console.log('📧 Preparing document rejected notification...', {
      documentId: documentData.id,
      userId: userData.id,
      enabled: NOTIFICATION_CONFIG.enabled
    });

    if (!NOTIFICATION_CONFIG.enabled) {
      console.log('Email notifications disabled - skipping');
      return { success: false, reason: 'disabled' };
    }

    // Check if user has email notifications enabled
    const userPrefs = await getUserNotificationPreferences(userData.id);
    if (!userPrefs.email_enabled || !userPrefs.document_signed) {
      console.log('User has disabled document rejected notifications');
      return { success: false, reason: 'user_disabled' };
    }

    // Get user's phone number
    const userPhone = await getUserPhoneNumber(userData.id);
    if (!userPhone) {
      console.log('User phone number not found');
      return { success: false, reason: 'no_phone' };
    }

    const recipients = [userPhone];
    console.log(`Found ${recipients.length} notification recipients:`, recipients);

    // Generate notification content for user
    const notification = generateDocumentRejectedNotification(documentData, userData, rejectionReason, 'ar');

    // Validate content
    const validation = validateNotificationContent(notification);
    if (!validation.valid) {
      console.error('Invalid notification content:', validation.error);
      return { success: false, reason: 'invalid_content', error: validation.error };
    }

    const result = await sendBulkEmailNotification(recipients, notification.subject, notification.message);

    await logNotificationAttempt(
      userData.id,
      documentData.id,
      recipients,
      notification.message,
      result
    );

    return result;

  } catch (error) {
    console.error('Error sending document rejected notification:', error);
    return { success: false, error: error.message };
  }
};

module.exports = {
  sendDocumentSignedNotification,
  sendDocumentUploadedNotification,
  sendDocumentApprovedNotification,
  sendDocumentRejectedNotification,
  getUserNotificationPreferences,
  getUserPhoneNumber,
  testNotificationSystem
};
