import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { AuthProvider, useAuth } from './services/AuthContext';
import { LanguageProvider } from './contexts/LanguageContext';
import { NetworkProvider } from './contexts/NetworkContext';
import ProtectedRoute from './components/ProtectedRoute';
import AdminRoute from './components/AdminRoute';
import Navbar from './components/Navbar';
import SessionTimeoutWarning from './components/SessionTimeoutWarning';
import Breadcrumb from './components/Breadcrumb';
import GlobalErrorBoundary from './components/GlobalErrorBoundary';
import NetworkStatus, { NetworkToast } from './components/NetworkStatus';

import Login from './pages/Login';
import Register from './pages/Register';
import Dashboard from './pages/Dashboard';
import DashboardAlt from './pages/DashboardAlt';
import DashboardMem0 from './pages/DashboardMem0';
import SignatureUpload from './pages/SignatureUpload';
import DocumentSigning from './pages/DocumentSigning';
import SigningConfirmation from './pages/SigningConfirmation';
import SigningError from './pages/SigningError';
import History from './pages/History';
import Users from './pages/Users';
import UserSettings from './pages/UserSettings';
import SerialVerification from './pages/SerialVerification';
import Mail from './pages/Mail';
import MyApprovedDocuments from './pages/MyApprovedDocuments';
import AdminDocumentSigning from './pages/AdminDocumentSigning';
import AdminRecords from './pages/AdminRecords';
import AdminAllApprovedDocuments from './pages/AdminAllApprovedDocuments';
import AdminGeolocationTracking from './pages/AdminGeolocationTracking';
import PDFViewerTest from './components/PDFViewerTest';
import SystemDocumentation from './pages/SystemDocumentation';

// Component to conditionally render Navbar based on authentication and route
const ConditionalNavbar: React.FC = () => {
  const { user } = useAuth();
  const location = useLocation();

  // Routes where navbar should not be shown (public routes and full layout routes)
  const publicRoutes = ['/login', '/register'];
  const fullLayoutRoutes = ['/dashboard-mem0'];
  const isPublicRoute = publicRoutes.includes(location.pathname);
  const isFullLayoutRoute = fullLayoutRoutes.includes(location.pathname);

  // Only show navbar if user is authenticated and not on a public route or full layout route
  if (!user || isPublicRoute || isFullLayoutRoute) {
    return null;
  }

  return <Navbar />;
};

// Component to conditionally render Breadcrumb based on authentication and route
const ConditionalBreadcrumb: React.FC = () => {
  const { user } = useAuth();
  const location = useLocation();

  // Routes where breadcrumb should not be shown (public routes and full layout routes)
  const publicRoutes = ['/login', '/register'];
  const fullLayoutRoutes = ['/dashboard-mem0'];
  const isPublicRoute = publicRoutes.includes(location.pathname);
  const isFullLayoutRoute = fullLayoutRoutes.includes(location.pathname);

  // Only show breadcrumb if user is authenticated and not on a public route or full layout route
  if (!user || isPublicRoute || isFullLayoutRoute) {
    return null;
  }

  return <Breadcrumb />;
};

// Component to conditionally style main container based on authentication and route
const ConditionalMain: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const location = useLocation();

  // Routes where different styling should be applied (public routes)
  const publicRoutes = ['/login', '/register'];
  const isPublicRoute = publicRoutes.includes(location.pathname);

  // Routes that have their own full layout (like Mem0 dashboard)
  const fullLayoutRoutes = ['/dashboard-mem0'];
  const isFullLayoutRoute = fullLayoutRoutes.includes(location.pathname);

  // Use different styling for public routes (full height, centered)
  if (!user || isPublicRoute) {
    return (
      <main className="min-h-screen flex items-center justify-center px-4">
        {children}
      </main>
    );
  }

  // Use no container for full layout routes
  if (isFullLayoutRoute) {
    return <>{children}</>;
  }

  // Use normal styling for authenticated routes
  return (
    <main className="container mx-auto px-4 py-8 overflow-x-hidden">
      {children}
    </main>
  );
};

function App() {
  useEffect(() => {
    // Set document direction to RTL for Arabic
    document.documentElement.dir = 'rtl';
    document.documentElement.lang = 'ar';

    // Load Almarai font from Google Fonts
    const link = document.createElement('link');
    link.href = 'https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap';
    link.rel = 'stylesheet';
    document.head.appendChild(link);
  }, []);

  return (
    <GlobalErrorBoundary>
      <LanguageProvider>
        <NetworkProvider>
          <AuthProvider>
          <Router
            future={{
              v7_startTransition: true,
              v7_relativeSplatPath: true
            }}
          >
            <div className="min-h-screen bg-gray-50 overflow-x-hidden" dir="rtl">
              <ConditionalNavbar />
              <SessionTimeoutWarning />
              <NetworkStatus />
              <NetworkToast />

              <ConditionalBreadcrumb />
              <ConditionalMain>
              <Routes>
                <Route path="/login" element={<Login />} />
                <Route path="/register" element={<Register />} />
                <Route path="/" element={<Navigate to="/dashboard" replace />} />
                <Route
                  path="/dashboard"
                  element={
                    <ProtectedRoute>
                      <Dashboard />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/dashboard-alt"
                  element={
                    <ProtectedRoute>
                      <DashboardAlt />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/dashboard-mem0"
                  element={
                    <ProtectedRoute>
                      <DashboardMem0 />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/signature-upload"
                  element={
                    <ProtectedRoute>
                      <SignatureUpload />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/document-signing"
                  element={
                    <AdminRoute requiredPermission="sign_documents">
                      <DocumentSigning />
                    </AdminRoute>
                  }
                />
                <Route
                  path="/sign"
                  element={
                    <AdminRoute requiredPermission="sign_documents">
                      <DocumentSigning />
                    </AdminRoute>
                  }
                />
                <Route
                  path="/signing-confirmation/:documentId"
                  element={
                    <ProtectedRoute>
                      <SigningConfirmation />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/signing-error"
                  element={
                    <ProtectedRoute>
                      <SigningError />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/history"
                  element={
                    <AdminRoute requiredPermission="view_history" requireAdminRole={true}>
                      <History />
                    </AdminRoute>
                  }
                />
                <Route
                  path="/documents"
                  element={
                    <ProtectedRoute>
                      <History />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/mail"
                  element={
                    <ProtectedRoute>
                      <Mail />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/my-approved-documents"
                  element={
                    <ProtectedRoute>
                      <MyApprovedDocuments />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/admin/document-signing"
                  element={
                    <AdminRoute requiredPermission="sign_documents">
                      <AdminDocumentSigning />
                    </AdminRoute>
                  }
                />
                <Route
                  path="/admin/records"
                  element={
                    <AdminRoute requireAdminRole={true} requiredPermission="view_history">
                      <AdminRecords />
                    </AdminRoute>
                  }
                />
                <Route
                  path="/admin/all-approved-documents"
                  element={
                    <AdminRoute requireAdminRole={true} requiredPermission="view_history">
                      <AdminAllApprovedDocuments />
                    </AdminRoute>
                  }
                />
                <Route
                  path="/users"
                  element={
                    <AdminRoute requiredPermission="manage_users" requireAdminRole={true}>
                      <Users />
                    </AdminRoute>
                  }
                />
                <Route
                  path="/admin/geolocation-tracking"
                  element={
                    <AdminRoute requiredPermission="track_user_locations" requireAdminRole={true}>
                      <AdminGeolocationTracking />
                    </AdminRoute>
                  }
                />
                <Route
                  path="/settings"
                  element={
                    <ProtectedRoute>
                      <UserSettings />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/verify"
                  element={
                    <SerialVerification />
                  }
                />
                <Route
                  path="/pdf-test"
                  element={
                    <ProtectedRoute>
                      <PDFViewerTest />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/documentation"
                  element={
                    <AdminRoute requiredPermission="view_dashboard" requireAdminRole={true}>
                      <SystemDocumentation />
                    </AdminRoute>
                  }
                />
              </Routes>
              </ConditionalMain>
          </div>
          </Router>
          </AuthProvider>
        </NetworkProvider>
      </LanguageProvider>
    </GlobalErrorBoundary>
  );
}

export default App;
