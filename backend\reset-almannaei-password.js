const bcrypt = require('bcryptjs');
const { query } = require('./src/models/database');

async function resetAlmannaeiPassword() {
  try {
    console.log('🔄 Resetting <NAME_EMAIL>...\n');

    const email = '<EMAIL>';
    const newPassword = 'password123';

    // Check if user exists
    const userCheck = await query('SELECT id, email, role FROM users WHERE email = $1', [email]);
    
    if (userCheck.rows.length === 0) {
      console.log(`❌ User with email "${email}" not found`);
      console.log('Creating the user...');
      
      // Create the user
      const hashedPassword = await bcrypt.hash(newPassword, 12);
      const createResult = await query(
        `INSERT INTO users (email, password_hash, role, language, text_direction, created_at, updated_at) 
         VALUES ($1, $2, $3, $4, $5, NOW(), NOW()) 
         RETURNING id, email, role`,
        [email, hashedPassword, 'admin', 'ar', 'rtl']
      );
      
      console.log('✅ User created successfully!');
      console.log(`📧 Email: ${createResult.rows[0].email}`);
      console.log(`🔐 Role: ${createResult.rows[0].role}`);
      console.log(`🔑 Password: ${newPassword}`);
      
    } else {
      const user = userCheck.rows[0];
      console.log(`📧 Found user: ${user.email}`);
      console.log(`🔐 Current role: ${user.role}`);
      
      // Hash new password
      const hashedPassword = await bcrypt.hash(newPassword, 12);
      
      // Update password and ensure admin role
      const updateResult = await query(
        `UPDATE users 
         SET password_hash = $1, role = $2, updated_at = NOW() 
         WHERE email = $3 
         RETURNING id, email, role`,
        [hashedPassword, 'admin', email]
      );
      
      if (updateResult.rows.length > 0) {
        console.log('✅ Password and role updated successfully!');
        console.log(`📧 Email: ${updateResult.rows[0].email}`);
        console.log(`🔐 Role: ${updateResult.rows[0].role}`);
        console.log(`🔑 New password: ${newPassword}`);
      } else {
        console.log('❌ Failed to update password');
      }
    }

    console.log('\n🎯 You can now login with:');
    console.log(`   Email: ${email}`);
    console.log(`   Password: ${newPassword}`);

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

// Run the script
resetAlmannaeiPassword();
