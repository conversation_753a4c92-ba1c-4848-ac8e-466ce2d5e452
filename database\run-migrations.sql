-- Combined migration script for password history and session management
-- This script combines both migrations into a single file

-- Migration 1: Password History
-- Description: Add table to track password history and prevent reuse

-- Create password history table
CREATE TABLE IF NOT EXISTS password_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    CONSTRAINT idx_password_history_user_created UNIQUE (user_id, created_at)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_password_history_user_id ON password_history(user_id);
CREATE INDEX IF NOT EXISTS idx_password_history_created_at ON password_history(created_at);

-- Add password change tracking to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS password_changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS password_change_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_password_check TIMESTAMP;

-- Create function to manage password history
CREATE OR REPLACE FUNCTION manage_password_history()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert old password into history when password changes
    IF TG_OP = 'UPDATE' AND OLD.password_hash != NEW.password_hash THEN
        INSERT INTO password_history (user_id, password_hash, created_at)
        VALUES (OLD.id, OLD.password_hash, OLD.password_changed_at);
        
        -- Update password change tracking
        NEW.password_changed_at = CURRENT_TIMESTAMP;
        NEW.password_change_count = COALESCE(OLD.password_change_count, 0) + 1;
        
        -- Clean up old password history (keep only last 5)
        DELETE FROM password_history 
        WHERE user_id = NEW.id 
        AND id NOT IN (
            SELECT id FROM password_history 
            WHERE user_id = NEW.id 
            ORDER BY created_at DESC 
            LIMIT 5
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for password history management
DROP TRIGGER IF EXISTS trigger_manage_password_history ON users;
CREATE TRIGGER trigger_manage_password_history
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION manage_password_history();

-- Migration 2: Session Management
-- Description: Add comprehensive session management and security tracking

-- Create user sessions table
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) NOT NULL UNIQUE,
    refresh_token VARCHAR(255),
    device_info JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT true,
    logout_reason VARCHAR(100),
    security_flags JSONB DEFAULT '{}'
);

-- Create session activity table for detailed logging
CREATE TABLE IF NOT EXISTS session_activity (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES user_sessions(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    activity_type VARCHAR(50) NOT NULL,
    activity_details JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    risk_score INTEGER DEFAULT 0,
    location_info JSONB DEFAULT '{}'
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON user_sessions(is_active, expires_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_last_activity ON user_sessions(last_activity);

CREATE INDEX IF NOT EXISTS idx_session_activity_session_id ON session_activity(session_id);
CREATE INDEX IF NOT EXISTS idx_session_activity_user_id ON session_activity(user_id);
CREATE INDEX IF NOT EXISTS idx_session_activity_timestamp ON session_activity(timestamp);
CREATE INDEX IF NOT EXISTS idx_session_activity_type ON session_activity(activity_type);

-- Function to clean up expired sessions
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    cleaned_count INTEGER;
BEGIN
    -- Mark expired sessions as inactive
    UPDATE user_sessions 
    SET is_active = false, 
        logout_reason = 'expired'
    WHERE expires_at < CURRENT_TIMESTAMP 
    AND is_active = true;
    
    GET DIAGNOSTICS cleaned_count = ROW_COUNT;
    
    -- Log cleanup activity
    INSERT INTO session_activity (
        session_id, user_id, activity_type, activity_details
    )
    SELECT 
        id, user_id, 'session_expired', 
        jsonb_build_object('cleanup_time', CURRENT_TIMESTAMP, 'reason', 'automatic_cleanup')
    FROM user_sessions 
    WHERE expires_at < CURRENT_TIMESTAMP 
    AND is_active = false 
    AND logout_reason = 'expired';
    
    RETURN cleaned_count;
END;
$$ LANGUAGE plpgsql;

-- Function to enforce concurrent session limits
CREATE OR REPLACE FUNCTION enforce_session_limits()
RETURNS TRIGGER AS $$
DECLARE
    session_count INTEGER;
    max_sessions INTEGER := 3; -- Maximum concurrent sessions per user
BEGIN
    -- Count active sessions for this user
    SELECT COUNT(*) INTO session_count
    FROM user_sessions 
    WHERE user_id = NEW.user_id 
    AND is_active = true 
    AND expires_at > CURRENT_TIMESTAMP;
    
    -- If limit exceeded, deactivate oldest sessions
    IF session_count >= max_sessions THEN
        UPDATE user_sessions 
        SET is_active = false, 
            logout_reason = 'session_limit_exceeded'
        WHERE user_id = NEW.user_id 
        AND is_active = true 
        AND id != NEW.id
        AND id IN (
            SELECT id FROM user_sessions 
            WHERE user_id = NEW.user_id 
            AND is_active = true 
            ORDER BY last_activity ASC 
            LIMIT (session_count - max_sessions + 1)
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for session limits
DROP TRIGGER IF EXISTS trigger_enforce_session_limits ON user_sessions;
CREATE TRIGGER trigger_enforce_session_limits
    AFTER INSERT ON user_sessions
    FOR EACH ROW
    EXECUTE FUNCTION enforce_session_limits();

-- Function to update session activity
CREATE OR REPLACE FUNCTION update_session_activity()
RETURNS TRIGGER AS $$
BEGIN
    -- Update last activity timestamp
    NEW.last_activity = CURRENT_TIMESTAMP;
    
    -- Log the activity
    INSERT INTO session_activity (
        session_id, user_id, activity_type, activity_details, ip_address, user_agent
    ) VALUES (
        NEW.id, NEW.user_id, 'session_updated', 
        jsonb_build_object('previous_activity', OLD.last_activity),
        NEW.ip_address, NEW.user_agent
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for session activity updates
DROP TRIGGER IF EXISTS trigger_update_session_activity ON user_sessions;
CREATE TRIGGER trigger_update_session_activity
    BEFORE UPDATE ON user_sessions
    FOR EACH ROW
    WHEN (OLD.last_activity IS DISTINCT FROM NEW.last_activity)
    EXECUTE FUNCTION update_session_activity();

-- Create function to get password history info
CREATE OR REPLACE FUNCTION get_password_history_info(p_user_id UUID)
RETURNS TABLE(
    total_passwords INTEGER,
    oldest_password_date TIMESTAMP,
    newest_password_date TIMESTAMP,
    can_change_password BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::INTEGER as total_passwords,
        MIN(created_at) as oldest_password_date,
        MAX(created_at) as newest_password_date,
        (CURRENT_TIMESTAMP - MAX(u.password_changed_at) > INTERVAL '24 hours') as can_change_password
    FROM password_history ph
    RIGHT JOIN users u ON u.id = p_user_id
    WHERE ph.user_id = p_user_id OR ph.user_id IS NULL;
END;
$$ LANGUAGE plpgsql;

-- Create function to check password reuse
CREATE OR REPLACE FUNCTION check_password_reuse(p_user_id UUID, p_password_hash VARCHAR)
RETURNS BOOLEAN AS $$
DECLARE
    reuse_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO reuse_count
    FROM password_history 
    WHERE user_id = p_user_id 
    AND password_hash = p_password_hash;
    
    RETURN reuse_count > 0;
END;
$$ LANGUAGE plpgsql;

-- Create function to cleanup old password history
CREATE OR REPLACE FUNCTION cleanup_old_password_history()
RETURNS INTEGER AS $$
DECLARE
    cleaned_count INTEGER;
BEGIN
    -- Remove password history older than 1 year
    DELETE FROM password_history 
    WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '1 year';
    
    GET DIAGNOSTICS cleaned_count = ROW_COUNT;
    RETURN cleaned_count;
END;
$$ LANGUAGE plpgsql;

-- Insert initial password history for existing users
INSERT INTO password_history (user_id, password_hash, created_at)
SELECT id, password_hash, COALESCE(password_changed_at, created_at)
FROM users 
WHERE id NOT IN (SELECT DISTINCT user_id FROM password_history WHERE user_id IS NOT NULL)
ON CONFLICT DO NOTHING;

-- Update password change tracking for existing users
UPDATE users 
SET password_changed_at = COALESCE(password_changed_at, created_at),
    password_change_count = COALESCE(password_change_count, 1)
WHERE password_changed_at IS NULL OR password_change_count IS NULL;

-- Success message
SELECT 'Security migrations completed successfully!' as result;
