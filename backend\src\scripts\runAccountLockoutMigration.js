const { query } = require('../models/database');
const fs = require('fs').promises;
const path = require('path');

/**
 * Run the account lockout migration
 */
async function runAccountLockoutMigration() {
  console.log('🔒 Running Account Lockout Migration...\n');

  try {
    // Read the migration file
    const migrationPath = path.join(__dirname, '../../..', 'database', 'migrations', '012_add_account_lockout.sql');
    const migrationSQL = await fs.readFile(migrationPath, 'utf8');

    console.log('📄 Migration file loaded successfully');
    console.log('🔧 Executing migration...\n');

    // Execute the migration
    await query(migrationSQL);

    console.log('✅ Account lockout migration completed successfully!');
    console.log('\n📋 Migration Summary:');
    console.log('   • Added failed_login_attempts column to users table');
    console.log('   • Added locked_until column to users table');
    console.log('   • Created indexes for efficient lockout queries');
    console.log('   • Added unlock_expired_accounts() function');
    console.log('   • Updated existing users with 0 failed attempts');

    // Verify the migration
    console.log('\n🔍 Verifying migration...');
    
    const verifyColumns = await query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      AND column_name IN ('failed_login_attempts', 'locked_until')
      ORDER BY column_name;
    `);

    if (verifyColumns.rows.length === 2) {
      console.log('✅ Migration verification successful!');
      console.log('\n📊 New columns added:');
      verifyColumns.rows.forEach(col => {
        console.log(`   • ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable}, default: ${col.column_default})`);
      });
    } else {
      console.log('⚠️  Migration verification failed - columns not found');
    }

    // Check if function was created
    const verifyFunction = await query(`
      SELECT routine_name, routine_type 
      FROM information_schema.routines 
      WHERE routine_name = 'unlock_expired_accounts';
    `);

    if (verifyFunction.rows.length > 0) {
      console.log('✅ unlock_expired_accounts() function created successfully');
    } else {
      console.log('⚠️  unlock_expired_accounts() function not found');
    }

    console.log('\n🎉 Account lockout security feature is now active!');
    console.log('\n📝 Next Steps:');
    console.log('1. Restart the application to use the new security features');
    console.log('2. Test the account lockout functionality');
    console.log('3. Monitor failed login attempts in the logs');
    console.log('4. Consider setting up a cron job to run unlock_expired_accounts() periodically');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    
    if (error.message.includes('already exists')) {
      console.log('\n💡 It looks like this migration has already been run.');
      console.log('   This is normal if you\'ve run this script before.');
    } else {
      console.log('\n🔧 Troubleshooting:');
      console.log('1. Check database connection');
      console.log('2. Ensure you have proper database permissions');
      console.log('3. Verify the migration file exists');
      console.log('4. Check for any syntax errors in the SQL');
    }
    
    process.exit(1);
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  runAccountLockoutMigration()
    .then(() => {
      console.log('\n✨ Migration completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Migration failed:', error);
      process.exit(1);
    });
}

module.exports = { runAccountLockoutMigration };
