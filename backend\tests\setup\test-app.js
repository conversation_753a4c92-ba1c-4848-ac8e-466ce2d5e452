// Test app wrapper to handle server lifecycle properly
const app = require('../../src/app');

// Track server instances for cleanup
const servers = new Set();

// Wrap the app to track server creation
const originalListen = app.listen;
app.listen = function(...args) {
  const server = originalListen.apply(this, args);
  servers.add(server);
  
  // Override close to remove from tracking
  const originalClose = server.close;
  server.close = function(callback) {
    servers.delete(server);
    return originalClose.call(this, callback);
  };
  
  return server;
};

// Global cleanup function
const closeAllServers = () => {
  return Promise.all(
    Array.from(servers).map(server => {
      return new Promise((resolve) => {
        if (server.listening) {
          server.close(() => resolve());
        } else {
          resolve();
        }
      });
    })
  );
};

// Cleanup on process exit
process.on('exit', () => {
  servers.forEach(server => {
    if (server.listening) {
      server.close();
    }
  });
});

module.exports = {
  app,
  closeAllServers
};
