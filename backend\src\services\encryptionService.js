const crypto = require('crypto-js');
const fs = require('fs').promises;
const fsSync = require('fs');
const path = require('path');
const { pipeline } = require('stream');
const { promisify } = require('util');
const pipelineAsync = promisify(pipeline);

// Ensure uploads directory exists
const UPLOADS_DIR = path.join(__dirname, '../../uploads');
const SIGNATURES_DIR = path.join(UPLOADS_DIR, 'signatures');
const DOCUMENTS_DIR = path.join(UPLOADS_DIR, 'documents');

const ensureDirectoryExists = async (dirPath) => {
  try {
    await fs.access(dirPath);
  } catch (error) {
    await fs.mkdir(dirPath, { recursive: true });
  }
};

const encryptData = (data) => {
  const encryptionKey = process.env.ENCRYPTION_KEY;
  if (!encryptionKey) {
    throw new Error('Encryption key not configured');
  }
  return crypto.AES.encrypt(data, encryptionKey).toString();
};

const decryptData = (encryptedData) => {
  const encryptionKey = process.env.ENCRYPTION_KEY;
  if (!encryptionKey) {
    throw new Error('Encryption key not configured');
  }
  const bytes = crypto.AES.decrypt(encryptedData, encryptionKey);
  return bytes.toString(crypto.enc.Utf8);
};

const saveFile = async (fileData, fileName, fileType = 'documents') => {
  const targetDir = fileType === 'signatures' ? SIGNATURES_DIR : DOCUMENTS_DIR;
  await ensureDirectoryExists(targetDir);

  const filePath = path.join(targetDir, fileName);
  await fs.writeFile(filePath, fileData);

  return filePath;
};

const readFile = async (filePath) => {
  return await fs.readFile(filePath);
};

const deleteFile = async (filePath) => {
  try {
    await fs.unlink(filePath);
    return true;
  } catch (error) {
    console.error('Error deleting file:', error);
    return false;
  }
};

// Streaming file save for large files
const saveFileStream = async (fileBuffer, fileName, fileType = 'documents') => {
  const targetDir = fileType === 'signatures' ? SIGNATURES_DIR : DOCUMENTS_DIR;
  await ensureDirectoryExists(targetDir);

  const filePath = path.join(targetDir, fileName);

  try {
    // For very large files, use streaming write
    if (fileBuffer.length > 50 * 1024 * 1024) { // 50MB threshold
      const writeStream = fsSync.createWriteStream(filePath);
      const chunkSize = 1024 * 1024; // 1MB chunks

      for (let i = 0; i < fileBuffer.length; i += chunkSize) {
        const chunk = fileBuffer.slice(i, i + chunkSize);
        await new Promise((resolve, reject) => {
          writeStream.write(chunk, (error) => {
            if (error) reject(error);
            else resolve();
          });
        });
      }

      await new Promise((resolve, reject) => {
        writeStream.end((error) => {
          if (error) reject(error);
          else resolve();
        });
      });
    } else {
      // For smaller files, use regular write
      await fs.writeFile(filePath, fileBuffer);
    }

    return filePath;
  } catch (error) {
    console.error('Error saving file:', error);
    throw new Error(`Failed to save file: ${error.message}`);
  }
};

// Streaming encryption for large files
const encryptLargeData = async (data, chunkSize = 1024 * 1024) => {
  const encryptionKey = process.env.ENCRYPTION_KEY;
  if (!encryptionKey) {
    throw new Error('Encryption key not configured');
  }

  if (typeof data === 'string' && data.length < chunkSize) {
    // For small data, use regular encryption
    return crypto.AES.encrypt(data, encryptionKey).toString();
  }

  // For large data, encrypt in chunks
  const chunks = [];
  const dataString = typeof data === 'string' ? data : data.toString('base64');

  for (let i = 0; i < dataString.length; i += chunkSize) {
    const chunk = dataString.slice(i, i + chunkSize);
    const encryptedChunk = crypto.AES.encrypt(chunk, encryptionKey).toString();
    chunks.push(encryptedChunk);
  }

  return JSON.stringify(chunks);
};

// Streaming decryption for large files
const decryptLargeData = async (encryptedData) => {
  const encryptionKey = process.env.ENCRYPTION_KEY;
  if (!encryptionKey) {
    throw new Error('Encryption key not configured');
  }

  try {
    // Try to parse as chunked data
    const chunks = JSON.parse(encryptedData);
    if (Array.isArray(chunks)) {
      // Decrypt chunks
      const decryptedChunks = chunks.map(chunk => {
        const bytes = crypto.AES.decrypt(chunk, encryptionKey);
        return bytes.toString(crypto.enc.Utf8);
      });
      return decryptedChunks.join('');
    }
  } catch (error) {
    // Fall back to regular decryption
    const bytes = crypto.AES.decrypt(encryptedData, encryptionKey);
    return bytes.toString(crypto.enc.Utf8);
  }
};

const generateFileName = (originalName, userId, prefix = '') => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2);
  const extension = path.extname(originalName);
  const baseName = path.basename(originalName, extension);

  return `${prefix}${userId}_${timestamp}_${random}_${baseName}${extension}`;
};

// Memory-efficient file reading for large files
const readFileStream = async (filePath, maxChunkSize = 10 * 1024 * 1024) => {
  try {
    const stats = await fs.stat(filePath);

    if (stats.size > maxChunkSize) {
      // For large files, read in chunks
      const chunks = [];
      const readStream = fsSync.createReadStream(filePath, { highWaterMark: maxChunkSize });

      return new Promise((resolve, reject) => {
        readStream.on('data', (chunk) => {
          chunks.push(chunk);
        });

        readStream.on('end', () => {
          resolve(Buffer.concat(chunks));
        });

        readStream.on('error', reject);
      });
    } else {
      // For smaller files, read normally
      return await fs.readFile(filePath);
    }
  } catch (error) {
    console.error('Error reading file:', error);
    throw new Error(`Failed to read file: ${error.message}`);
  }
};

module.exports = {
  encryptData,
  decryptData,
  encryptLargeData,
  decryptLargeData,
  saveFile,
  saveFileStream,
  readFile,
  readFileStream,
  deleteFile,
  generateFileName,
  ensureDirectoryExists,
  UPLOADS_DIR,
  SIGNATURES_DIR,
  DOCUMENTS_DIR
};
