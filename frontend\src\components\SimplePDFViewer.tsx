import React, { useState, useEffect, useCallback } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';

// Import CSS files for react-pdf
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';

// Set up PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

interface SimplePDFViewerProps {
  isOpen: boolean;
  onClose: () => void;
  documentId: string;
  documentName: string;
  onError?: (error: string) => void;
}

const SimplePDFViewer: React.FC<SimplePDFViewerProps> = ({
  isOpen,
  onClose,
  documentId,
  documentName,
  onError
}) => {
  const [numPages, setNumPages] = useState<number>(0);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [scale, setScale] = useState<number>(1.0);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');

  // Fetch PDF data
  const fetchPDF = useCallback(async () => {
    if (!documentId) return;

    setLoading(true);
    setError('');

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('لا يوجد رمز مصادقة. يرجى تسجيل الدخول مرة أخرى.');
      }

      const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';
      const response = await fetch(`${API_BASE_URL}/documents/${documentId}/view`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/pdf'
        }
      });

      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          localStorage.removeItem('token');
          window.location.href = '/login';
          return;
        }
        const errorText = await response.text();
        throw new Error(`خطأ في الخادم: ${response.status} - ${errorText}`);
      }

      const blob = await response.blob();
      if (blob.size === 0) {
        throw new Error('المستند فارغ أو تالف.');
      }

      const objectUrl = URL.createObjectURL(blob);
      setPdfUrl(objectUrl);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [documentId, onError]);

  // Fetch PDF when modal opens
  useEffect(() => {
    if (isOpen && documentId) {
      fetchPDF();
    }

    // Cleanup
    return () => {
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl);
      }
    };
  }, [isOpen, documentId, fetchPDF, pdfUrl]);

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setPageNumber(1);
      setScale(1.0);
      setNumPages(0);
      setError('');
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl);
        setPdfUrl(null);
      }
    }
  }, [isOpen, pdfUrl]);

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
    setLoading(false);
  };

  const onDocumentLoadError = (error: Error) => {
    console.error('PDF load error:', error);
    setError('فشل في تحميل المستند. يرجى المحاولة مرة أخرى.');
    setLoading(false);
  };

  const goToPrevPage = () => setPageNumber(Math.max(1, pageNumber - 1));
  const goToNextPage = () => setPageNumber(Math.min(numPages, pageNumber + 1));
  const zoomIn = () => setScale(Math.min(3.0, scale + 0.2));
  const zoomOut = () => setScale(Math.max(0.5, scale - 0.2));
  const resetZoom = () => setScale(1.0);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-75 flex items-center justify-center" dir="rtl">
      <div className="bg-white w-11/12 h-5/6 max-w-6xl mx-4 rounded-lg shadow-xl flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-bold text-gray-900 font-['Almarai'] truncate flex-1 ml-4">
            عرض المستند: {documentName}
          </h2>
          
          {/* Controls */}
          <div className="flex items-center space-x-2 space-x-reverse">
            {numPages > 0 && (
              <>
                <button onClick={goToPrevPage} disabled={pageNumber <= 1} className="p-2 text-gray-600 hover:text-gray-900 disabled:opacity-50">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                
                <span className="text-sm text-gray-600 font-['Almarai'] px-2">
                  {pageNumber} من {numPages}
                </span>
                
                <button onClick={goToNextPage} disabled={pageNumber >= numPages} className="p-2 text-gray-600 hover:text-gray-900 disabled:opacity-50">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
                
                <div className="border-r border-gray-300 h-6 mx-2"></div>
                
                <button onClick={zoomOut} className="p-2 text-gray-600 hover:text-gray-900">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                  </svg>
                </button>
                
                <span className="text-sm text-gray-600 font-['Almarai'] px-2">
                  {Math.round(scale * 100)}%
                </span>
                
                <button onClick={zoomIn} className="p-2 text-gray-600 hover:text-gray-900">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                </button>
                
                <button onClick={resetZoom} className="p-2 text-gray-600 hover:text-gray-900">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                </button>
              </>
            )}
            
            <div className="border-r border-gray-300 h-6 mx-2"></div>
            
            <button onClick={onClose} className="p-2 text-gray-600 hover:text-gray-900">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* PDF Viewer */}
        <div className="flex-1 overflow-auto bg-gray-100 flex items-center justify-center p-4">
          {loading && (
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-600 font-['Almarai']">جاري تحميل المستند...</p>
            </div>
          )}

          {error && (
            <div className="text-center max-w-md">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2 font-['Almarai']">خطأ في تحميل المستند</h3>
              <p className="text-gray-600 font-['Almarai'] mb-4">{error}</p>
              <button
                onClick={fetchPDF}
                className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 font-['Almarai']"
              >
                إعادة المحاولة
              </button>
            </div>
          )}

          {pdfUrl && !error && !loading && (
            <Document
              file={pdfUrl}
              onLoadSuccess={onDocumentLoadSuccess}
              onLoadError={onDocumentLoadError}
              loading={
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
                  <p className="text-gray-600 font-['Almarai']">جاري معالجة المستند...</p>
                </div>
              }
              error={
                <div className="text-center">
                  <p className="text-red-600 font-['Almarai']">فشل في معالجة المستند</p>
                </div>
              }
            >
              <Page
                pageNumber={pageNumber}
                scale={scale}
                renderTextLayer={true}
                renderAnnotationLayer={true}
                className="shadow-lg"
              />
            </Document>
          )}
        </div>
      </div>
    </div>
  );
};

export default SimplePDFViewer;
