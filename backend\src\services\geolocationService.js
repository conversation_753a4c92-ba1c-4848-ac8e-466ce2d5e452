const axios = require('axios');

/**
 * Simplified Geolocation Service
 * Provides basic IP-to-location conversion only
 * Removed complex risk assessment, VPN detection, and tracking features
 */

class GeolocationService {
  constructor() {
    // Basic configuration for geolocation services
    this.ipGeolocationApiKey = process.env.IP_GEOLOCATION_API_KEY;
    this.fallbackService = process.env.GEOLOCATION_FALLBACK_SERVICE || 'ipapi';

    // Simple cache for location data
    this.locationCache = new Map();
    this.cacheTimeout = 30 * 60 * 1000; // 30 minutes
  }

  /**
   * Get basic location data from IP address
   * @param {string} ipAddress - Client IP address
   * @returns {Object} Basic location data
   */
  async getLocationFromIP(ipAddress) {
    try {
      // Check cache first
      const cacheKey = `ip_${ipAddress}`;
      const cached = this.locationCache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }

      let locationData = null;

      // Try primary service (IPGeolocation.io)
      if (this.ipGeolocationApiKey) {
        locationData = await this.getLocationFromIPGeolocation(ipAddress);
      }

      // Fallback to free service if primary fails
      if (!locationData) {
        locationData = await this.getLocationFromFallbackService(ipAddress);
      }

      // Cache the result
      if (locationData) {
        this.locationCache.set(cacheKey, {
          data: locationData,
          timestamp: Date.now()
        });
      }

      return locationData;
    } catch (error) {
      console.error('Error getting location from IP:', error);
      return null;
    }
  }

  /**
   * Get location from IPGeolocation.io service
   * @param {string} ipAddress - IP address to lookup
   * @returns {Object} Basic location data
   */
  async getLocationFromIPGeolocation(ipAddress) {
    try {
      const response = await axios.get(`https://api.ipgeolocation.io/ipgeo`, {
        params: {
          apiKey: this.ipGeolocationApiKey,
          ip: ipAddress,
          fields: 'country_code2,country_name,state_prov,city,latitude,longitude'
        },
        timeout: 5000
      });

      const data = response.data;
      return {
        country_code: data.country_code2,
        country_name: data.country_name,
        region: data.state_prov,
        city: data.city,
        latitude: parseFloat(data.latitude) || null,
        longitude: parseFloat(data.longitude) || null,
        source: 'ipgeolocation.io'
      };
    } catch (error) {
      console.error('IPGeolocation.io API error:', error.message);
      return null;
    }
  }

  /**
   * Get location from fallback service (ip-api.com - free)
   * @param {string} ipAddress - IP address to lookup
   * @returns {Object} Basic location data
   */
  async getLocationFromFallbackService(ipAddress) {
    try {
      const response = await axios.get(`http://ip-api.com/json/${ipAddress}`, {
        params: {
          fields: 'status,country,countryCode,region,regionName,city,lat,lon'
        },
        timeout: 5000
      });

      const data = response.data;
      if (data.status !== 'success') {
        return null;
      }

      return {
        country_code: data.countryCode,
        country_name: data.country,
        region: data.regionName,
        city: data.city,
        latitude: data.lat || null,
        longitude: data.lon || null,
        source: 'ip-api.com'
      };
    } catch (error) {
      console.error('Fallback geolocation API error:', error.message);
      return null;
    }
  }

  /**
   * Clear the location cache
   */
  clearCache() {
    this.locationCache.clear();
  }
}

module.exports = new GeolocationService();
