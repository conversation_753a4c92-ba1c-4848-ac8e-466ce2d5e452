const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

/**
 * Advanced File Content Validation Service
 * Provides deep content analysis beyond MIME type checking
 */

class FileValidationService {
  constructor() {
    // File signatures (magic numbers) for common file types
    this.fileSignatures = {
      pdf: [
        Buffer.from([0x25, 0x50, 0x44, 0x46]), // %PDF
      ],
      docx: [
        Buffer.from([0x50, 0x4B, 0x03, 0x04]), // ZIP signature (DOCX is ZIP-based)
        Buffer.from([0x50, 0x4B, 0x05, 0x06]), // ZIP signature variant
        Buffer.from([0x50, 0x4B, 0x07, 0x08]), // ZIP signature variant
      ],
      doc: [
        Buffer.from([0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1]), // MS Office signature
      ],
      png: [
        Buffer.from([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]), // PNG signature
      ],
      jpeg: [
        Buffer.from([0xFF, 0xD8, 0xFF]), // JPEG signature
      ],
      gif: [
        Buffer.from([0x47, 0x49, 0x46, 0x38, 0x37, 0x61]), // GIF87a
        Buffer.from([0x47, 0x49, 0x46, 0x38, 0x39, 0x61]), // GIF89a
      ]
    };

    // Suspicious patterns to detect
    this.suspiciousPatterns = [
      // Script injection patterns
      /<script[\s\S]*?>[\s\S]*?<\/script>/gi,
      /javascript:/gi,
      /vbscript:/gi,
      /data:text\/html/gi,
      /data:application\/javascript/gi,
      
      // Executable patterns
      /\.exe\b/gi,
      /\.bat\b/gi,
      /\.cmd\b/gi,
      /\.scr\b/gi,
      /\.pif\b/gi,
      /\.com\b/gi,
      
      // Suspicious URLs
      /https?:\/\/[^\s]+\.(?:exe|bat|cmd|scr|pif|com)/gi,
      
      // Embedded objects
      /<object[\s\S]*?>[\s\S]*?<\/object>/gi,
      /<embed[\s\S]*?>[\s\S]*?<\/embed>/gi,
      /<iframe[\s\S]*?>[\s\S]*?<\/iframe>/gi,
      
      // Macro patterns (for Office documents)
      /Auto_Open/gi,
      /Document_Open/gi,
      /Workbook_Open/gi,
      /Shell\(/gi,
      /CreateObject\(/gi,
    ];

    // Maximum file sizes by type (in bytes)
    this.maxFileSizes = {
      pdf: 100 * 1024 * 1024,    // 100MB
      docx: 50 * 1024 * 1024,    // 50MB
      doc: 50 * 1024 * 1024,     // 50MB
      png: 10 * 1024 * 1024,     // 10MB
      jpeg: 10 * 1024 * 1024,    // 10MB
      gif: 5 * 1024 * 1024,      // 5MB
    };
  }

  /**
   * Comprehensive file validation
   */
  async validateFile(file, options = {}) {
    const {
      checkContent = true,
      checkSignature = true,
      checkSize = true,
      allowedTypes = ['pdf', 'docx', 'doc', 'png', 'jpeg', 'gif'],
      customMaxSize = null
    } = options;

    const results = {
      isValid: true,
      errors: [],
      warnings: [],
      fileInfo: {
        originalName: file.originalname,
        size: file.size,
        mimeType: file.mimetype,
        detectedType: null,
        hash: null
      },
      securityChecks: {
        signatureValid: false,
        contentSafe: false,
        sizeValid: false,
        typeAllowed: false
      }
    };

    try {
      // Generate file hash for integrity checking
      if (file.buffer) {
        results.fileInfo.hash = crypto.createHash('sha256').update(file.buffer).digest('hex');
      }

      // 1. File signature validation
      if (checkSignature) {
        const signatureResult = this.validateFileSignature(file);
        results.fileInfo.detectedType = signatureResult.detectedType;
        results.securityChecks.signatureValid = signatureResult.isValid;
        
        if (!signatureResult.isValid) {
          results.errors.push('نوع الملف غير صالح أو تم تعديل الملف');
          results.isValid = false;
        }
      }

      // 2. File type validation
      const detectedType = results.fileInfo.detectedType || this.getTypeFromMime(file.mimetype);
      results.securityChecks.typeAllowed = allowedTypes.includes(detectedType);
      
      if (!results.securityChecks.typeAllowed) {
        results.errors.push(`نوع الملف غير مسموح: ${detectedType}`);
        results.isValid = false;
      }

      // 3. File size validation
      if (checkSize) {
        const maxSize = customMaxSize || this.maxFileSizes[detectedType] || 10 * 1024 * 1024;
        results.securityChecks.sizeValid = file.size <= maxSize;
        
        if (!results.securityChecks.sizeValid) {
          results.errors.push(`حجم الملف كبير جداً. الحد الأقصى: ${this.formatFileSize(maxSize)}`);
          results.isValid = false;
        }
      }

      // 4. Content security validation
      if (checkContent && file.buffer) {
        const contentResult = await this.validateFileContent(file.buffer, detectedType);
        results.securityChecks.contentSafe = contentResult.isSafe;
        
        if (!contentResult.isSafe) {
          results.errors.push(...contentResult.threats);
          results.isValid = false;
        }
        
        if (contentResult.warnings.length > 0) {
          results.warnings.push(...contentResult.warnings);
        }
      }

      // 5. Additional security checks
      await this.performAdditionalSecurityChecks(file, results);

    } catch (error) {
      console.error('File validation error:', error);
      results.errors.push('فشل في التحقق من الملف');
      results.isValid = false;
    }

    return results;
  }

  /**
   * Validate file signature (magic numbers)
   */
  validateFileSignature(file) {
    if (!file.buffer || file.buffer.length < 8) {
      return { isValid: false, detectedType: null };
    }

    const fileHeader = file.buffer.slice(0, 16);

    for (const [type, signatures] of Object.entries(this.fileSignatures)) {
      for (const signature of signatures) {
        if (fileHeader.slice(0, signature.length).equals(signature)) {
          return { isValid: true, detectedType: type };
        }
      }
    }

    return { isValid: false, detectedType: null };
  }

  /**
   * Validate file content for security threats
   */
  async validateFileContent(buffer, fileType) {
    const result = {
      isSafe: true,
      threats: [],
      warnings: []
    };

    try {
      // Convert buffer to string for pattern matching (first 64KB only for performance)
      const contentString = buffer.slice(0, 65536).toString('utf8', 0, Math.min(buffer.length, 65536));

      // Check for suspicious patterns
      for (const pattern of this.suspiciousPatterns) {
        const matches = contentString.match(pattern);
        if (matches) {
          result.threats.push(`تم اكتشاف محتوى مشبوه: ${matches[0].substring(0, 50)}...`);
          result.isSafe = false;
        }
      }

      // File-type specific checks
      switch (fileType) {
        case 'pdf':
          await this.validatePDFContent(buffer, result);
          break;
        case 'docx':
        case 'doc':
          await this.validateOfficeContent(buffer, result);
          break;
        default:
          break;
      }

      // Check for embedded files
      if (this.hasEmbeddedFiles(buffer)) {
        result.warnings.push('الملف يحتوي على ملفات مدمجة');
      }

      // Check for suspicious metadata
      const metadataCheck = this.checkMetadata(buffer);
      if (metadataCheck.suspicious) {
        result.warnings.push('البيانات الوصفية للملف تحتوي على معلومات مشبوهة');
      }

    } catch (error) {
      console.error('Content validation error:', error);
      result.warnings.push('لم يتم التمكن من فحص محتوى الملف بالكامل');
    }

    return result;
  }

  /**
   * PDF-specific content validation
   */
  async validatePDFContent(buffer, result) {
    const pdfString = buffer.toString('utf8');

    // Check for JavaScript in PDF
    if (pdfString.includes('/JavaScript') || pdfString.includes('/JS')) {
      result.threats.push('PDF يحتوي على JavaScript');
      result.isSafe = false;
    }

    // Check for forms
    if (pdfString.includes('/AcroForm') || pdfString.includes('/XFA')) {
      result.warnings.push('PDF يحتوي على نماذج تفاعلية');
    }

    // Check for external links
    if (pdfString.includes('/URI') || pdfString.includes('http://') || pdfString.includes('https://')) {
      result.warnings.push('PDF يحتوي على روابط خارجية');
    }
  }

  /**
   * Office document content validation
   */
  async validateOfficeContent(buffer, result) {
    const docString = buffer.toString('utf8');

    // Check for macros
    if (docString.includes('vbaProject') || docString.includes('macros')) {
      result.threats.push('المستند يحتوي على ماكرو');
      result.isSafe = false;
    }

    // Check for external references
    if (docString.includes('http://') || docString.includes('https://')) {
      result.warnings.push('المستند يحتوي على مراجع خارجية');
    }
  }

  /**
   * Check for embedded files
   */
  hasEmbeddedFiles(buffer) {
    const content = buffer.toString('utf8');
    return content.includes('application/') || 
           content.includes('Content-Type:') ||
           buffer.includes(Buffer.from([0x50, 0x4B])); // ZIP signature
  }

  /**
   * Check metadata for suspicious content
   */
  checkMetadata(buffer) {
    const content = buffer.toString('utf8');
    const suspicious = content.includes('<script>') ||
                      content.includes('javascript:') ||
                      content.includes('vbscript:');
    
    return { suspicious };
  }

  /**
   * Additional security checks
   */
  async performAdditionalSecurityChecks(file, results) {
    // Check filename for suspicious patterns
    const filename = file.originalname.toLowerCase();
    const suspiciousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com', '.vbs', '.js'];
    
    for (const ext of suspiciousExtensions) {
      if (filename.includes(ext)) {
        results.warnings.push(`اسم الملف يحتوي على امتداد مشبوه: ${ext}`);
      }
    }

    // Check for double extensions
    const extensionCount = (filename.match(/\./g) || []).length;
    if (extensionCount > 1) {
      results.warnings.push('اسم الملف يحتوي على امتدادات متعددة');
    }
  }

  /**
   * Get file type from MIME type
   */
  getTypeFromMime(mimeType) {
    const mimeMap = {
      'application/pdf': 'pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
      'application/msword': 'doc',
      'image/png': 'png',
      'image/jpeg': 'jpeg',
      'image/gif': 'gif'
    };
    
    return mimeMap[mimeType] || 'unknown';
  }

  /**
   * Format file size for display
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

module.exports = new FileValidationService();
