import React, { useState, useEffect } from 'react';
import { documentAPI } from '../services/api';
import { useLanguage } from '../contexts/LanguageContext';
import SimplePDFViewer from '../components/SimplePDFViewer';


interface Document {
  id: string;
  original_filename: string;
  signed_filename: string;
  serial_number: string;
  signed_date: string;
  file_size: number;
  status: string;
  signature_coordinates: any;
}

interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

const History: React.FC = () => {
  const { t } = useLanguage();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [dateFilter, setDateFilter] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [downloadingId, setDownloadingId] = useState<string | null>(null);
  const [viewerOpen, setViewerOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);


  useEffect(() => {
    fetchDocuments();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pagination.page, dateFilter, startDate, endDate]);

  const fetchDocuments = async () => {
    try {
      setLoading(true);
      // Fetch all documents without status filter
      const response = await documentAPI.getAll(
        pagination.page,
        pagination.limit
      );

      let filteredDocuments = response.data.documents;

      // Apply date filtering on client side
      if (dateFilter || (startDate && endDate)) {
        filteredDocuments = filterDocumentsByDate(filteredDocuments);
      }

      setDocuments(filteredDocuments);
      setPagination(response.data.pagination);
    } catch (error) {
      console.error('Error fetching documents:', error);
      setError(t.errors.loadFailed);
    } finally {
      setLoading(false);
    }
  };

  const downloadDocument = async (documentId: string, filename: string) => {
    try {
      setError(''); // Clear any previous errors
      setDownloadingId(documentId); // Set downloading state

      console.log(`Starting download for document: ${filename}`);

      const response = await documentAPI.download(documentId);

      // Validate response
      if (!response.data) {
        throw new Error('No data received from server');
      }

      // Create blob and download
      const blob = new Blob([response.data], { type: 'application/pdf' });

      // Check if blob is valid
      if (blob.size === 0) {
        throw new Error('Downloaded file is empty');
      }

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;

      // Add link to DOM, click it, then remove it
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the object URL
      window.URL.revokeObjectURL(url);

      console.log(`Successfully downloaded: ${filename} (${blob.size} bytes)`);

    } catch (error: any) {
      console.error('Download error:', error);

      // Provide specific error messages
      let errorMessage = t.errors.loadFailed;

      if (error.response?.status === 404) {
        errorMessage = t.errors.notFound;
      } else if (error.response?.status === 403) {
        errorMessage = t.errors.unauthorized;
      } else if (error.response?.status === 500) {
        errorMessage = t.errors.serverError;
      } else if (error.message.includes('Network Error')) {
        errorMessage = t.errors.networkError;
      } else if (error.message.includes('empty')) {
        errorMessage = 'الملف المحمل فارغ أو تالف';
      }

      setError(errorMessage);

      // Auto-clear error after 5 seconds
      setTimeout(() => setError(''), 5000);
    } finally {
      setDownloadingId(null); // Clear downloading state
    }
  };

  const viewDocument = (document: Document) => {
    console.log('History: Attempting to view document:', document);
    console.log('History: Document status:', document.status);

    if (document.status === 'signed') {
      console.log('History: Opening document viewer for:', document.id);
      setSelectedDocument(document);
      setViewerOpen(true);
    } else {
      console.log('History: Document not signed, cannot view');
      setError('يمكن عرض المستندات الموقعة فقط');
      setTimeout(() => setError(''), 3000);
    }
  };

  const closeViewer = () => {
    console.log('History: Closing document viewer');
    setViewerOpen(false);
    setSelectedDocument(null);
  };

  const handleViewerError = (error: string) => {
    console.error('History: DocumentViewer error:', error);
    setError(error);
    // Auto-clear error after 5 seconds
    setTimeout(() => setError(''), 5000);
  };

  const filterDocumentsByDate = (docs: Document[]) => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    return docs.filter(doc => {
      const docDate = new Date(doc.signed_date);

      if (dateFilter) {
        switch (dateFilter) {
          case 'today':
            const docToday = new Date(docDate.getFullYear(), docDate.getMonth(), docDate.getDate());
            return docToday.getTime() === today.getTime();

          case 'yesterday':
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            const docYesterday = new Date(docDate.getFullYear(), docDate.getMonth(), docDate.getDate());
            return docYesterday.getTime() === yesterday.getTime();

          case 'last7days':
            const sevenDaysAgo = new Date(today);
            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
            return docDate >= sevenDaysAgo;

          case 'last30days':
            const thirtyDaysAgo = new Date(today);
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            return docDate >= thirtyDaysAgo;

          case 'thisMonth':
            return docDate.getMonth() === now.getMonth() && docDate.getFullYear() === now.getFullYear();

          case 'lastMonth':
            const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
            const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
            return docDate >= lastMonth && docDate <= lastMonthEnd;

          default:
            return true;
        }
      }

      // Custom date range filtering
      if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        end.setHours(23, 59, 59, 999); // Include the entire end date
        return docDate >= start && docDate <= end;
      }

      return true;
    });
  };

  const clearDateFilter = () => {
    setDateFilter('');
    setStartDate('');
    setEndDate('');
  };



  const formatFileSize = (bytes: number | undefined) => {
    // Handle undefined, null, or invalid values
    if (bytes === undefined || bytes === null || isNaN(bytes) || bytes < 0) {
      return 'غير محدد';
    }

    if (bytes === 0) return '0 بايت';

    const k = 1024;
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    // Ensure we don't exceed the sizes array
    const sizeIndex = Math.min(i, sizes.length - 1);
    const formattedSize = parseFloat((bytes / Math.pow(k, sizeIndex)).toFixed(2));

    return `${formattedSize} ${sizes[sizeIndex]}`;
  };

  const formatDate = (dateString: string | undefined) => {
    // Handle undefined, null, or invalid date strings
    if (!dateString) {
      return 'غير محدد';
    }

    try {
      const date = new Date(dateString);

      // Check if the date is valid
      if (isNaN(date.getTime())) {
        return 'تاريخ غير صالح';
      }

      // Format date and time in English locale
      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
    } catch (error) {
      console.error('Date formatting error:', error);
      return 'تاريخ غير صالح';
    }
  };

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  if (loading && documents.length === 0) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">{t.history.title}</h1>
            <p className="text-gray-600 mt-1">{t.history.subtitle}</p>
          </div>

          {/* Date Filter */}
          <div className="flex items-center space-x-reverse space-x-4">
            {/* Date Filter */}
            <div className="flex items-center space-x-reverse space-x-2">
              <label htmlFor="date-filter" className="text-sm font-medium text-gray-700">
                تصفية بالتاريخ:
              </label>
              <select
                id="date-filter"
                value={dateFilter}
                onChange={(e) => {
                  setDateFilter(e.target.value);
                  if (e.target.value) {
                    setStartDate('');
                    setEndDate('');
                  }
                }}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 font-['Almarai']"
              >
                <option value="">الكل</option>
                <option value="today">اليوم</option>
                <option value="yesterday">أمس</option>
                <option value="last7days">آخر 7 أيام</option>
                <option value="last30days">آخر 30 يوم</option>
                <option value="thisMonth">هذا الشهر</option>
                <option value="lastMonth">الشهر الماضي</option>
              </select>
            </div>
          </div>
        </div>

        {/* Custom Date Range */}
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
            <label className="text-sm font-medium text-gray-700 font-['Almarai']">
              أو اختر نطاق تاريخ مخصص:
            </label>
            <div className="flex items-center gap-2">
              <div className="flex flex-col">
                <label className="text-xs text-gray-500 mb-1 font-['Almarai']">من تاريخ</label>
                <input
                  type="date"
                  value={startDate}
                  onChange={(e) => {
                    setStartDate(e.target.value);
                    if (e.target.value) setDateFilter('');
                  }}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm"
                />
              </div>
              <div className="flex flex-col">
                <label className="text-xs text-gray-500 mb-1 font-['Almarai']">إلى تاريخ</label>
                <input
                  type="date"
                  value={endDate}
                  onChange={(e) => {
                    setEndDate(e.target.value);
                    if (e.target.value) setDateFilter('');
                  }}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm"
                />
              </div>
              {(dateFilter || startDate || endDate) && (
                <button
                  onClick={clearDateFilter}
                  className="px-3 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200 text-sm font-['Almarai'] mt-6"
                >
                  مسح التصفية
                </button>
              )}
            </div>
          </div>
        </div>



        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}



        {documents.length === 0 ? (
          <div className="text-center py-12">
            <div className="mx-auto w-16 h-16 text-gray-400 mb-4">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <p className="text-gray-600 text-lg">{t.history.noDocuments}</p>
            <p className="text-sm text-gray-500 mt-1">
              {(dateFilter || startDate || endDate) ? 'لا توجد مستندات في النطاق الزمني المحدد' : t.history.startSigning}
            </p>
          </div>
        ) : (
          <>
            {/* Documents Table */}
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                      {t.history.document}
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t.history.serialNumber}
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t.history.signedDate}
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t.history.fileSize}
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الحالة
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t.history.actions}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {documents.map((document) => (
                    <tr key={document.id} className="hover:bg-gray-50">
                      <td className="px-3 py-4 whitespace-nowrap">
                        <div className="max-w-32">
                          <div className="text-sm font-medium text-gray-900 truncate" title={document.original_filename}>
                            {document.original_filename.length > 20
                              ? `${document.original_filename.substring(0, 17)}...`
                              : document.original_filename}
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 font-mono">
                          {document.serial_number}
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {formatDate(document.signed_date)}
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {formatFileSize(document.file_size)}
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          document.status === 'signed'
                            ? 'bg-green-100 text-green-800'
                            : document.status === 'pending'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {document.status === 'signed' ? 'موقع' : document.status === 'pending' ? 'في الانتظار' : 'فشل'}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          {/* View Document Button */}
                          <button
                            onClick={() => viewDocument(document)}
                            disabled={document.status !== 'signed'}
                            className={`inline-flex items-center justify-center w-8 h-8 border border-transparent rounded-md ${
                              document.status === 'signed'
                                ? 'text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                                : 'text-gray-400 bg-gray-100 cursor-not-allowed'
                            }`}
                            title={document.status === 'signed' ? 'عرض المستند' : 'يمكن عرض المستندات الموقعة فقط'}
                            aria-label={`عرض المستند ${document.original_filename}`}
                          >
                            <svg
                              className="w-4 h-4"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                              aria-hidden="true"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                              />
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                              />
                            </svg>
                          </button>



                          {/* Download Button */}
                          <button
                            onClick={() => downloadDocument(document.id, document.signed_filename)}
                            disabled={downloadingId === document.id || document.status !== 'signed'}
                            className={`inline-flex items-center justify-center w-8 h-8 border border-transparent rounded-md ${
                              downloadingId === document.id
                                ? 'text-gray-400 bg-gray-100 cursor-not-allowed'
                                : document.status === 'signed'
                                ? 'text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500'
                                : 'text-gray-400 bg-gray-100 cursor-not-allowed'
                            }`}
                            title={document.status === 'signed' ? 'تحميل المستند' : 'يمكن تحميل المستندات الموقعة فقط'}
                            aria-label={`تحميل المستند ${document.original_filename}`}
                          >
                            {downloadingId === document.id ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"></div>
                            ) : (
                              <svg
                                className="w-4 h-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                                aria-hidden="true"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                />
                              </svg>
                            )}
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <div className="flex items-center justify-between mt-6">
                <div className="text-sm text-gray-700">
                  عرض {((pagination.page - 1) * pagination.limit) + 1} إلى{' '}
                  {Math.min(pagination.page * pagination.limit, pagination.total)} {t.history.of}{' '}
                  {pagination.total} نتيجة
                </div>
                <div className="flex space-x-reverse space-x-2">
                  <button
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={pagination.page === 1}
                    className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {t.history.previous}
                  </button>

                  {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((page) => (
                    <button
                      key={page}
                      onClick={() => handlePageChange(page)}
                      className={`px-3 py-2 border rounded-md text-sm font-medium ${
                        page === pagination.page
                          ? 'bg-primary-500 text-white border-primary-500'
                          : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      {page}
                    </button>
                  ))}

                  <button
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={pagination.page === pagination.totalPages}
                    className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {t.history.next}
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Document Viewer Modal */}
      {selectedDocument && (
        <>
          {console.log('History: Rendering DocumentViewer', {
            selectedDocument: selectedDocument.id,
            viewerOpen,
            documentName: selectedDocument.original_filename
          })}
          <SimplePDFViewer
            isOpen={viewerOpen}
            onClose={closeViewer}
            documentId={selectedDocument.id}
            documentName={selectedDocument.original_filename}
            onError={handleViewerError}
          />
        </>
      )}


    </div>
  );
};

export default History;
