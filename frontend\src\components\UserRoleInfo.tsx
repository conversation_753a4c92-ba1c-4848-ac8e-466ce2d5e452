import React from 'react';
import { useAuth } from '../services/AuthContext';

const UserRoleInfo: React.FC = () => {
  const { user, isAdmin } = useAuth();

  if (!user) return null;

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-reverse space-x-3">
          <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
            isAdmin() ? 'bg-purple-100 text-purple-600' : 'bg-blue-100 text-blue-600'
          }`}>
            {isAdmin() ? (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            ) : (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            )}
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-900 font-['Almarai']">
              {user.email}
            </h3>
            <p className={`text-xs font-['Almarai'] ${
              isAdmin() ? 'text-purple-600' : 'text-blue-600'
            }`}>
              {isAdmin() ? '👑 مدير النظام' : '👤 مستخدم عادي'}
            </p>
          </div>
        </div>
        
        <div className="text-left">
          <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium font-['Almarai'] ${
            isAdmin() 
              ? 'bg-purple-100 text-purple-800' 
              : 'bg-blue-100 text-blue-800'
          }`}>
            {isAdmin() ? 'صلاحيات كاملة' : 'صلاحيات محدودة'}
          </div>
        </div>
      </div>
      
      {!isAdmin() && (
        <div className="mt-3 p-3 bg-amber-50 border border-amber-200 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-amber-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="mr-3">
              <h3 className="text-sm font-medium text-amber-800 font-['Almarai']">
                صلاحيات محدودة
              </h3>
              <div className="mt-2 text-sm text-amber-700 font-['Almarai']">
                <p>كمستخدم عادي، يمكنك:</p>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>عرض لوحة التحكم</li>
                  <li>تغيير كلمة المرور</li>
                  <li>عرض المستندات الخاصة بك</li>
                </ul>
                <p className="mt-2 text-xs">
                  للحصول على صلاحيات إضافية، يرجى التواصل مع مدير النظام.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserRoleInfo;
