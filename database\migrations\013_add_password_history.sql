-- Migration: Add password history tracking
-- Description: Add table to track password history and prevent reuse

-- Create password history table
CREATE TABLE IF NOT EXISTS password_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    CONSTRAINT idx_password_history_user_created UNIQUE (user_id, created_at)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_password_history_user_id ON password_history(user_id);
CREATE INDEX IF NOT EXISTS idx_password_history_created_at ON password_history(created_at);

-- Add password change tracking to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS password_changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS password_change_count INTEGER DEFAULT 0;

-- <PERSON><PERSON> function to manage password history
CREATE OR REPLACE FUNCTION manage_password_history()
<PERSON><PERSON><PERSON>NS TRIGGER AS $$
DECLARE
    history_limit INTEGER := 5; -- Keep last 5 passwords
    old_password_count INTEGER;
BEGIN
    -- Only process if password_hash has changed
    IF TG_OP = 'UPDATE' AND OLD.password_hash = NEW.password_hash THEN
        RETURN NEW;
    END IF;

    -- Insert old password into history (for updates)
    IF TG_OP = 'UPDATE' THEN
        INSERT INTO password_history (user_id, password_hash, created_at)
        VALUES (OLD.id, OLD.password_hash, OLD.password_changed_at);
    END IF;

    -- Update password change tracking
    NEW.password_changed_at = CURRENT_TIMESTAMP;
    NEW.password_change_count = COALESCE(OLD.password_change_count, 0) + 1;

    -- Clean up old password history (keep only last N passwords)
    DELETE FROM password_history 
    WHERE user_id = NEW.id 
    AND id NOT IN (
        SELECT id FROM password_history 
        WHERE user_id = NEW.id 
        ORDER BY created_at DESC 
        LIMIT history_limit
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for password history management
DROP TRIGGER IF EXISTS trigger_manage_password_history ON users;
CREATE TRIGGER trigger_manage_password_history
    BEFORE UPDATE OF password_hash ON users
    FOR EACH ROW
    EXECUTE FUNCTION manage_password_history();

-- Create function to check password reuse
CREATE OR REPLACE FUNCTION check_password_reuse(
    p_user_id UUID,
    p_new_password_hash VARCHAR(255),
    p_history_limit INTEGER DEFAULT 5
)
RETURNS BOOLEAN AS $$
DECLARE
    password_exists BOOLEAN := FALSE;
BEGIN
    -- Check if the new password hash matches any in the history
    SELECT EXISTS(
        SELECT 1 FROM password_history 
        WHERE user_id = p_user_id 
        AND password_hash = p_new_password_hash
        ORDER BY created_at DESC 
        LIMIT p_history_limit
    ) INTO password_exists;

    -- Also check current password
    IF NOT password_exists THEN
        SELECT EXISTS(
            SELECT 1 FROM users 
            WHERE id = p_user_id 
            AND password_hash = p_new_password_hash
        ) INTO password_exists;
    END IF;

    RETURN password_exists;
END;
$$ LANGUAGE plpgsql;

-- Create function to get password history info
CREATE OR REPLACE FUNCTION get_password_history_info(p_user_id UUID)
RETURNS TABLE(
    total_changes INTEGER,
    last_change TIMESTAMP,
    history_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.password_change_count,
        u.password_changed_at,
        (SELECT COUNT(*)::INTEGER FROM password_history WHERE user_id = p_user_id)
    FROM users u
    WHERE u.id = p_user_id;
END;
$$ LANGUAGE plpgsql;

-- Create function to clean up old password history
CREATE OR REPLACE FUNCTION cleanup_old_password_history(
    p_days_to_keep INTEGER DEFAULT 365
)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM password_history 
    WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '1 day' * p_days_to_keep;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    IF deleted_count > 0 THEN
        INSERT INTO logs (action, details) 
        VALUES ('PASSWORD_HISTORY_CLEANUP', jsonb_build_object('deleted_count', deleted_count));
    END IF;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Add comments for documentation
COMMENT ON TABLE password_history IS 'Stores password history to prevent password reuse';
COMMENT ON COLUMN password_history.user_id IS 'Reference to the user who owns this password history';
COMMENT ON COLUMN password_history.password_hash IS 'Hashed password (previous password)';
COMMENT ON COLUMN password_history.created_at IS 'When this password was replaced';

COMMENT ON FUNCTION manage_password_history() IS 'Automatically manages password history when passwords are changed';
COMMENT ON FUNCTION check_password_reuse(UUID, VARCHAR, INTEGER) IS 'Checks if a password has been used recently';
COMMENT ON FUNCTION get_password_history_info(UUID) IS 'Gets password change statistics for a user';
COMMENT ON FUNCTION cleanup_old_password_history(INTEGER) IS 'Cleans up old password history entries';

-- Initialize password change tracking for existing users
UPDATE users 
SET password_changed_at = created_at,
    password_change_count = 1
WHERE password_changed_at IS NULL;

-- Create a scheduled cleanup job (commented out - requires pg_cron extension)
-- SELECT cron.schedule('cleanup-password-history', '0 2 * * 0', 'SELECT cleanup_old_password_history(365);');
