import React, { useState, useRef, useEffect } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import fallbackAuthService from '../services/fallbackAuthService';
import mobileDetection from '../utils/mobileDetection';

interface PinAuthComponentProps {
  mode: 'setup' | 'authenticate';
  email?: string;
  onSuccess?: (result: any) => void;
  onError?: (error: string) => void;
  onCancel?: () => void;
  className?: string;
}

const PinAuthComponent: React.FC<PinAuthComponentProps> = ({
  mode,
  email,
  onSuccess,
  onError,
  onCancel,
  className = ''
}) => {
  const { t } = useLanguage();
  const [pin, setPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [hint, setHint] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showHint, setShowHint] = useState(false);
  const [step, setStep] = useState<'pin' | 'confirm' | 'hint'>('pin');
  
  const pinInputRefs = useRef<(HTMLInputElement | null)[]>([]);
  const confirmPinInputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const pinLength = 6; // Default PIN length

  // Focus management for PIN inputs
  const focusNextInput = (index: number, isConfirm: boolean = false) => {
    const refs = isConfirm ? confirmPinInputRefs.current : pinInputRefs.current;
    if (index < pinLength - 1 && refs[index + 1]) {
      refs[index + 1]?.focus();
    }
  };

  const focusPrevInput = (index: number, isConfirm: boolean = false) => {
    const refs = isConfirm ? confirmPinInputRefs.current : pinInputRefs.current;
    if (index > 0 && refs[index - 1]) {
      refs[index - 1]?.focus();
    }
  };

  // Handle PIN input change
  const handlePinChange = (index: number, value: string, isConfirm: boolean = false) => {
    if (!/^\d*$/.test(value)) return; // Only allow digits

    const currentPin = isConfirm ? confirmPin : pin;
    const newPin = currentPin.split('');
    newPin[index] = value;
    const updatedPin = newPin.join('').slice(0, pinLength);

    if (isConfirm) {
      setConfirmPin(updatedPin);
    } else {
      setPin(updatedPin);
    }

    if (value && index < pinLength - 1) {
      focusNextInput(index, isConfirm);
    }
  };

  // Handle backspace
  const handleKeyDown = (e: React.KeyboardEvent, index: number, isConfirm: boolean = false) => {
    if (e.key === 'Backspace') {
      const currentPin = isConfirm ? confirmPin : pin;
      if (!currentPin[index] && index > 0) {
        focusPrevInput(index, isConfirm);
      }
    }
  };

  // Handle setup flow
  const handleSetup = async () => {
    if (step === 'pin') {
      if (pin.length !== pinLength) {
        onError?.(`الرقم السري يجب أن يكون ${pinLength} أرقام`);
        return;
      }
      setStep('confirm');
      return;
    }

    if (step === 'confirm') {
      if (confirmPin.length !== pinLength) {
        onError?.(`تأكيد الرقم السري يجب أن يكون ${pinLength} أرقام`);
        return;
      }
      if (pin !== confirmPin) {
        onError?.('الرقم السري غير متطابق');
        setConfirmPin('');
        return;
      }
      setStep('hint');
      return;
    }

    if (step === 'hint') {
      try {
        setIsLoading(true);
        const result = await fallbackAuthService.setupPin({
          pin,
          confirmPin,
          hint: hint.trim() || undefined
        });

        if (result.success) {
          mobileDetection.triggerHapticFeedback('success');
          onSuccess?.(result);
        } else {
          mobileDetection.triggerHapticFeedback('error');
          onError?.(result.message || 'فشل في إعداد الرقم السري');
        }
      } catch (error: any) {
        mobileDetection.triggerHapticFeedback('error');
        onError?.(error.message || 'فشل في إعداد الرقم السري');
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Handle authentication
  const handleAuthenticate = async () => {
    if (!email) {
      onError?.('البريد الإلكتروني مطلوب');
      return;
    }

    if (pin.length !== pinLength) {
      onError?.(`الرقم السري يجب أن يكون ${pinLength} أرقام`);
      return;
    }

    try {
      setIsLoading(true);
      mobileDetection.triggerHapticFeedback('warning');

      const result = await fallbackAuthService.authenticateWithPin(email, pin);

      if (result.success) {
        mobileDetection.triggerHapticFeedback('success');
        onSuccess?.(result);
      } else {
        mobileDetection.triggerHapticFeedback('error');
        onError?.(result.message || 'الرقم السري غير صحيح');
        setPin(''); // Clear PIN on failure
      }
    } catch (error: any) {
      mobileDetection.triggerHapticFeedback('error');
      onError?.(error.message || 'فشل في المصادقة');
      setPin('');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle back button in setup flow
  const handleBack = () => {
    if (step === 'confirm') {
      setStep('pin');
      setConfirmPin('');
    } else if (step === 'hint') {
      setStep('confirm');
      setHint('');
    }
  };

  // Render PIN input grid
  const renderPinInput = (isConfirm: boolean = false) => {
    const currentPin = isConfirm ? confirmPin : pin;
    const refs = isConfirm ? confirmPinInputRefs : pinInputRefs;

    return (
      <div className="flex justify-center gap-2 mb-6">
        {Array.from({ length: pinLength }, (_, index) => (
          <input
            key={index}
            ref={(el) => (refs.current[index] = el)}
            type="text"
            inputMode="numeric"
            pattern="[0-9]*"
            maxLength={1}
            value={currentPin[index] || ''}
            onChange={(e) => handlePinChange(index, e.target.value, isConfirm)}
            onKeyDown={(e) => handleKeyDown(e, index, isConfirm)}
            className={`
              w-12 h-12 text-center text-xl font-bold border-2 rounded-lg
              focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
              ${currentPin[index] ? 'border-blue-500 bg-blue-50' : 'border-gray-300'}
              ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}
            `}
            disabled={isLoading}
            autoComplete="off"
          />
        ))}
      </div>
    );
  };

  return (
    <div className={`pin-auth-component ${className}`} style={{ direction: 'rtl' }}>
      <div className="bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto">
        <div className="text-center mb-6">
          <div className="text-4xl mb-4">🔢</div>
          <h3 className="text-xl font-bold font-['Almarai']">
            {mode === 'setup' ? 'إعداد الرقم السري' : 'تسجيل الدخول بالرقم السري'}
          </h3>
          <p className="text-gray-600 mt-2 font-['Almarai']">
            {mode === 'setup' 
              ? step === 'pin' ? `أدخل رقم سري مكون من ${pinLength} أرقام`
                : step === 'confirm' ? 'أكد الرقم السري'
                : 'أضف تلميح اختياري (يساعدك على التذكر)'
              : `أدخل الرقم السري المكون من ${pinLength} أرقام`
            }
          </p>
        </div>

        {mode === 'setup' && step === 'pin' && (
          <div>
            {renderPinInput()}
            <div className="flex gap-3 justify-center">
              <button
                onClick={handleSetup}
                disabled={pin.length !== pinLength || isLoading}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-['Almarai'] disabled:opacity-50 disabled:cursor-not-allowed"
              >
                المتابعة
              </button>
              <button
                onClick={onCancel}
                className="bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 transition-colors font-['Almarai']"
              >
                إلغاء
              </button>
            </div>
          </div>
        )}

        {mode === 'setup' && step === 'confirm' && (
          <div>
            {renderPinInput(true)}
            <div className="flex gap-3 justify-center">
              <button
                onClick={handleSetup}
                disabled={confirmPin.length !== pinLength || isLoading}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-['Almarai'] disabled:opacity-50 disabled:cursor-not-allowed"
              >
                المتابعة
              </button>
              <button
                onClick={handleBack}
                className="bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 transition-colors font-['Almarai']"
              >
                رجوع
              </button>
            </div>
          </div>
        )}

        {mode === 'setup' && step === 'hint' && (
          <div>
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2 font-['Almarai']">
                تلميح اختياري
              </label>
              <input
                type="text"
                value={hint}
                onChange={(e) => setHint(e.target.value)}
                placeholder="مثال: تاريخ ميلادي"
                className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 font-['Almarai']"
                maxLength={100}
                disabled={isLoading}
              />
              <p className="text-xs text-gray-500 mt-1 font-['Almarai']">
                التلميح سيساعدك على تذكر الرقم السري (اختياري)
              </p>
            </div>
            <div className="flex gap-3 justify-center">
              <button
                onClick={handleSetup}
                disabled={isLoading}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-['Almarai'] disabled:opacity-50"
              >
                {isLoading ? 'جاري الحفظ...' : 'حفظ الرقم السري'}
              </button>
              <button
                onClick={handleBack}
                disabled={isLoading}
                className="bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 transition-colors font-['Almarai']"
              >
                رجوع
              </button>
            </div>
          </div>
        )}

        {mode === 'authenticate' && (
          <div>
            {renderPinInput()}
            
            {showHint && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
                <p className="text-yellow-800 text-sm font-['Almarai']">
                  💡 تلميح: {hint || 'لا يوجد تلميح محفوظ'}
                </p>
              </div>
            )}

            <div className="flex gap-3 justify-center mb-4">
              <button
                onClick={handleAuthenticate}
                disabled={pin.length !== pinLength || isLoading}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-['Almarai'] disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'جاري التحقق...' : 'تسجيل الدخول'}
              </button>
              <button
                onClick={onCancel}
                disabled={isLoading}
                className="bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 transition-colors font-['Almarai']"
              >
                إلغاء
              </button>
            </div>

            <div className="text-center">
              <button
                onClick={() => setShowHint(!showHint)}
                className="text-blue-600 hover:text-blue-800 text-sm font-['Almarai']"
              >
                {showHint ? 'إخفاء التلميح' : 'عرض التلميح'}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PinAuthComponent;
