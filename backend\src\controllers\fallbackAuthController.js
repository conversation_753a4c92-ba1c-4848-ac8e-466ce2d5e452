const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const { query } = require('../models/database');
const { generateToken, generateRefreshToken } = require('../middleware/auth');
const { validateUUID, sanitizeInput } = require('../utils/validation');
const { sendEmail } = require('../services/emailService');

/**
 * Fallback Authentication Controller
 * Handles alternative authentication methods
 * Supports PIN, pattern, email recovery, and progressive enhancement
 */

// Get available fallback methods for user
const getFallbackMethods = async (req, res) => {
  try {
    const { userId } = req.params;

    if (!validateUUID(userId)) {
      return res.status(400).json({
        success: false,
        message: 'معرف المستخدم غير صالح'
      });
    }

    // Get user's fallback method settings
    const userResult = await query(
      `SELECT pin_enabled, pattern_enabled, email_recovery_enabled, 
              pin_hint, pattern_hint, last_pin_used, last_pattern_used
       FROM users WHERE id = $1`,
      [userId]
    );

    if (userResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    const user = userResult.rows[0];

    const methods = [
      {
        type: 'password',
        name: 'Password',
        nameArabic: 'كلمة المرور',
        icon: '🔑',
        available: true,
        enabled: true,
        setupRequired: false
      },
      {
        type: 'pin',
        name: 'PIN',
        nameArabic: 'رقم سري',
        icon: '🔢',
        available: true,
        enabled: user.pin_enabled || false,
        setupRequired: !user.pin_enabled,
        lastUsed: user.last_pin_used,
        hint: user.pin_hint
      },
      {
        type: 'pattern',
        name: 'Pattern',
        nameArabic: 'نمط',
        icon: '⚫',
        available: true,
        enabled: user.pattern_enabled || false,
        setupRequired: !user.pattern_enabled,
        lastUsed: user.last_pattern_used,
        hint: user.pattern_hint
      },
      {
        type: 'email',
        name: 'Email Recovery',
        nameArabic: 'استرداد بالبريد',
        icon: '📧',
        available: true,
        enabled: user.email_recovery_enabled !== false,
        setupRequired: false
      }
    ];

    res.json({
      success: true,
      methods: methods.filter(method => method.available)
    });

  } catch (error) {
    console.error('خطأ في جلب طرق المصادقة البديلة:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في جلب طرق المصادقة البديلة'
    });
  }
};

// Setup PIN for user
const setupPin = async (req, res) => {
  try {
    const { userId } = req.user;
    const { pin, hint } = req.body;

    // Validate PIN
    if (!pin || typeof pin !== 'string') {
      return res.status(400).json({
        success: false,
        message: 'الرقم السري مطلوب'
      });
    }

    if (pin.length < 4 || pin.length > 8) {
      return res.status(400).json({
        success: false,
        message: 'الرقم السري يجب أن يكون بين 4 و 8 أرقام'
      });
    }

    if (!/^\d+$/.test(pin)) {
      return res.status(400).json({
        success: false,
        message: 'الرقم السري يجب أن يحتوي على أرقام فقط'
      });
    }

    // Hash the PIN
    const hashedPin = await bcrypt.hash(pin, 12);
    const sanitizedHint = hint ? sanitizeInput(hint).substring(0, 100) : null;

    // Update user with PIN
    await query(
      `UPDATE users SET 
        pin_hash = $1, 
        pin_enabled = true, 
        pin_hint = $2,
        updated_at = NOW()
       WHERE id = $3`,
      [hashedPin, sanitizedHint, userId]
    );

    // Log the setup
    await query(
      `INSERT INTO logs (user_id, action, details) 
       VALUES ($1, $2, $3)`,
      [userId, 'PIN_SETUP', { method: 'pin', hasHint: !!hint }]
    );

    res.json({
      success: true,
      message: 'تم إعداد الرقم السري بنجاح'
    });

  } catch (error) {
    console.error('خطأ في إعداد الرقم السري:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في إعداد الرقم السري'
    });
  }
};

// Authenticate with PIN
const authenticateWithPin = async (req, res) => {
  try {
    const { email, pin } = req.body;

    if (!email || !pin) {
      return res.status(400).json({
        success: false,
        message: 'البريد الإلكتروني والرقم السري مطلوبان'
      });
    }

    // Find user
    const userResult = await query(
      `SELECT id, email, pin_hash, pin_enabled, failed_pin_attempts, 
              pin_locked_until, role
       FROM users WHERE email = $1`,
      [email.toLowerCase()]
    );

    if (userResult.rows.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'بيانات الدخول غير صحيحة'
      });
    }

    const user = userResult.rows[0];

    // Check if PIN is enabled
    if (!user.pin_enabled || !user.pin_hash) {
      return res.status(400).json({
        success: false,
        message: 'الرقم السري غير مفعل لهذا الحساب'
      });
    }

    // Check if PIN is locked
    if (user.pin_locked_until && new Date() < user.pin_locked_until) {
      return res.status(423).json({
        success: false,
        message: 'الرقم السري مؤقت مؤقتاً. حاول مرة أخرى لاحقاً'
      });
    }

    // Verify PIN
    const pinValid = await bcrypt.compare(pin, user.pin_hash);

    if (!pinValid) {
      // Increment failed attempts
      const newFailedAttempts = (user.failed_pin_attempts || 0) + 1;
      const lockUntil = newFailedAttempts >= 5 ? 
        new Date(Date.now() + 15 * 60 * 1000) : null; // 15 minutes

      await query(
        `UPDATE users SET 
          failed_pin_attempts = $1,
          pin_locked_until = $2
         WHERE id = $3`,
        [newFailedAttempts, lockUntil, user.id]
      );

      // Log failed attempt
      await query(
        `INSERT INTO logs (user_id, action, details, ip_address) 
         VALUES ($1, $2, $3, $4)`,
        [user.id, 'PIN_AUTH_FAILED', { attempts: newFailedAttempts }, req.ip]
      );

      return res.status(401).json({
        success: false,
        message: 'الرقم السري غير صحيح'
      });
    }

    // Reset failed attempts and update last used
    await query(
      `UPDATE users SET 
        failed_pin_attempts = 0,
        pin_locked_until = NULL,
        last_pin_used = NOW()
       WHERE id = $1`,
      [user.id]
    );

    // Generate tokens
    const token = generateToken(user.id, 'pin');
    const refreshToken = generateRefreshToken(user.id, 'pin');

    // Store refresh token
    await query(
      'UPDATE users SET refresh_token = $1, refresh_token_expires = $2 WHERE id = $3',
      [refreshToken, new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), user.id]
    );

    // Log successful authentication
    await query(
      `INSERT INTO logs (user_id, action, details, ip_address) 
       VALUES ($1, $2, $3, $4)`,
      [user.id, 'PIN_AUTH_SUCCESS', { method: 'pin' }, req.ip]
    );

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      token,
      refreshToken,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        language: 'ar',
        textDirection: 'rtl',
        authMethod: 'pin'
      }
    });

  } catch (error) {
    console.error('خطأ في المصادقة بالرقم السري:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في تسجيل الدخول'
    });
  }
};

// Setup pattern for user
const setupPattern = async (req, res) => {
  try {
    const { userId } = req.user;
    const { pattern, hint } = req.body;

    // Validate pattern
    if (!pattern || !Array.isArray(pattern)) {
      return res.status(400).json({
        success: false,
        message: 'النمط مطلوب'
      });
    }

    if (pattern.length < 4 || pattern.length > 9) {
      return res.status(400).json({
        success: false,
        message: 'النمط يجب أن يحتوي على 4-9 نقاط'
      });
    }

    // Validate pattern points (should be 0-8 for 3x3 grid)
    if (!pattern.every(point => Number.isInteger(point) && point >= 0 && point <= 8)) {
      return res.status(400).json({
        success: false,
        message: 'النمط يحتوي على نقاط غير صالحة'
      });
    }

    // Check for duplicate points
    if (new Set(pattern).size !== pattern.length) {
      return res.status(400).json({
        success: false,
        message: 'النمط يحتوي على نقاط مكررة'
      });
    }

    // Hash the pattern
    const patternString = pattern.join(',');
    const hashedPattern = await bcrypt.hash(patternString, 12);
    const sanitizedHint = hint ? sanitizeInput(hint).substring(0, 100) : null;

    // Update user with pattern
    await query(
      `UPDATE users SET 
        pattern_hash = $1, 
        pattern_enabled = true, 
        pattern_hint = $2,
        updated_at = NOW()
       WHERE id = $3`,
      [hashedPattern, sanitizedHint, userId]
    );

    // Log the setup
    await query(
      `INSERT INTO logs (user_id, action, details) 
       VALUES ($1, $2, $3)`,
      [userId, 'PATTERN_SETUP', { method: 'pattern', hasHint: !!hint, patternLength: pattern.length }]
    );

    res.json({
      success: true,
      message: 'تم إعداد النمط بنجاح'
    });

  } catch (error) {
    console.error('خطأ في إعداد النمط:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في إعداد النمط'
    });
  }
};

// Authenticate with pattern
const authenticateWithPattern = async (req, res) => {
  try {
    const { email, pattern } = req.body;

    if (!email || !pattern || !Array.isArray(pattern)) {
      return res.status(400).json({
        success: false,
        message: 'البريد الإلكتروني والنمط مطلوبان'
      });
    }

    // Find user
    const userResult = await query(
      `SELECT id, email, pattern_hash, pattern_enabled, failed_pattern_attempts, 
              pattern_locked_until, role
       FROM users WHERE email = $1`,
      [email.toLowerCase()]
    );

    if (userResult.rows.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'بيانات الدخول غير صحيحة'
      });
    }

    const user = userResult.rows[0];

    // Check if pattern is enabled
    if (!user.pattern_enabled || !user.pattern_hash) {
      return res.status(400).json({
        success: false,
        message: 'النمط غير مفعل لهذا الحساب'
      });
    }

    // Check if pattern is locked
    if (user.pattern_locked_until && new Date() < user.pattern_locked_until) {
      return res.status(423).json({
        success: false,
        message: 'النمط مؤقت مؤقتاً. حاول مرة أخرى لاحقاً'
      });
    }

    // Verify pattern
    const patternString = pattern.join(',');
    const patternValid = await bcrypt.compare(patternString, user.pattern_hash);

    if (!patternValid) {
      // Increment failed attempts
      const newFailedAttempts = (user.failed_pattern_attempts || 0) + 1;
      const lockUntil = newFailedAttempts >= 5 ? 
        new Date(Date.now() + 15 * 60 * 1000) : null; // 15 minutes

      await query(
        `UPDATE users SET 
          failed_pattern_attempts = $1,
          pattern_locked_until = $2
         WHERE id = $3`,
        [newFailedAttempts, lockUntil, user.id]
      );

      // Log failed attempt
      await query(
        `INSERT INTO logs (user_id, action, details, ip_address) 
         VALUES ($1, $2, $3, $4)`,
        [user.id, 'PATTERN_AUTH_FAILED', { attempts: newFailedAttempts }, req.ip]
      );

      return res.status(401).json({
        success: false,
        message: 'النمط غير صحيح'
      });
    }

    // Reset failed attempts and update last used
    await query(
      `UPDATE users SET 
        failed_pattern_attempts = 0,
        pattern_locked_until = NULL,
        last_pattern_used = NOW()
       WHERE id = $1`,
      [user.id]
    );

    // Generate tokens
    const token = generateToken(user.id, 'pattern');
    const refreshToken = generateRefreshToken(user.id, 'pattern');

    // Store refresh token
    await query(
      'UPDATE users SET refresh_token = $1, refresh_token_expires = $2 WHERE id = $3',
      [refreshToken, new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), user.id]
    );

    // Log successful authentication
    await query(
      `INSERT INTO logs (user_id, action, details, ip_address) 
       VALUES ($1, $2, $3, $4)`,
      [user.id, 'PATTERN_AUTH_SUCCESS', { method: 'pattern' }, req.ip]
    );

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      token,
      refreshToken,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        language: 'ar',
        textDirection: 'rtl',
        authMethod: 'pattern'
      }
    });

  } catch (error) {
    console.error('خطأ في المصادقة بالنمط:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في تسجيل الدخول'
    });
  }
};

// Send email recovery code
const sendEmailRecovery = async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'البريد الإلكتروني مطلوب'
      });
    }

    // Find user
    const userResult = await query(
      'SELECT id, email, email_recovery_enabled FROM users WHERE email = $1',
      [email.toLowerCase()]
    );

    if (userResult.rows.length === 0) {
      // Don't reveal if email exists or not for security
      return res.json({
        success: true,
        message: 'إذا كان البريد الإلكتروني موجود، فسيتم إرسال رمز الاسترداد'
      });
    }

    const user = userResult.rows[0];

    // Check if email recovery is enabled
    if (user.email_recovery_enabled === false) {
      return res.json({
        success: true,
        message: 'إذا كان البريد الإلكتروني موجود، فسيتم إرسال رمز الاسترداد'
      });
    }

    // Generate recovery code
    const recoveryCode = crypto.randomInt(100000, 999999).toString();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Store recovery code
    await query(
      `INSERT INTO recovery_codes (user_id, code, type, expires_at, ip_address)
       VALUES ($1, $2, $3, $4, $5)
       ON CONFLICT (user_id, type)
       DO UPDATE SET code = $2, expires_at = $4, created_at = NOW(), ip_address = $5`,
      [user.id, recoveryCode, 'email', expiresAt, req.ip]
    );

    // Send email (implement based on your email service)
    try {
      await sendEmail({
        to: user.email,
        subject: 'رمز استرداد الحساب - نظام التوقيع الإلكتروني',
        template: 'recovery-code',
        data: {
          recoveryCode,
          expiresIn: '10 دقائق',
          ipAddress: req.ip,
          userAgent: req.get('User-Agent')
        }
      });
    } catch (emailError) {
      console.error('خطأ في إرسال البريد الإلكتروني:', emailError);
      // Don't fail the request if email fails
    }

    // Log recovery request
    await query(
      `INSERT INTO logs (user_id, action, details, ip_address)
       VALUES ($1, $2, $3, $4)`,
      [user.id, 'EMAIL_RECOVERY_REQUESTED', { method: 'email' }, req.ip]
    );

    res.json({
      success: true,
      message: 'تم إرسال رمز الاسترداد إلى بريدك الإلكتروني'
    });

  } catch (error) {
    console.error('خطأ في إرسال رمز الاسترداد:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في إرسال رمز الاسترداد'
    });
  }
};

// Verify email recovery code
const verifyEmailRecovery = async (req, res) => {
  try {
    const { email, recoveryCode } = req.body;

    if (!email || !recoveryCode) {
      return res.status(400).json({
        success: false,
        message: 'البريد الإلكتروني ورمز الاسترداد مطلوبان'
      });
    }

    // Find user and recovery code
    const result = await query(
      `SELECT u.id, u.email, u.role, rc.code, rc.expires_at, rc.attempts
       FROM users u
       JOIN recovery_codes rc ON u.id = rc.user_id
       WHERE u.email = $1 AND rc.type = 'email' AND rc.expires_at > NOW()`,
      [email.toLowerCase()]
    );

    if (result.rows.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'رمز الاسترداد غير صالح أو منتهي الصلاحية'
      });
    }

    const { id: userId, email: userEmail, role, code, expires_at, attempts } = result.rows[0];

    // Check attempts limit
    if (attempts >= 3) {
      await query(
        'DELETE FROM recovery_codes WHERE user_id = $1 AND type = $2',
        [userId, 'email']
      );

      return res.status(429).json({
        success: false,
        message: 'تم تجاوز الحد المسموح من المحاولات'
      });
    }

    // Verify code
    if (code !== recoveryCode) {
      // Increment attempts
      await query(
        'UPDATE recovery_codes SET attempts = attempts + 1 WHERE user_id = $1 AND type = $2',
        [userId, 'email']
      );

      // Log failed attempt
      await query(
        `INSERT INTO logs (user_id, action, details, ip_address)
         VALUES ($1, $2, $3, $4)`,
        [userId, 'EMAIL_RECOVERY_FAILED', { attempts: attempts + 1 }, req.ip]
      );

      return res.status(400).json({
        success: false,
        message: 'رمز الاسترداد غير صحيح'
      });
    }

    // Code is valid, delete it
    await query(
      'DELETE FROM recovery_codes WHERE user_id = $1 AND type = $2',
      [userId, 'email']
    );

    // Generate tokens
    const token = generateToken(userId, 'email_recovery');
    const refreshToken = generateRefreshToken(userId, 'email_recovery');

    // Store refresh token
    await query(
      'UPDATE users SET refresh_token = $1, refresh_token_expires = $2 WHERE id = $3',
      [refreshToken, new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), userId]
    );

    // Log successful recovery
    await query(
      `INSERT INTO logs (user_id, action, details, ip_address)
       VALUES ($1, $2, $3, $4)`,
      [userId, 'EMAIL_RECOVERY_SUCCESS', { method: 'email_recovery' }, req.ip]
    );

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      token,
      refreshToken,
      user: {
        id: userId,
        email: userEmail,
        role,
        language: 'ar',
        textDirection: 'rtl',
        authMethod: 'email_recovery'
      }
    });

  } catch (error) {
    console.error('خطأ في التحقق من رمز الاسترداد:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في التحقق من رمز الاسترداد'
    });
  }
};

module.exports = {
  getFallbackMethods,
  setupPin,
  authenticateWithPin,
  setupPattern,
  authenticateWithPattern,
  sendEmailRecovery,
  verifyEmailRecovery
};
