const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const { requirePermission } = require('../middleware/roleAuth');
const geolocationService = require('../services/geolocationService');
const { query } = require('../models/database');
const locationTrackingController = require('../controllers/locationTrackingController');
const adminLocationController = require('../controllers/adminLocationController');

const router = express.Router();

/**
 * Simple Location API
 * Provides basic IP-to-location detection only
 * Replaces the over-engineered geographical security system
 */

/**
 * Get current location from IP address
 * Returns basic location data: country, city, latitude, longitude
 */
router.post('/current', authenticateToken, async (req, res) => {
  try {
    const ipAddress = req.ip || req.connection.remoteAddress || '127.0.0.1';
    
    // Get basic location data
    const locationData = await geolocationService.getLocationFromIP(ipAddress);
    
    if (!locationData) {
      return res.status(404).json({
        success: false,
        message: 'لا يمكن تحديد الموقع الجغرافي',
        data: null
      });
    }

    // Optional: Log location for basic tracking (if needed)
    if (req.user?.userId) {
      try {
        await query(
          `INSERT INTO location_logs (user_id, ip_address, country_code, country_name, region, city, latitude, longitude, source)
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
          [
            req.user.userId,
            ipAddress,
            locationData.country_code,
            locationData.country_name,
            locationData.region,
            locationData.city,
            locationData.latitude,
            locationData.longitude,
            locationData.source
          ]
        );
      } catch (logError) {
        // Don't fail the request if logging fails
        console.warn('Failed to log location:', logError.message);
      }
    }

    // Return simplified location data (IP masked for privacy)
    res.json({
      success: true,
      message: 'تم تحديد الموقع الجغرافي بنجاح',
      data: {
        country_code: locationData.country_code,
        country_name: locationData.country_name,
        region: locationData.region,
        city: locationData.city,
        latitude: locationData.latitude,
        longitude: locationData.longitude,
        ip_masked: ipAddress.replace(/\.\d+$/, '.***'), // Mask last octet for privacy
        source: locationData.source,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error getting current location:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تحديد الموقع الجغرافي',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * User Location Tracking Routes
 */

// Update user location (real-time tracking)
router.post('/update', authenticateToken, locationTrackingController.updateUserLocation);

// Get current user location
router.get('/current-session', authenticateToken, locationTrackingController.getCurrentLocation);

// Update location consent
router.post('/consent', authenticateToken, locationTrackingController.updateLocationConsent);

// Get location consent status
router.get('/consent', authenticateToken, locationTrackingController.getLocationConsent);

/**
 * Admin Location Monitoring Routes
 */

// Get all active user sessions (admin only)
router.get('/admin/sessions',
  authenticateToken,
  requirePermission('track_user_locations'),
  adminLocationController.getActiveUserSessions
);

// Get user location history (admin only)
router.get('/admin/users/:userId/history',
  authenticateToken,
  requirePermission('track_user_locations'),
  adminLocationController.getUserLocationHistory
);

// Get user activity timeline (admin only)
router.get('/admin/users/:userId/activity',
  authenticateToken,
  requirePermission('track_user_locations'),
  adminLocationController.getUserActivityTimeline
);

// Export location data (admin only)
router.get('/admin/export',
  authenticateToken,
  requirePermission('track_user_locations'),
  adminLocationController.exportLocationData
);

// Export user activity timeline (admin only)
router.get('/admin/users/:userId/activity/export',
  authenticateToken,
  requirePermission('track_user_locations'),
  adminLocationController.exportUserActivityTimeline
);

/**
 * Health check for location service
 */
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'خدمة تحديد المواقع تعمل بشكل طبيعي',
    timestamp: new Date().toISOString(),
    version: '2.0.0',
    features: {
      ipGeolocation: true,
      realTimeTracking: true,
      adminMonitoring: true,
      consentManagement: true
    }
  });
});

module.exports = router;
