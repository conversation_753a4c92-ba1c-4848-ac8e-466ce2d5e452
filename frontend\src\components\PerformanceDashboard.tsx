import React, { useState, useEffect, useCallback, useRef } from 'react';
import { authAPI } from '../services/api';

interface PerformanceMetrics {
  status: string;
  timestamp: string;
  database: {
    status: string;
    responseTime: number;
    poolInfo: {
      totalCount: number;
      idleCount: number;
      waitingCount: number;
    };
  };
  system: {
    memory: {
      used: number;
      total: number;
      external: number;
      rss: number;
    };
    cpu: {
      user: number;
      system: number;
    };
    uptime: number;
    nodeVersion: string;
  };
  performance: {
    totalQueries: number;
    slowQueries: number;
    failedQueries: number;
    averageResponseTime: number;
    slowQueryPercentage: string;
    failureRate: string;
  };
}

interface PerformanceDashboardProps {
  className?: string;
}

const PerformanceDashboard: React.FC<PerformanceDashboardProps> = ({ className = '' }) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const lastFetchTime = useRef<number>(0);
  const minFetchInterval = 10000; // Minimum 10 seconds between requests

  const fetchMetrics = useCallback(async () => {
    // Throttle requests to prevent excessive API calls
    const now = Date.now();
    if (now - lastFetchTime.current < minFetchInterval) {
      console.log('Performance metrics fetch throttled');
      return;
    }
    lastFetchTime.current = now;

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        setError('Authentication required');
        setLoading(false);
        return;
      }

      const response = await fetch('/api/performance/health', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.status === 401 || response.status === 403) {
        setError('Authentication failed - please log in again');
        setLoading(false);
        return;
      }

      if (!response.ok) {
        throw new Error(`Failed to fetch performance metrics: ${response.status}`);
      }

      const data = await response.json();
      setMetrics(data.data);
      setError(null);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [minFetchInterval]);

  useEffect(() => {
    fetchMetrics();

    if (autoRefresh && !error?.includes('Authentication')) {
      const interval = setInterval(() => {
        // Only continue auto-refresh if no authentication errors
        if (!error?.includes('Authentication')) {
          fetchMetrics();
        }
      }, 60000); // Increased to 60 seconds to reduce load
      return () => clearInterval(interval);
    }
  }, [autoRefresh, error, fetchMetrics]);

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-100';
      case 'unhealthy': return 'text-red-600 bg-red-100';
      default: return 'text-yellow-600 bg-yellow-100';
    }
  };

  const getMetricColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return 'text-green-600';
    if (value <= thresholds.warning) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-red-800 mb-2">خطأ في تحميل البيانات</h3>
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchMetrics}
            className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
          >
            إعادة المحاولة
          </button>
        </div>
      </div>
    );
  }

  if (!metrics) return null;

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">لوحة مراقبة الأداء</h2>
        <div className="flex items-center space-x-4 space-x-reverse">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="autoRefresh"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="ml-2"
            />
            <label htmlFor="autoRefresh" className="text-sm text-gray-600">
              تحديث تلقائي
            </label>
          </div>
          <button
            onClick={fetchMetrics}
            className="bg-blue-600 text-white px-3 py-1 rounded-md hover:bg-blue-700 text-sm"
          >
            تحديث
          </button>
        </div>
      </div>

      {/* System Status */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-gray-600">حالة النظام</h3>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(metrics.status)}`}>
              {metrics.status === 'healthy' ? 'سليم' : 'غير سليم'}
            </span>
          </div>
        </div>

        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-sm font-medium text-gray-600 mb-2">قاعدة البيانات</h3>
          <div className="text-lg font-semibold">
            <span className={getMetricColor(metrics.database.responseTime, { good: 100, warning: 500 })}>
              {metrics.database.responseTime}ms
            </span>
          </div>
          <p className="text-xs text-gray-500">زمن الاستجابة</p>
        </div>

        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-sm font-medium text-gray-600 mb-2">الذاكرة</h3>
          <div className="text-lg font-semibold">
            <span className={getMetricColor(metrics.system.memory.used, { good: 500, warning: 1000 })}>
              {metrics.system.memory.used}MB
            </span>
          </div>
          <p className="text-xs text-gray-500">من {metrics.system.memory.total}MB</p>
        </div>

        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-sm font-medium text-gray-600 mb-2">وقت التشغيل</h3>
          <div className="text-lg font-semibold text-blue-600">
            {formatUptime(metrics.system.uptime)}
          </div>
          <p className="text-xs text-gray-500">منذ آخر إعادة تشغيل</p>
        </div>
      </div>

      {/* Database Performance */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">أداء قاعدة البيانات</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">إجمالي الاستعلامات</span>
              <span className="font-medium">{metrics.performance.totalQueries.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">الاستعلامات البطيئة</span>
              <span className={`font-medium ${getMetricColor(parseFloat(metrics.performance.slowQueryPercentage), { good: 5, warning: 15 })}`}>
                {metrics.performance.slowQueryPercentage}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">معدل الفشل</span>
              <span className={`font-medium ${getMetricColor(parseFloat(metrics.performance.failureRate), { good: 1, warning: 5 })}`}>
                {metrics.performance.failureRate}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">متوسط زمن الاستجابة</span>
              <span className={`font-medium ${getMetricColor(metrics.performance.averageResponseTime, { good: 50, warning: 200 })}`}>
                {metrics.performance.averageResponseTime.toFixed(2)}ms
              </span>
            </div>
          </div>
        </div>

        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">تجمع الاتصالات</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">إجمالي الاتصالات</span>
              <span className="font-medium">{metrics.database.poolInfo.totalCount}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">الاتصالات الخاملة</span>
              <span className="font-medium text-green-600">{metrics.database.poolInfo.idleCount}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">الاتصالات المنتظرة</span>
              <span className={`font-medium ${metrics.database.poolInfo.waitingCount > 0 ? 'text-yellow-600' : 'text-green-600'}`}>
                {metrics.database.poolInfo.waitingCount}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* System Information */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">معلومات النظام</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div>
            <span className="text-gray-600">إصدار Node.js:</span>
            <span className="font-medium mr-2">{metrics.system.nodeVersion}</span>
          </div>
          <div>
            <span className="text-gray-600">آخر تحديث:</span>
            <span className="font-medium mr-2">
              {new Date(metrics.timestamp).toLocaleString('ar-SA')}
            </span>
          </div>
          <div>
            <span className="text-gray-600">الذاكرة الخارجية:</span>
            <span className="font-medium mr-2">{metrics.system.memory.external}MB</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PerformanceDashboard;
