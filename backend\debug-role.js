const { query } = require('./src/models/database');

async function debugRole() {
  try {
    console.log('🔍 Debugging role system...\n');

    // Check if role column exists
    console.log('1. Checking if role column exists...');
    try {
      const columnCheck = await query(`
        SELECT column_name, data_type, is_nullable, column_default 
        FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'role'
      `);
      
      if (columnCheck.rows.length > 0) {
        console.log('✅ Role column exists:');
        console.log('   Column:', columnCheck.rows[0].column_name);
        console.log('   Type:', columnCheck.rows[0].data_type);
        console.log('   Nullable:', columnCheck.rows[0].is_nullable);
        console.log('   Default:', columnCheck.rows[0].column_default);
      } else {
        console.log('❌ Role column does not exist');
        return;
      }
    } catch (error) {
      console.log('❌ Error checking role column:', error.message);
      return;
    }

    // Check users and their roles
    console.log('\n2. Checking users and their roles...');
    try {
      const users = await query('SELECT id, email, role FROM users ORDER BY email');
      
      if (users.rows.length > 0) {
        console.log('✅ Users found:');
        users.rows.forEach((user, index) => {
          console.log(`   ${index + 1}. ${user.email} - Role: ${user.role || 'NULL'} - ID: ${user.id}`);
        });
      } else {
        console.log('❌ No users found');
      }
    } catch (error) {
      console.log('❌ Error fetching users:', error.message);
    }

    // Test the specific admin user
    console.log('\n3. Testing specific admin user...');
    try {
      const adminUser = await query('SELECT id, email, role FROM users WHERE email = $1', ['<EMAIL>']);
      
      if (adminUser.rows.length > 0) {
        const user = adminUser.rows[0];
        console.log('✅ Admin user found:');
        console.log('   Email:', user.email);
        console.log('   Role:', user.role);
        console.log('   ID:', user.id);
        
        // Test permission function
        console.log('\n4. Testing permission function...');
        const { hasPermission } = require('./src/middleware/roleAuth');
        
        const testPermissions = ['upload_signatures', 'sign_documents', 'view_history'];
        testPermissions.forEach(permission => {
          const hasAccess = hasPermission(permission, user.role);
          console.log(`   ${permission}: ${hasAccess ? '✅ ALLOWED' : '❌ DENIED'}`);
        });
        
      } else {
        console.log('❌ Admin user not found');
      }
    } catch (error) {
      console.log('❌ Error testing admin user:', error.message);
    }

    console.log('\n🎉 Debug completed!');

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  }
}

// Run the debug
debugRole();
