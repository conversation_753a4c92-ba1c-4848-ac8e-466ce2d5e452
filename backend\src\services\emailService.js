const nodemailer = require('nodemailer');

/**
 * Email Service
 * Handles email sending functionality
 */

class EmailService {
  constructor() {
    this.transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: process.env.SMTP_PORT || 587,
      secure: false,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    });
  }

  /**
   * Send email
   * @param {Object} options - Email options
   * @returns {Promise<Object>} Email result
   */
  async sendEmail(options) {
    try {
      const {
        to,
        subject,
        text,
        html,
        from = process.env.SMTP_FROM || process.env.SMTP_USER
      } = options;

      const mailOptions = {
        from,
        to,
        subject,
        text,
        html
      };

      const result = await this.transporter.sendMail(mailOptions);
      return {
        success: true,
        messageId: result.messageId
      };
    } catch (error) {
      console.error('Email sending error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Send verification email
   * @param {string} email - Recipient email
   * @param {string} verificationCode - Verification code
   * @returns {Promise<Object>} Email result
   */
  async sendVerificationEmail(email, verificationCode) {
    const subject = 'رمز التحقق - نظام التوقيع الإلكتروني';
    const text = `رمز التحقق الخاص بك هو: ${verificationCode}`;
    const html = `
      <div dir="rtl" style="font-family: 'Almarai', Arial, sans-serif;">
        <h2>رمز التحقق</h2>
        <p>رمز التحقق الخاص بك هو:</p>
        <h1 style="color: #007bff; font-size: 32px; letter-spacing: 5px;">${verificationCode}</h1>
        <p>هذا الرمز صالح لمدة 10 دقائق فقط.</p>
      </div>
    `;

    return this.sendEmail({
      to: email,
      subject,
      text,
      html
    });
  }

  /**
   * Send password reset email
   * @param {string} email - Recipient email
   * @param {string} resetToken - Reset token
   * @returns {Promise<Object>} Email result
   */
  async sendPasswordResetEmail(email, resetToken) {
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;
    const subject = 'إعادة تعيين كلمة المرور - نظام التوقيع الإلكتروني';
    const text = `لإعادة تعيين كلمة المرور، يرجى زيارة الرابط التالي: ${resetUrl}`;
    const html = `
      <div dir="rtl" style="font-family: 'Almarai', Arial, sans-serif;">
        <h2>إعادة تعيين كلمة المرور</h2>
        <p>تم طلب إعادة تعيين كلمة المرور لحسابك.</p>
        <p>لإعادة تعيين كلمة المرور، يرجى النقر على الرابط التالي:</p>
        <a href="${resetUrl}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
          إعادة تعيين كلمة المرور
        </a>
        <p>هذا الرابط صالح لمدة ساعة واحدة فقط.</p>
        <p>إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذا البريد الإلكتروني.</p>
      </div>
    `;

    return this.sendEmail({
      to: email,
      subject,
      text,
      html
    });
  }
}

module.exports = new EmailService();
