# Email Notification Configuration
# Copy this to your .env file and configure with your email settings

# Email Service Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Notification Settings
EMAIL_NOTIFICATIONS_ENABLED=true
EMAIL_FROM=<EMAIL>
ADMIN_NOTIFICATION_EMAILS=<EMAIL>,<EMAIL>

# Retry Configuration
EMAIL_RETRY_ATTEMPTS=3
EMAIL_RETRY_DELAY=5000

# Gmail Configuration Example:
# 1. Enable 2-factor authentication on your Gmail account
# 2. Generate an App Password: https://myaccount.google.com/apppasswords
# 3. Use the App Password as EMAIL_PASS (not your regular password)

# Other Email Providers:
# Outlook/Hotmail:
# EMAIL_HOST=smtp-mail.outlook.com
# EMAIL_PORT=587

# Yahoo:
# EMAIL_HOST=smtp.mail.yahoo.com
# EMAIL_PORT=587

# Custom SMTP:
# EMAIL_HOST=your-smtp-server.com
# EMAIL_PORT=587 (or 465 for SSL)
# EMAIL_SECURE=true (for port 465)
