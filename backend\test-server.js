const axios = require('axios');

async function testServer() {
  try {
    console.log('🔍 Testing backend server...\n');
    
    const baseURL = 'http://localhost:3001';
    
    // Test 1: Basic server response
    console.log('1. Testing basic server response...');
    try {
      const response = await axios.get(`${baseURL}/api/auth/login`, {
        validateStatus: () => true
      });
      console.log(`✅ Server is responding (Status: ${response.status})`);
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log('❌ Server is not running on port 3001');
        return;
      }
      console.log('✅ Server is responding (connection established)');
    }
    
    // Test 2: Login with admin account
    console.log('\n2. Testing admin login...');
    try {
      const loginResponse = await axios.post(`${baseURL}/api/auth/login`, {
        email: '<EMAIL>',
        password: 'admin123'
      });
      
      console.log('✅ Admin login successful');
      const token = loginResponse.data.token;
      
      // Test 3: Test signatures endpoint
      console.log('\n3. Testing signatures endpoint...');
      try {
        const signaturesResponse = await axios.get(`${baseURL}/api/signatures`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        console.log('✅ Signatures endpoint accessible');
        console.log(`📊 Signatures count: ${signaturesResponse.data.signatures?.length || 0}`);
      } catch (sigError) {
        console.log('❌ Signatures endpoint failed:');
        console.log(`   Status: ${sigError.response?.status}`);
        console.log(`   Message: ${sigError.response?.data?.message || sigError.message}`);
      }
      
      // Test 4: Test documents endpoint
      console.log('\n4. Testing documents endpoint...');
      try {
        const documentsResponse = await axios.get(`${baseURL}/api/documents?page=1&limit=5`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        console.log('✅ Documents endpoint accessible');
        console.log(`📊 Documents count: ${documentsResponse.data.pagination?.total || 0}`);
      } catch (docError) {
        console.log('❌ Documents endpoint failed:');
        console.log(`   Status: ${docError.response?.status}`);
        console.log(`   Message: ${docError.response?.data?.message || docError.message}`);
      }
      
    } catch (loginError) {
      console.log('❌ Admin login failed:');
      console.log(`   Status: ${loginError.response?.status}`);
      console.log(`   Message: ${loginError.response?.data?.message || loginError.message}`);
    }
    
    console.log('\n🏁 Test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testServer();
