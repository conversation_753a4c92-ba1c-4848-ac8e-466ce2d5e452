const axios = require('axios');

async function testAdminLogin() {
  try {
    console.log('🔍 Testing admin login...\n');
    
    const baseURL = 'http://localhost:3001/api';
    
    // Test login with admin account
    const loginData = {
      email: '<EMAIL>',
      password: 'password123' // Common test password
    };
    
    console.log(`📧 Attempting login with: ${loginData.email}`);
    
    try {
      const loginResponse = await axios.post(`${baseURL}/auth/login`, loginData);
      
      console.log('✅ Login successful!');
      console.log(`👤 User ID: ${loginResponse.data.user.id}`);
      console.log(`📧 Email: ${loginResponse.data.user.email}`);
      console.log(`🔐 Role: ${loginResponse.data.user.role}`);
      console.log(`🎫 Token: ${loginResponse.data.token.substring(0, 20)}...`);
      
      // Test accessing signatures endpoint
      const token = loginResponse.data.token;
      console.log('\n🔍 Testing signatures endpoint...');
      
      try {
        const signaturesResponse = await axios.get(`${baseURL}/signatures`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        console.log('✅ Signatures endpoint accessible');
        console.log(`📊 Signatures count: ${signaturesResponse.data.signatures?.length || 0}`);
        
      } catch (sigError) {
        console.log('❌ Signatures endpoint failed:');
        console.log(`   Status: ${sigError.response?.status}`);
        console.log(`   Message: ${sigError.response?.data?.message || sigError.message}`);
      }
      
      // Test accessing documents endpoint
      console.log('\n🔍 Testing documents endpoint...');
      
      try {
        const documentsResponse = await axios.get(`${baseURL}/documents?page=1&limit=5`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        console.log('✅ Documents endpoint accessible');
        console.log(`📊 Documents count: ${documentsResponse.data.pagination?.total || 0}`);
        
      } catch (docError) {
        console.log('❌ Documents endpoint failed:');
        console.log(`   Status: ${docError.response?.status}`);
        console.log(`   Message: ${docError.response?.data?.message || docError.message}`);
      }
      
    } catch (loginError) {
      console.log('❌ Login failed:');
      console.log(`   Status: ${loginError.response?.status}`);
      console.log(`   Message: ${loginError.response?.data?.message || loginError.message}`);
      
      // Try with different password
      console.log('\n🔄 Trying with different password...');
      
      const altLoginData = {
        email: '<EMAIL>',
        password: 'admin123'
      };
      
      try {
        const altLoginResponse = await axios.post(`${baseURL}/auth/login`, altLoginData);
        console.log('✅ Login successful with alternative password!');
        console.log(`🔐 Role: ${altLoginResponse.data.user.role}`);

        // Test API endpoints with this token
        const altToken = altLoginResponse.data.token;

        console.log('\n🔍 Testing signatures endpoint with admin token...');
        try {
          const signaturesResponse = await axios.get(`${baseURL}/signatures`, {
            headers: { 'Authorization': `Bearer ${altToken}` }
          });
          console.log('✅ Signatures endpoint accessible');
          console.log(`📊 Signatures count: ${signaturesResponse.data.signatures?.length || 0}`);
        } catch (sigError) {
          console.log('❌ Signatures endpoint failed:');
          console.log(`   Status: ${sigError.response?.status}`);
          console.log(`   Message: ${sigError.response?.data?.message || sigError.message}`);
        }

        console.log('\n🔍 Testing documents endpoint with admin token...');
        try {
          const documentsResponse = await axios.get(`${baseURL}/documents?page=1&limit=5`, {
            headers: { 'Authorization': `Bearer ${altToken}` }
          });
          console.log('✅ Documents endpoint accessible');
          console.log(`📊 Documents count: ${documentsResponse.data.pagination?.total || 0}`);
        } catch (docError) {
          console.log('❌ Documents endpoint failed:');
          console.log(`   Status: ${docError.response?.status}`);
          console.log(`   Message: ${docError.response?.data?.message || docError.message}`);
        }
      } catch (altError) {
        console.log('❌ Alternative login also failed');
        
        // Try with the main admin account
        console.log('\n🔄 Trying main admin account...');
        
        const mainAdminData = {
          email: '<EMAIL>',
          password: 'password123'
        };
        
        try {
          const mainLoginResponse = await axios.post(`${baseURL}/auth/login`, mainAdminData);
          console.log('✅ Main admin login successful!');
          console.log(`🔐 Role: ${mainLoginResponse.data.user.role}`);
        } catch (mainError) {
          console.log('❌ Main admin login failed');
          console.log('💡 You may need to register these admin accounts first');
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('💡 Make sure the backend server is running on port 3001');
  }
}

// Run the test
testAdminLogin();
