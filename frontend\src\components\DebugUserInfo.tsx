import React from 'react';
import { useAuth } from '../services/AuthContext';

const DebugUserInfo: React.FC = () => {
  const { user, token, isAdmin, hasPermission, loading } = useAuth();

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="bg-gray-100 border border-gray-300 rounded-lg p-4 mb-4 text-xs font-mono">
      <h4 className="font-bold text-gray-800 mb-2">🐛 Debug Info (Development Only)</h4>
      <div className="space-y-1 text-gray-700">
        <div><strong>Loading:</strong> {loading ? 'true' : 'false'}</div>
        <div><strong>User:</strong> {user ? JSON.stringify(user, null, 2) : 'null'}</div>
        <div><strong>Token:</strong> {token ? `${token.substring(0, 20)}...` : 'null'}</div>
        <div><strong>Is Admin:</strong> {isAdmin() ? 'true' : 'false'}</div>
        <div><strong>Has upload_signatures:</strong> {hasPermission('upload_signatures') ? 'true' : 'false'}</div>
        <div><strong>Has sign_documents:</strong> {hasPermission('sign_documents') ? 'true' : 'false'}</div>
        <div><strong>Has view_history:</strong> {hasPermission('view_history') ? 'true' : 'false'}</div>
      </div>
    </div>
  );
};

export default DebugUserInfo;
