import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../services/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import { signatureAPI, documentAPI } from '../services/api';
import DashboardSwitcher from '../components/DashboardSwitcher';
import UserRoleInfo from '../components/UserRoleInfo';
import DebugUserInfo from '../components/DebugUserInfo';
import AdminLoginInfo from '../components/AdminLoginInfo';

interface DashboardStats {
  totalSignatures: number;
  totalDocuments: number;
  recentDocuments: any[];
}

// Enhanced skeleton component for loading states
const SkeletonCard: React.FC = () => (
  <div className="bg-white p-4 sm:p-6 rounded-xl shadow-sm border border-gray-100">
    <div className="flex items-center">
      <div className="p-3 rounded-xl animate-shimmer flex-shrink-0 w-12 h-12"></div>
      <div className="mr-4 min-w-0 flex-1">
        <div className="h-4 animate-shimmer rounded w-24 mb-2"></div>
        <div className="h-6 animate-shimmer rounded w-16"></div>
      </div>
    </div>
  </div>
);

// Enhanced stat card component
const StatCard: React.FC<{
  icon: React.ReactNode;
  title: string;
  value: string | number;
  color: 'blue' | 'green' | 'purple' | 'orange';
  trend?: { value: number; isPositive: boolean };
}> = ({ icon, title, value, color, trend }) => {
  const colorClasses = {
    blue: 'bg-blue-50 text-blue-600 border-blue-100 hover:bg-blue-100',
    green: 'bg-green-50 text-green-600 border-green-100 hover:bg-green-100',
    purple: 'bg-purple-50 text-purple-600 border-purple-100 hover:bg-purple-100',
    orange: 'bg-orange-50 text-orange-600 border-orange-100 hover:bg-orange-100'
  };

  return (
    <div className="bg-white p-4 sm:p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 group min-h-[120px] flex flex-col justify-center">
      <div className="flex items-center justify-between h-full">
        <div className="flex items-center flex-1 min-w-0">
          <div className={`p-3 rounded-xl ${colorClasses[color]} transition-colors duration-200 flex-shrink-0`}>
            {icon}
          </div>
          <div className="mr-3 sm:mr-4 min-w-0 flex-1">
            <p className="text-xs sm:text-sm font-medium text-gray-600 font-['Almarai'] mb-1 leading-tight">{title}</p>
            <p className="text-xl sm:text-2xl font-bold text-gray-900 font-['Almarai'] leading-tight">{value}</p>
          </div>
        </div>
        {trend && (
          <div className={`flex items-center text-xs sm:text-sm font-medium ${trend.isPositive ? 'text-green-600' : 'text-red-600'} flex-shrink-0 ml-2`}>
            <svg className={`w-3 h-3 sm:w-4 sm:h-4 ml-1 ${trend.isPositive ? '' : 'rotate-180'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 14l9-9 3 3" />
            </svg>
            <span className="font-['Almarai']">{Math.abs(trend.value)}%</span>
          </div>
        )}
      </div>
    </div>
  );
};

const Dashboard: React.FC = () => {
  const { user, isAdmin } = useAuth();
  const { t } = useLanguage();
  const [stats, setStats] = useState<DashboardStats>({
    totalSignatures: 0,
    totalDocuments: 0,
    recentDocuments: []
  });
  const [loading, setLoading] = useState(true);

  const formatSignedDate = (dateString: string | undefined) => {
    // Handle undefined, null, or invalid date strings
    if (!dateString) {
      return 'غير محدد';
    }

    try {
      const date = new Date(dateString);

      // Check if the date is valid
      if (isNaN(date.getTime())) {
        return 'تاريخ غير صالح';
      }

      // Format date in English locale
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Date formatting error:', error);
      return 'تاريخ غير صالح';
    }
  };

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        const promises = [];

        // Only fetch signatures if user is admin
        if (isAdmin()) {
          promises.push(signatureAPI.getAll());
        } else {
          promises.push(Promise.resolve({ data: { signatures: [] } }));
        }

        // Only fetch documents if user is admin
        if (isAdmin()) {
          promises.push(documentAPI.getAll(1, 5));
        } else {
          promises.push(Promise.resolve({ data: { documents: [], pagination: { total: 0 } } }));
        }

        const [signaturesResponse, documentsResponse] = await Promise.all(promises);

        setStats({
          totalSignatures: signaturesResponse.data.signatures.length,
          totalDocuments: documentsResponse.data.pagination.total,
          recentDocuments: documentsResponse.data.documents
        });
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        // Set default values on error
        setStats({
          totalSignatures: 0,
          totalDocuments: 0,
          recentDocuments: []
        });
      } finally {
        setLoading(false);
      }
    };

    // Only fetch data if user is loaded
    if (user) {
      fetchDashboardData();
    }
  }, [user, isAdmin]);

  if (loading) {
    return (
      <div className="space-y-6 px-4 sm:px-0">
        {/* Welcome Section Skeleton */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 sm:p-8 rounded-xl shadow-sm border border-blue-100">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="h-8 animate-shimmer rounded w-64 mb-3"></div>
              <div className="h-4 animate-shimmer rounded w-96 mb-4"></div>
              <div className="h-3 animate-shimmer rounded w-48"></div>
            </div>
            <div className="hidden sm:block">
              <div className="w-20 h-20 animate-shimmer rounded-full"></div>
            </div>
          </div>
        </div>

        {/* Stats Cards Skeleton */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          <SkeletonCard />
          <SkeletonCard />
          <SkeletonCard />
          <SkeletonCard />
        </div>

        {/* Quick Actions Skeleton */}
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          <div className="flex items-center justify-between mb-6">
            <div className="h-6 animate-shimmer rounded w-32"></div>
            <div className="h-4 animate-shimmer rounded w-20"></div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="h-32 animate-shimmer rounded-xl"></div>
            <div className="h-32 animate-shimmer rounded-xl"></div>
            <div className="h-32 animate-shimmer rounded-xl"></div>
            <div className="h-32 animate-shimmer rounded-xl"></div>
          </div>
        </div>

        {/* Recent Documents Skeleton */}
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          <div className="flex items-center justify-between mb-6">
            <div className="h-6 animate-shimmer rounded w-32"></div>
            <div className="h-4 animate-shimmer rounded w-16"></div>
          </div>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center justify-between p-4 border border-gray-200 rounded-xl">
                <div className="flex items-center flex-1">
                  <div className="w-10 h-10 animate-shimmer rounded-lg mr-4"></div>
                  <div className="flex-1">
                    <div className="h-4 animate-shimmer rounded w-48 mb-2"></div>
                    <div className="h-3 animate-shimmer rounded w-32"></div>
                  </div>
                </div>
                <div className="h-6 animate-shimmer rounded-full w-16"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 px-4 sm:px-0 overflow-x-hidden dashboard-container">
      <div className="max-w-7xl mx-auto layout-fix">
        <DashboardSwitcher />

        {/* Debug Information */}
        <DebugUserInfo />

        {/* User Role Information */}
        <UserRoleInfo />

        {/* Admin Login Info for non-admin users */}
        {!isAdmin() && <AdminLoginInfo />}

        {/* Enhanced Welcome Section */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 sm:p-6 lg:p-8 rounded-xl shadow-sm border border-blue-100 mb-6 dashboard-header">
          <div className="flex items-center justify-between flex-wrap sm:flex-nowrap gap-4">
            <div className="flex-1 min-w-0">
              <h1 className="text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-bold text-gray-800 mb-2 font-['Almarai'] leading-tight word-wrap">
                {t.dashboard.welcome}، {user?.email?.split('@')[0]}!
              </h1>
              <p className="text-sm sm:text-base lg:text-lg text-gray-600 font-['Almarai'] mb-4 leading-relaxed">
                {t.dashboard.subtitle}
              </p>
              <div className="flex items-center text-xs sm:text-sm text-blue-600 font-['Almarai']">
                <svg className="w-3 h-3 sm:w-4 sm:h-4 ml-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="truncate">آخر تسجيل دخول: {new Date().toLocaleDateString('ar-SA')}</span>
              </div>
            </div>
            <div className="hidden sm:block flex-shrink-0">
              <div className="w-16 h-16 sm:w-20 sm:h-20 bg-blue-100 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 sm:w-10 sm:h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Signatures Card */}
        <StatCard
          icon={
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
            </svg>
          }
          title={t.dashboard.totalSignatures}
          value={stats.totalSignatures}
          color="blue"
          trend={{ value: 12, isPositive: true }}
        />

        {/* Documents Card */}
        <StatCard
          icon={
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          }
          title={t.dashboard.signedDocuments}
          value={stats.totalDocuments}
          color="green"
          trend={{ value: 8, isPositive: true }}
        />

        {/* Status Card */}
        <StatCard
          icon={
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          }
          title={t.dashboard.status}
          value={t.dashboard.active}
          color="purple"
        />

        {/* Additional metric for admins */}
        {isAdmin() && (
          <StatCard
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            }
            title="المستخدمون النشطون"
            value="24"
            color="orange"
            trend={{ value: 5, isPositive: true }}
          />
        )}
        </div>

        {/* Enhanced Quick Actions */}
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-800 font-['Almarai']">{t.dashboard.quickActions}</h2>
            <div className="flex items-center text-sm text-gray-500 font-['Almarai']">
              <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              إجراءات سريعة
            </div>
          </div>

          <div className={`grid gap-4 sm:gap-6 ${isAdmin() ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' : 'grid-cols-1 sm:grid-cols-2'}`}>
          {/* Admin Actions */}
          {isAdmin() && (
            <>
              {/* Upload Signature Action */}
              <Link
                to="/signature-upload"
                className="group relative overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100 p-4 sm:p-6 rounded-xl border border-blue-200 hover:from-blue-100 hover:to-blue-200 transition-all duration-300 hover:shadow-lg hover:-translate-y-1 min-h-[140px] flex flex-col justify-center"
              >
                <div className="flex flex-col items-center text-center h-full">
                  <div className="p-2 sm:p-3 bg-blue-500 rounded-xl text-white mb-3 sm:mb-4 group-hover:scale-110 transition-transform duration-200 flex-shrink-0">
                    <svg className="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                  </div>
                  <h3 className="font-bold text-gray-900 font-['Almarai'] mb-2 text-sm sm:text-base leading-tight text-center">{t.dashboard.uploadSignature}</h3>
                  <p className="text-xs sm:text-sm text-gray-600 font-['Almarai'] leading-tight text-center">إضافة توقيع جديد للنظام</p>
                </div>
              </Link>

              {/* Admin Document Signing */}
              <Link
                to="/admin-document-signing"
                className="group relative overflow-hidden bg-gradient-to-br from-green-50 to-green-100 p-6 rounded-xl border border-green-200 hover:from-green-100 hover:to-green-200 transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
              >
                <div className="flex flex-col items-center text-center">
                  <div className="p-3 bg-green-500 rounded-xl text-white mb-4 group-hover:scale-110 transition-transform duration-200">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="font-bold text-gray-900 font-['Almarai'] mb-2">توقيع المستندات</h3>
                  <p className="text-sm text-gray-600 font-['Almarai']">مراجعة وتوقيع المستندات</p>
                </div>
              </Link>
            </>
          )}

          {/* Document Upload - For Regular Users */}
          <Link
            to="/document-signing"
            className="group relative overflow-hidden bg-gradient-to-br from-purple-50 to-purple-100 p-6 rounded-xl border border-purple-200 hover:from-purple-100 hover:to-purple-200 transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
          >
            <div className="flex flex-col items-center text-center">
              <div className="p-3 bg-purple-500 rounded-xl text-white mb-4 group-hover:scale-110 transition-transform duration-200">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
              </div>
              <h3 className="font-bold text-gray-900 font-['Almarai'] mb-2">
                {isAdmin() ? 'رفع مستند جديد' : t.dashboard.signDocument}
              </h3>
              <p className="text-sm text-gray-600 font-['Almarai']">
                {isAdmin() ? 'رفع مستند للمراجعة' : 'رفع مستند للتوقيع'}
              </p>
            </div>
          </Link>

          {/* View History */}
          <Link
            to="/history"
            className="group relative overflow-hidden bg-gradient-to-br from-orange-50 to-orange-100 p-6 rounded-xl border border-orange-200 hover:from-orange-100 hover:to-orange-200 transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
          >
            <div className="flex flex-col items-center text-center">
              <div className="p-3 bg-orange-500 rounded-xl text-white mb-4 group-hover:scale-110 transition-transform duration-200">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="font-bold text-gray-900 font-['Almarai'] mb-2">{t.dashboard.viewHistory}</h3>
              <p className="text-sm text-gray-600 font-['Almarai']">عرض سجل المستندات</p>
            </div>
          </Link>
          </div>
        </div>

        {/* Enhanced Recent Documents */}
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-800 font-['Almarai']">{t.dashboard.recentDocuments}</h2>
            {stats.recentDocuments.length > 0 && (
              <Link
                to="/history"
                className="text-sm text-blue-600 hover:text-blue-700 font-medium font-['Almarai'] flex items-center transition-colors duration-200"
              >
                عرض الكل
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </Link>
            )}
          </div>

          {stats.recentDocuments.length > 0 ? (
            <div className="space-y-4">
              {stats.recentDocuments.map((doc, index) => (
              <div
                key={doc.id}
                className="group flex items-center justify-between p-4 border border-gray-200 rounded-xl hover:border-blue-300 hover:bg-blue-50 transition-all duration-200"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="flex items-center flex-1 min-w-0">
                  <div className="p-2 bg-gray-100 rounded-lg group-hover:bg-blue-100 transition-colors duration-200 flex-shrink-0">
                    <svg className="w-5 h-5 text-gray-600 group-hover:text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div className="mr-4 min-w-0 flex-1">
                    <p className="font-medium text-gray-900 font-['Almarai'] truncate">{doc.original_filename}</p>
                    <p className="text-sm text-gray-600 font-['Almarai'] mt-1">
                      تم التوقيع في {formatSignedDate(doc.signed_date)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <span className="px-3 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full font-['Almarai'] whitespace-nowrap">
                    موقع
                  </span>
                  <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                    </svg>
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="w-24 h-24 mx-auto mb-6 text-gray-300">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="w-full h-full">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-bold text-gray-800 mb-3 font-['Almarai']">لا توجد مستندات بعد</h3>
            <p className="text-gray-600 mb-6 font-['Almarai'] leading-relaxed max-w-md mx-auto">
              {isAdmin()
                ? 'لم يتم رفع أي مستندات للمراجعة بعد. ستظهر المستندات المرفوعة من المستخدمين هنا.'
                : t.dashboard.noDocuments
              }
            </p>
            <Link
              to="/document-signing"
              className="inline-flex items-center justify-center bg-blue-600 text-white px-6 py-3 rounded-xl hover:bg-blue-700 transition-colors duration-200 font-medium font-['Almarai'] shadow-lg hover:shadow-xl"
            >
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              {isAdmin() ? 'مراجعة المستندات' : t.dashboard.startSigning}
            </Link>
          </div>
        )}
      </div>

      {/* Additional Dashboard Features */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* System Status */}
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          <h3 className="text-lg font-bold text-gray-800 mb-4 font-['Almarai'] flex items-center">
            <svg className="w-5 h-5 ml-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            حالة النظام
          </h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 font-['Almarai']">الخدمة</span>
              <span className="flex items-center text-sm font-medium text-green-600 font-['Almarai']">
                <div className="w-2 h-2 bg-green-500 rounded-full ml-2"></div>
                متاح
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 font-['Almarai']">قاعدة البيانات</span>
              <span className="flex items-center text-sm font-medium text-green-600 font-['Almarai']">
                <div className="w-2 h-2 bg-green-500 rounded-full ml-2"></div>
                متصل
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 font-['Almarai']">آخر نسخة احتياطية</span>
              <span className="text-sm text-gray-600 font-['Almarai']">
                {new Date().toLocaleDateString('ar-SA')}
              </span>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          <h3 className="text-lg font-bold text-gray-800 mb-4 font-['Almarai'] flex items-center">
            <svg className="w-5 h-5 ml-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            إحصائيات سريعة
          </h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 font-['Almarai']">اليوم</span>
              <span className="text-sm font-medium text-gray-900 font-['Almarai']">
                {Math.floor(Math.random() * 10) + 1} مستند
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 font-['Almarai']">هذا الأسبوع</span>
              <span className="text-sm font-medium text-gray-900 font-['Almarai']">
                {Math.floor(Math.random() * 50) + 10} مستند
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 font-['Almarai']">متوسط وقت المعالجة</span>
              <span className="text-sm text-gray-600 font-['Almarai']">
                2.3 دقيقة
              </span>
            </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
