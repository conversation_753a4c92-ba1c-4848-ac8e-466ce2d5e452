const app = require('./app');
const { initializeArabicFonts } = require('./services/arabicFontService');
const locationTrackingService = require('./services/locationTrackingService');
const dataRetentionService = require('./services/dataRetentionService');

const PORT = process.env.PORT || 3001;

// Initialize Almarai font support for Arabic-only system
initializeArabicFonts().then((success) => {
  if (success) {
    console.log('✓ تم تهيئة خط الماراي بنجاح');
  } else {
    console.warn('⚠ فشل في تهيئة خط الماراي - استخدام الخطوط الاحتياطية');
  }
});

const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
  console.log(`البيئة: ${process.env.NODE_ENV || 'development'}`);
  console.log(`فحص الحالة: http://localhost:${PORT}/health`);
  console.log(`فحص الحالة: http://127.0.0.1:${PORT}/health`);
  console.log(`فحص الحالة: http://0.0.0.0:${PORT}/health`);
  console.log('🇸🇦 نظام التوقيع الإلكتروني العربي جاهز');

  // Initialize location tracking service
  locationTrackingService.initializeWebSocket(server);
  console.log('📍 خدمة تتبع المواقع في الوقت الفعلي مفعلة');

  // Start data retention service
  dataRetentionService.start();
  console.log('📅 خدمة الاحتفاظ بالبيانات مفعلة');

  // Test server is actually listening
  const http = require('http');
  setTimeout(() => {
    const req = http.get(`http://localhost:${PORT}/health`, (res) => {
      console.log('✅ Self-test successful - server is responding');
    });
    req.on('error', (err) => {
      console.error('❌ Self-test failed:', err.message);
    });
  }, 1000);
});

// Configure server timeouts for large file uploads
server.timeout = 10 * 60 * 1000; // 10 minutes
server.keepAliveTimeout = 5 * 60 * 1000; // 5 minutes
server.headersTimeout = 6 * 60 * 1000; // 6 minutes

// Graceful shutdown handling
const gracefulShutdown = (signal) => {
  console.log(`\n📊 Received ${signal}. Starting graceful shutdown...`);

  // Stop location tracking service
  locationTrackingService.stop();

  // Stop data retention service
  dataRetentionService.stop();

  // Close server
  server.close((err) => {
    if (err) {
      console.error('❌ Error during server shutdown:', err);
      process.exit(1);
    }

    console.log('✅ Server closed successfully');
    process.exit(0);
  });

  // Force shutdown after 30 seconds
  setTimeout(() => {
    console.error('❌ Forced shutdown after timeout');
    process.exit(1);
  }, 30000);
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

module.exports = server;

