const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001/api';

async function testAuthFix() {
  try {
    console.log('🔍 Testing authentication fix...\n');

    // Step 1: Login with admin credentials
    console.log('1. Logging in with admin credentials...');
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });

    const { token, user } = loginResponse.data;
    console.log(`✅ Login successful!`);
    console.log(`   User: ${user.email}`);
    console.log(`   Role: ${user.role}`);
    console.log(`   Token: ${token.substring(0, 20)}...`);

    // Step 2: Test getProfile endpoint
    console.log('\n2. Testing getProfile endpoint...');
    const profileResponse = await axios.get(`${API_BASE_URL}/auth/profile`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const profileUser = profileResponse.data.user;
    console.log(`✅ Profile fetch successful!`);
    console.log(`   User: ${profileUser.email}`);
    console.log(`   Role: ${profileUser.role}`);

    // Step 3: Test signatures endpoint (this was failing before)
    console.log('\n3. Testing signatures endpoint...');
    try {
      const signaturesResponse = await axios.get(`${API_BASE_URL}/signatures`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log(`✅ Signatures fetch successful!`);
      console.log(`   Signatures count: ${signaturesResponse.data.signatures.length}`);
      
      if (signaturesResponse.data.signatures.length > 0) {
        console.log(`   First signature: ${signaturesResponse.data.signatures[0].filename}`);
      }
    } catch (sigError) {
      console.log(`❌ Signatures fetch failed:`);
      console.log(`   Status: ${sigError.response?.status}`);
      console.log(`   Message: ${sigError.response?.data?.message || sigError.message}`);
      
      if (sigError.response?.status === 403) {
        console.log(`   🔍 This indicates the user doesn't have admin permissions`);
      }
    }

    // Step 4: Check if user has admin role
    console.log('\n4. Checking admin status...');
    if (profileUser.role === 'admin') {
      console.log(`✅ User has admin role - should be able to access signatures`);
    } else {
      console.log(`❌ User does not have admin role (${profileUser.role}) - cannot access signatures`);
    }

    console.log('\n🎉 Test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    }
  }
}

// Run the test
testAuthFix();
