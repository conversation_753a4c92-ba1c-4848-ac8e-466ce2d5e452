import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../services/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import { signatureAPI, documentAPI } from '../services/api';
import DashboardSwitcher from '../components/DashboardSwitcher';
import UserRoleInfo from '../components/UserRoleInfo';
import AdminLoginInfo from '../components/AdminLoginInfo';

interface DashboardStats {
  totalSignatures: number;
  totalDocuments: number;
  recentDocuments: any[];
}

// Alternative skeleton component with different styling
const SkeletonCardAlt: React.FC = () => (
  <div className="bg-gradient-to-br from-gray-50 to-gray-100 p-6 rounded-2xl border-2 border-gray-200">
    <div className="flex flex-col items-center">
      <div className="w-16 h-16 rounded-full animate-shimmer mb-4"></div>
      <div className="h-5 animate-shimmer rounded-full w-32 mb-2"></div>
      <div className="h-8 animate-shimmer rounded-full w-20"></div>
    </div>
  </div>
);

// Alternative stat card with vertical layout and different colors
const StatCardAlt: React.FC<{
  icon: React.ReactNode;
  title: string;
  value: string | number;
  color: 'emerald' | 'violet' | 'amber' | 'rose' | 'cyan';
  description?: string;
}> = ({ icon, title, value, color, description }) => {
  const colorClasses = {
    emerald: 'bg-gradient-to-br from-emerald-400 to-emerald-600 shadow-emerald-200',
    violet: 'bg-gradient-to-br from-violet-400 to-violet-600 shadow-violet-200',
    amber: 'bg-gradient-to-br from-amber-400 to-amber-600 shadow-amber-200',
    rose: 'bg-gradient-to-br from-rose-400 to-rose-600 shadow-rose-200',
    cyan: 'bg-gradient-to-br from-cyan-400 to-cyan-600 shadow-cyan-200'
  };

  return (
    <div className="bg-white p-4 sm:p-6 rounded-2xl shadow-xl border-2 border-gray-100 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 group min-h-[160px] flex flex-col justify-center">
      <div className="flex flex-col items-center text-center h-full">
        <div className={`p-3 sm:p-4 rounded-full ${colorClasses[color]} text-white mb-3 sm:mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg flex-shrink-0`}>
          {icon}
        </div>
        <h3 className="text-xs sm:text-sm font-medium text-gray-600 font-['Almarai'] mb-2 leading-tight text-center px-1">{title}</h3>
        <p className="text-2xl sm:text-3xl font-bold text-gray-900 font-['Almarai'] mb-1 leading-tight">{value}</p>
        {description && (
          <p className="text-xs text-gray-500 font-['Almarai'] leading-tight text-center px-1">{description}</p>
        )}
      </div>
    </div>
  );
};

// Alternative action card with horizontal layout
const ActionCardAlt: React.FC<{
  to: string;
  icon: React.ReactNode;
  title: string;
  description: string;
  color: 'blue' | 'green' | 'purple' | 'orange' | 'pink';
}> = ({ to, icon, title, description, color }) => {
  const colorClasses = {
    blue: 'from-blue-500 to-blue-700 hover:from-blue-600 hover:to-blue-800',
    green: 'from-green-500 to-green-700 hover:from-green-600 hover:to-green-800',
    purple: 'from-purple-500 to-purple-700 hover:from-purple-600 hover:to-purple-800',
    orange: 'from-orange-500 to-orange-700 hover:from-orange-600 hover:to-orange-800',
    pink: 'from-pink-500 to-pink-700 hover:from-pink-600 hover:to-pink-800'
  };

  return (
    <Link
      to={to}
      className={`group bg-gradient-to-r ${colorClasses[color]} p-4 sm:p-6 rounded-2xl text-white shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 block w-full`}
    >
      <div className="flex items-center min-h-[60px]">
        <div className="p-2 sm:p-3 bg-white bg-opacity-20 rounded-xl group-hover:bg-opacity-30 transition-all duration-200 flex-shrink-0">
          {icon}
        </div>
        <div className="mr-3 sm:mr-4 flex-1 min-w-0">
          <h3 className="font-bold text-base sm:text-lg font-['Almarai'] mb-1 leading-tight">{title}</h3>
          <p className="text-xs sm:text-sm opacity-90 font-['Almarai'] leading-tight">{description}</p>
        </div>
        <svg className="w-5 h-5 sm:w-6 sm:h-6 group-hover:translate-x-1 transition-transform duration-200 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
        </svg>
      </div>
    </Link>
  );
};

const DashboardAlt: React.FC = () => {
  const { user, isAdmin } = useAuth();
  const { t } = useLanguage();
  const [stats, setStats] = useState<DashboardStats>({
    totalSignatures: 0,
    totalDocuments: 0,
    recentDocuments: []
  });
  const [loading, setLoading] = useState(true);

  const formatSignedDate = (dateString: string | undefined) => {
    if (!dateString) {
      return 'غير محدد';
    }

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return 'تاريخ غير صالح';
      }
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Date formatting error:', error);
      return 'تاريخ غير صالح';
    }
  };

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        const promises = [];

        // Only fetch signatures if user is admin
        if (isAdmin()) {
          promises.push(signatureAPI.getAll());
        } else {
          promises.push(Promise.resolve({ data: { signatures: [] } }));
        }

        // Only fetch documents if user is admin
        if (isAdmin()) {
          promises.push(documentAPI.getAll(1, 5));
        } else {
          promises.push(Promise.resolve({ data: { documents: [], pagination: { total: 0 } } }));
        }

        const [signaturesResponse, documentsResponse] = await Promise.all(promises);

        setStats({
          totalSignatures: signaturesResponse.data.signatures.length,
          totalDocuments: documentsResponse.data.pagination.total,
          recentDocuments: documentsResponse.data.documents
        });
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        // Set default values on error
        setStats({
          totalSignatures: 0,
          totalDocuments: 0,
          recentDocuments: []
        });
      } finally {
        setLoading(false);
      }
    };

    // Only fetch data if user is loaded
    if (user) {
      fetchDashboardData();
    }
  }, [user, isAdmin]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 p-6">
        {/* Header Skeleton */}
        <div className="text-center mb-12">
          <div className="h-12 animate-shimmer rounded-full w-96 mx-auto mb-4"></div>
          <div className="h-6 animate-shimmer rounded-full w-64 mx-auto"></div>
        </div>

        {/* Stats Grid Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-12">
          <SkeletonCardAlt />
          <SkeletonCardAlt />
          <SkeletonCardAlt />
          <SkeletonCardAlt />
          <SkeletonCardAlt />
        </div>

        {/* Content Grid Skeleton */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <div className="h-96 animate-shimmer rounded-2xl"></div>
          </div>
          <div className="space-y-6">
            <div className="h-48 animate-shimmer rounded-2xl"></div>
            <div className="h-48 animate-shimmer rounded-2xl"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 p-4 sm:p-6 overflow-x-hidden dashboard-container">
      <div className="max-w-7xl mx-auto layout-fix">
        <DashboardSwitcher />

        {/* User Role Information */}
        <UserRoleInfo />

        {/* Admin Login Info for non-admin users */}
        {!isAdmin() && <AdminLoginInfo />}

        {/* Centered Header Section */}
        <div className="text-center mb-8 sm:mb-12 dashboard-header">
          <div className="inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mb-4 sm:mb-6 shadow-xl">
            <svg className="w-8 h-8 sm:w-10 sm:h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
          <h1 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold text-gray-800 mb-4 font-['Almarai'] px-4">
            {t.dashboard.welcome}، {user?.email?.split('@')[0]}!
          </h1>
          <p className="text-lg sm:text-xl text-gray-600 font-['Almarai'] max-w-2xl mx-auto px-4">
            {t.dashboard.subtitle}
          </p>
          <div className="flex items-center justify-center mt-4 text-sm text-gray-500 font-['Almarai']">
            <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            آخر تسجيل دخول: {new Date().toLocaleDateString('ar-SA')}
          </div>
        </div>

        {/* Stats Cards - Responsive Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 sm:gap-6 mb-8 sm:mb-12">
        <StatCardAlt
          icon={
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
            </svg>
          }
          title={t.dashboard.totalSignatures}
          value={stats.totalSignatures}
          color="emerald"
          description="إجمالي التوقيعات"
        />

        <StatCardAlt
          icon={
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          }
          title={t.dashboard.signedDocuments}
          value={stats.totalDocuments}
          color="violet"
          description="المستندات الموقعة"
        />

        <StatCardAlt
          icon={
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          }
          title={t.dashboard.status}
          value={t.dashboard.active}
          color="amber"
          description="حالة النظام"
        />

        <StatCardAlt
          icon={
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          }
          title="اليوم"
          value={Math.floor(Math.random() * 10) + 1}
          color="rose"
          description="مستندات اليوم"
        />

        {isAdmin() && (
          <StatCardAlt
            icon={
              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            }
            title="المستخدمون النشطون"
            value="24"
            color="cyan"
            description="المستخدمون المتصلون"
          />
        )}
      </div>

        {/* Main Content Grid - 2/3 and 1/3 layout */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
          {/* Left Column - Quick Actions and Recent Documents */}
          <div className="lg:col-span-2 space-y-6 lg:space-y-8">
            {/* Quick Actions Section */}
            <div className="bg-white p-6 sm:p-8 rounded-2xl shadow-xl border-2 border-gray-100">
            <div className="flex items-center mb-8">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl text-white mr-4">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-gray-800 font-['Almarai']">{t.dashboard.quickActions}</h2>
            </div>

            <div className="space-y-4">
              {/* Admin Actions */}
              {isAdmin() && (
                <>
                  <ActionCardAlt
                    to="/signature-upload"
                    icon={
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                      </svg>
                    }
                    title={t.dashboard.uploadSignature}
                    description="إضافة توقيع جديد للنظام"
                    color="blue"
                  />

                  <ActionCardAlt
                    to="/admin/document-signing"
                    icon={
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    }
                    title="توقيع المستندات"
                    description="مراجعة وتوقيع المستندات"
                    color="green"
                  />
                </>
              )}

              <ActionCardAlt
                to="/document-signing"
                icon={
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                }
                title={isAdmin() ? 'رفع مستند جديد' : t.dashboard.signDocument}
                description={isAdmin() ? 'رفع مستند للمراجعة' : 'رفع مستند للتوقيع'}
                color="purple"
              />

              <ActionCardAlt
                to="/history"
                icon={
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                }
                title={t.dashboard.viewHistory}
                description="عرض سجل المستندات"
                color="orange"
              />
            </div>
          </div>

          {/* Recent Documents Section */}
          <div className="bg-white p-8 rounded-2xl shadow-xl border-2 border-gray-100">
            <div className="flex items-center justify-between mb-8">
              <div className="flex items-center">
                <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl text-white mr-4">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h2 className="text-2xl font-bold text-gray-800 font-['Almarai']">{t.dashboard.recentDocuments}</h2>
              </div>
              {stats.recentDocuments.length > 0 && (
                <Link
                  to="/history"
                  className="text-blue-600 hover:text-blue-700 font-medium font-['Almarai'] flex items-center transition-colors duration-200 bg-blue-50 px-4 py-2 rounded-xl hover:bg-blue-100"
                >
                  عرض الكل
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </Link>
              )}
            </div>

            {stats.recentDocuments.length > 0 ? (
              <div className="space-y-4">
                {stats.recentDocuments.map((doc, index) => (
                  <div
                    key={doc.id}
                    className="group flex items-center p-6 border-2 border-gray-100 rounded-2xl hover:border-blue-300 hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <div className="p-3 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl group-hover:from-blue-100 group-hover:to-blue-200 transition-all duration-300 mr-4">
                      <svg className="w-6 h-6 text-gray-600 group-hover:text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="font-bold text-gray-900 font-['Almarai'] text-lg mb-1">{doc.original_filename}</p>
                      <p className="text-gray-600 font-['Almarai']">
                        تم التوقيع في {formatSignedDate(doc.signed_date)}
                      </p>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="px-4 py-2 text-sm font-bold bg-gradient-to-r from-green-400 to-emerald-500 text-white rounded-full font-['Almarai'] shadow-lg">
                        موقع
                      </span>
                      <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200 hover:bg-gray-100 rounded-xl">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                        </svg>
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-16">
                <div className="w-32 h-32 mx-auto mb-8 text-gray-300">
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="w-full h-full">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4 font-['Almarai']">لا توجد مستندات بعد</h3>
                <p className="text-gray-600 mb-8 font-['Almarai'] leading-relaxed max-w-md mx-auto text-lg">
                  {isAdmin()
                    ? 'لم يتم رفع أي مستندات للمراجعة بعد. ستظهر المستندات المرفوعة من المستخدمين هنا.'
                    : t.dashboard.noDocuments
                  }
                </p>
                <Link
                  to="/document-signing"
                  className="inline-flex items-center justify-center bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-2xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 font-bold font-['Almarai'] shadow-xl hover:shadow-2xl transform hover:-translate-y-1"
                >
                  <svg className="w-6 h-6 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  {isAdmin() ? 'مراجعة المستندات' : t.dashboard.startSigning}
                </Link>
              </div>
            )}
          </div>
        </div>

        {/* Right Sidebar */}
        <div className="space-y-8">
          {/* System Status Card */}
          <div className="bg-white p-6 rounded-2xl shadow-xl border-2 border-gray-100">
            <div className="flex items-center mb-6">
              <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl text-white mr-3">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-bold text-gray-800 font-['Almarai']">حالة النظام</h3>
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl">
                <span className="text-sm text-gray-700 font-['Almarai'] font-medium">الخدمة</span>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-green-500 rounded-full ml-2 animate-pulse"></div>
                  <span className="text-sm font-bold text-green-700 font-['Almarai']">متاح</span>
                </div>
              </div>
              <div className="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl">
                <span className="text-sm text-gray-700 font-['Almarai'] font-medium">قاعدة البيانات</span>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-blue-500 rounded-full ml-2 animate-pulse"></div>
                  <span className="text-sm font-bold text-blue-700 font-['Almarai']">متصل</span>
                </div>
              </div>
              <div className="flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-violet-50 rounded-xl">
                <span className="text-sm text-gray-700 font-['Almarai'] font-medium">آخر نسخة احتياطية</span>
                <span className="text-sm font-bold text-purple-700 font-['Almarai']">
                  {new Date().toLocaleDateString('ar-SA')}
                </span>
              </div>
            </div>
          </div>

          {/* Quick Stats Card */}
          <div className="bg-white p-6 rounded-2xl shadow-xl border-2 border-gray-100">
            <div className="flex items-center mb-6">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-xl text-white mr-3">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h3 className="text-lg font-bold text-gray-800 font-['Almarai']">إحصائيات سريعة</h3>
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl">
                <span className="text-sm text-gray-700 font-['Almarai'] font-medium">اليوم</span>
                <span className="text-lg font-bold text-orange-700 font-['Almarai']">
                  {Math.floor(Math.random() * 10) + 1} مستند
                </span>
              </div>
              <div className="flex items-center justify-between p-3 bg-gradient-to-r from-rose-50 to-pink-50 rounded-xl">
                <span className="text-sm text-gray-700 font-['Almarai'] font-medium">هذا الأسبوع</span>
                <span className="text-lg font-bold text-rose-700 font-['Almarai']">
                  {Math.floor(Math.random() * 50) + 10} مستند
                </span>
              </div>
              <div className="flex items-center justify-between p-3 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl">
                <span className="text-sm text-gray-700 font-['Almarai'] font-medium">متوسط وقت المعالجة</span>
                <span className="text-lg font-bold text-indigo-700 font-['Almarai']">2.3 دقيقة</span>
              </div>
            </div>
          </div>

          {/* Performance Indicator */}
          <div className="bg-gradient-to-br from-violet-500 to-purple-600 p-6 rounded-2xl shadow-xl text-white">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-lg font-bold font-['Almarai']">أداء النظام</h3>
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-['Almarai'] opacity-90">معدل النجاح</span>
                <span className="text-xl font-bold font-['Almarai']">99.8%</span>
              </div>
              <div className="w-full bg-white bg-opacity-20 rounded-full h-2">
                <div className="bg-white h-2 rounded-full" style={{ width: '99.8%' }}></div>
              </div>
              <div className="flex items-center justify-between text-sm opacity-90">
                <span className="font-['Almarai']">استجابة سريعة</span>
                <span className="font-['Almarai']">أداء ممتاز</span>
              </div>
            </div>
          </div>
        </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardAlt;
