import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import { useNetworkRecovery } from '../hooks/useNetworkRecovery';

// Import CSS files for react-pdf
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';

// Set up PDF.js worker with fallback
const setupPDFWorker = () => {
  const workerSources = [
    `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`,
    `https://unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`,
    '/pdf.worker.min.js' // Local fallback
  ];

  let currentWorkerIndex = 0;

  const tryNextWorker = () => {
    if (currentWorkerIndex < workerSources.length) {
      pdfjs.GlobalWorkerOptions.workerSrc = workerSources[currentWorkerIndex];
      currentWorkerIndex++;
      return true;
    }
    return false;
  };

  // Try first worker
  tryNextWorker();

  return { tryNextWorker };
};

interface PDFViewerProps {
  documentId: string;
  className?: string;
  onError?: (error: Error) => void;
  onSuccess?: () => void;
  enableDownload?: boolean;
  enablePrint?: boolean;
}

type ViewerMode = 'react-pdf' | 'iframe' | 'object' | 'download-link';

interface ViewerState {
  mode: ViewerMode;
  numPages: number;
  pageNumber: number;
  loading: boolean;
  error: string | null;
  pdfData: string | null;
  scale: number;
  retryCount: number;
}

const EnhancedPDFViewer: React.FC<PDFViewerProps> = ({
  documentId,
  className = '',
  onError,
  onSuccess,
  enableDownload = true,
  enablePrint = true
}) => {
  const [state, setState] = useState<ViewerState>({
    mode: 'react-pdf',
    numPages: 0,
    pageNumber: 1,
    loading: false,
    error: null,
    pdfData: null,
    scale: 1.0,
    retryCount: 0
  });

  const [workerReady, setWorkerReady] = useState(false);
  const [workerError, setWorkerError] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const { isOnline, manualRetry } = useNetworkRecovery();
  const workerSetup = useRef(setupPDFWorker());

  // Initialize PDF worker
  useEffect(() => {
    const initializePDFWorker = async () => {
      try {
        const workerResult = workerSetup.current;
        // Call the function to check if worker setup is successful
        if (workerResult.tryNextWorker()) {
          setWorkerReady(true);
        }
      } catch (error) {
        console.error('PDF worker initialization failed:', error);
        setWorkerError(true);
      }
    };

    initializePDFWorker();
  }, []);

  const viewerModes: Array<{ mode: ViewerMode; name: string; description: string }> = [
    { mode: 'react-pdf', name: 'React PDF', description: 'عارض PDF المتقدم' },
    { mode: 'iframe', name: 'IFrame', description: 'عارض المتصفح المدمج' },
    { mode: 'object', name: 'Object', description: 'عارض HTML Object' },
    { mode: 'download-link', name: 'تحميل', description: 'رابط تحميل مباشر' }
  ];

  const getPDFUrl = useCallback(() => {
    const token = localStorage.getItem('token');
    const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';
    return `${API_BASE_URL}/documents/${documentId}/view?token=${encodeURIComponent(token || '')}`;
  }, [documentId]);

  const fetchPDFData = useCallback(async (): Promise<string> => {
    const response = await fetch(getPDFUrl());
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const contentType = response.headers.get('content-type');
    if (!contentType?.includes('application/pdf')) {
      throw new Error(`Expected PDF but got ${contentType}`);
    }

    const arrayBuffer = await response.arrayBuffer();
    const uint8Array = new Uint8Array(arrayBuffer);
    
    // Verify PDF signature
    const pdfSignature = uint8Array.slice(0, 4);
    const expectedSignature = new Uint8Array([0x25, 0x50, 0x44, 0x46]); // %PDF
    
    if (!pdfSignature.every((byte, index) => byte === expectedSignature[index])) {
      throw new Error('Invalid PDF format');
    }

    // Convert to data URL
    const blob = new Blob([arrayBuffer], { type: 'application/pdf' });
    return URL.createObjectURL(blob);
  }, [getPDFUrl]);

  const loadPDF = useCallback(async (mode: ViewerMode = state.mode) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      if (!isOnline) {
        throw new Error('لا يوجد اتصال بالإنترنت');
      }

      const pdfUrl = await fetchPDFData();
      
      setState(prev => ({
        ...prev,
        loading: false,
        pdfData: pdfUrl,
        mode,
        retryCount: 0
      }));

      onSuccess?.();
    } catch (error: any) {
      console.error('PDF loading error:', error);
      
      setState(prev => ({
        ...prev,
        loading: false,
        error: error.message,
        retryCount: prev.retryCount + 1
      }));

      onError?.(error);
      
      // Auto-fallback to next viewer mode
      await tryNextViewerMode();
    }
  }, [state.mode, isOnline, fetchPDFData, onSuccess, onError]);

  const tryNextViewerMode = useCallback(async () => {
    const currentIndex = viewerModes.findIndex(vm => vm.mode === state.mode);
    const nextIndex = currentIndex + 1;
    
    if (nextIndex < viewerModes.length) {
      const nextMode = viewerModes[nextIndex].mode;
      console.log(`Falling back to viewer mode: ${nextMode}`);
      
      setState(prev => ({ ...prev, mode: nextMode, error: null }));
      
      // Small delay before trying next mode
      setTimeout(() => loadPDF(nextMode), 1000);
    }
  }, [state.mode, viewerModes, loadPDF]);

  const handleDocumentLoadSuccess = useCallback(({ numPages }: { numPages: number }) => {
    setState(prev => ({ ...prev, numPages, loading: false, error: null }));
    onSuccess?.();
  }, [onSuccess]);

  const handleDocumentLoadError = useCallback((error: Error) => {
    console.error('React-PDF load error:', error);

    // Try next PDF.js worker source
    if (workerSetup.current.tryNextWorker()) {
      console.log('Trying next PDF.js worker source...');
      setTimeout(() => loadPDF(), 1000);
      return;
    }

    setState(prev => ({ ...prev, error: error.message, loading: false }));
    tryNextViewerMode();
  }, [loadPDF, tryNextViewerMode]);

  const changePage = useCallback((delta: number) => {
    setState(prev => ({
      ...prev,
      pageNumber: Math.max(1, Math.min(prev.numPages, prev.pageNumber + delta))
    }));
  }, []);

  const changeScale = useCallback((delta: number) => {
    setState(prev => ({
      ...prev,
      scale: Math.max(0.5, Math.min(3.0, prev.scale + delta))
    }));
  }, []);

  const downloadPDF = useCallback(() => {
    const link = document.createElement('a');
    link.href = getPDFUrl();
    link.download = `document-${documentId}.pdf`;
    link.click();
  }, [getPDFUrl, documentId]);

  const printPDF = useCallback(() => {
    if (state.pdfData) {
      const printWindow = window.open(state.pdfData);
      printWindow?.print();
    }
  }, [state.pdfData]);

  useEffect(() => {
    loadPDF();
  }, [documentId]);

  const renderViewer = () => {
    if (state.loading) {
      return (
        <div className="flex items-center justify-center h-96 bg-gray-100 rounded-lg">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل المستند...</p>
            <p className="text-sm text-gray-500 mt-2">الوضع: {viewerModes.find(vm => vm.mode === state.mode)?.name}</p>
          </div>
        </div>
      );
    }

    if (state.error && !state.pdfData) {
      return (
        <div className="flex items-center justify-center h-96 bg-red-50 rounded-lg">
          <div className="text-center">
            <div className="text-red-600 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-red-800 mb-2">فشل في تحميل المستند</h3>
            <p className="text-red-600 mb-4">{state.error}</p>
            <div className="space-y-2">
              <button
                onClick={() => loadPDF()}
                className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 mx-2"
              >
                إعادة المحاولة
              </button>
              {enableDownload && (
                <button
                  onClick={downloadPDF}
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 mx-2"
                >
                  تحميل المستند
                </button>
              )}
            </div>
            <p className="text-xs text-gray-500 mt-4">
              محاولات: {state.retryCount} | الوضع: {viewerModes.find(vm => vm.mode === state.mode)?.name}
            </p>
          </div>
        </div>
      );
    }

    switch (state.mode) {
      case 'react-pdf':
        return (
          <div className="border rounded-lg overflow-hidden">
            <Document
              file={state.pdfData}
              onLoadSuccess={handleDocumentLoadSuccess}
              onLoadError={handleDocumentLoadError}
              loading={<div className="p-4 text-center">جاري تحميل المستند...</div>}
              error={<div className="p-4 text-center text-red-600">فشل في تحميل المستند</div>}
            >
              <Page
                pageNumber={state.pageNumber}
                scale={state.scale}
                loading={<div className="p-4 text-center">جاري تحميل الصفحة...</div>}
                error={<div className="p-4 text-center text-red-600">فشل في تحميل الصفحة</div>}
              />
            </Document>
          </div>
        );

      case 'iframe':
        return (
          <iframe
            src={state.pdfData || getPDFUrl()}
            className="w-full h-96 border rounded-lg"
            title="PDF Viewer"
            onError={() => tryNextViewerMode()}
          />
        );

      case 'object':
        return (
          <object
            data={state.pdfData || getPDFUrl()}
            type="application/pdf"
            className="w-full h-96 border rounded-lg"
            onError={() => tryNextViewerMode()}
          >
            <p className="p-4 text-center">
              متصفحك لا يدعم عرض PDF. 
              <button onClick={downloadPDF} className="text-blue-600 hover:underline mr-2">
                تحميل المستند
              </button>
            </p>
          </object>
        );

      case 'download-link':
        return (
          <div className="flex items-center justify-center h-96 bg-gray-100 rounded-lg">
            <div className="text-center">
              <div className="text-gray-600 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">تحميل المستند</h3>
              <p className="text-gray-600 mb-4">لا يمكن عرض المستند في المتصفح</p>
              <button
                onClick={downloadPDF}
                className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700"
              >
                تحميل PDF
              </button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={`pdf-viewer ${className}`} ref={containerRef}>
      {/* Controls */}
      {state.mode === 'react-pdf' && state.numPages > 0 && (
        <div className="flex items-center justify-between bg-gray-100 p-3 rounded-t-lg">
          <div className="flex items-center space-x-2 space-x-reverse">
            <button
              onClick={() => changePage(-1)}
              disabled={state.pageNumber <= 1}
              className="bg-white border px-3 py-1 rounded disabled:opacity-50"
            >
              السابق
            </button>
            <span className="text-sm">
              صفحة {state.pageNumber} من {state.numPages}
            </span>
            <button
              onClick={() => changePage(1)}
              disabled={state.pageNumber >= state.numPages}
              className="bg-white border px-3 py-1 rounded disabled:opacity-50"
            >
              التالي
            </button>
          </div>

          <div className="flex items-center space-x-2 space-x-reverse">
            <button
              onClick={() => changeScale(-0.1)}
              className="bg-white border px-2 py-1 rounded text-sm"
            >
              -
            </button>
            <span className="text-sm">{Math.round(state.scale * 100)}%</span>
            <button
              onClick={() => changeScale(0.1)}
              className="bg-white border px-2 py-1 rounded text-sm"
            >
              +
            </button>

            {enablePrint && (
              <button
                onClick={printPDF}
                className="bg-white border px-3 py-1 rounded text-sm hover:bg-gray-50"
              >
                طباعة
              </button>
            )}

            {enableDownload && (
              <button
                onClick={downloadPDF}
                className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700"
              >
                تحميل
              </button>
            )}
          </div>
        </div>
      )}

      {/* Viewer */}
      {renderViewer()}

      {/* Mode Selector (for debugging) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-4 p-3 bg-gray-100 rounded-lg">
          <h4 className="text-sm font-semibold mb-2">وضع العرض:</h4>
          <div className="flex space-x-2 space-x-reverse">
            {viewerModes.map((vm) => (
              <button
                key={vm.mode}
                onClick={() => loadPDF(vm.mode)}
                className={`px-3 py-1 rounded text-sm ${
                  state.mode === vm.mode
                    ? 'bg-blue-600 text-white'
                    : 'bg-white border hover:bg-gray-50'
                }`}
              >
                {vm.name}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedPDFViewer;
