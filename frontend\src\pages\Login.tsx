import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../services/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import SecurityAlert from '../components/SecurityAlert';

const Login: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [securityAlert, setSecurityAlert] = useState<{
    type: 'lockout' | 'attempts' | 'warning' | 'success' | 'error';
    message: string;
    attemptsRemaining?: number;
    lockedUntil?: string;
  } | null>(null);
  const [loading, setLoading] = useState(false);
  const { login, user } = useAuth();
  const { t } = useLanguage();
  const navigate = useNavigate();

  // Redirect if already logged in
  React.useEffect(() => {
    if (user) {
      navigate('/dashboard');
    }
  }, [user, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSecurityAlert(null);
    setLoading(true);

    try {
      await login(email, password);
      navigate('/dashboard');
    } catch (err: any) {
      // Parse error response for security information
      const errorMessage = err.message;

      // Check if it's an account lockout error (status 423)
      if (err.response?.status === 423) {
        const responseData = err.response.data;
        setSecurityAlert({
          type: 'lockout',
          message: responseData.message || 'تم قفل الحساب مؤقتاً',
          lockedUntil: responseData.lockedUntil
        });
      }
      // Check if it's a failed attempt with remaining attempts info
      else if (err.response?.status === 401 && err.response?.data?.attemptsRemaining !== undefined) {
        const attemptsRemaining = err.response.data.attemptsRemaining;
        if (attemptsRemaining > 0) {
          setSecurityAlert({
            type: 'attempts',
            message: errorMessage,
            attemptsRemaining
          });
        } else {
          setSecurityAlert({
            type: 'warning',
            message: 'المحاولة التالية ستؤدي إلى قفل الحساب'
          });
        }
      }
      // Regular error
      else {
        setError(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-md mx-auto mt-8" dir="rtl">
      <div className="bg-white p-8 rounded-lg shadow-md">
        <h2 className="text-2xl font-bold text-center mb-6">{t.auth.login}</h2>

        {/* Security Alert Component */}
        {securityAlert && (
          <SecurityAlert
            type={securityAlert.type}
            message={securityAlert.message}
            attemptsRemaining={securityAlert.attemptsRemaining}
            lockedUntil={securityAlert.lockedUntil}
            onDismiss={() => setSecurityAlert(null)}
          />
        )}

        {/* Regular Error Display */}
        {error && !securityAlert && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="email" className="block text-gray-700 text-sm font-bold mb-2">
              {t.auth.email}
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
              dir="ltr"
            />
          </div>

          <div className="mb-6">
            <label htmlFor="password" className="block text-gray-700 text-sm font-bold mb-2">
              {t.auth.password}
            </label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {loading ? t.auth.loggingIn : t.auth.loginButton}
          </button>
        </form>

        <p className="text-center mt-4 text-gray-600">
          {t.auth.noAccount}{' '}
          <Link to="/register" className="text-blue-500 hover:text-blue-600">
            {t.auth.registerHere}
          </Link>
        </p>
      </div>
    </div>
  );
};

export default Login;
