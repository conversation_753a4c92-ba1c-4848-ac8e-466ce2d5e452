-- Migration: Update user_sessions table for location tracking
-- Description: Add missing columns to existing user_sessions table
-- Date: 2025-07-18

-- Add missing columns to user_sessions table
ALTER TABLE user_sessions 
ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'active',
ADD COLUMN IF NOT EXISTS login_time TIMESTAMP DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS logout_time TIMESTAMP,
ADD COLUMN IF NOT EXISTS current_latitude DECIMAL(10, 8),
ADD COLUMN IF NOT EXISTS current_longitude DECIMAL(11, 8),
ADD COLUMN IF NOT EXISTS current_accuracy INTEGER,
ADD COLUMN IF NOT EXISTS location_source VARCHAR(20) DEFAULT 'ip',
ADD COLUMN IF NOT EXISTS location_consent_given BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS location_last_updated TIMESTAMP,
ADD COLUMN IF NOT EXISTS device_type VARCHAR(50),
ADD COLUMN IF NOT EXISTS browser_name VA<PERSON>HA<PERSON>(100),
ADD COLUMN IF NOT EXISTS os_name VA<PERSON>HAR(100),
ADD COLUMN IF NOT EXISTS country_code CHAR(2),
ADD COLUMN IF NOT EXISTS country_name VARCHAR(100),
ADD COLUMN IF NOT EXISTS region_name VARCHAR(100),
ADD COLUMN IF NOT EXISTS city_name VARCHAR(100),
ADD COLUMN IF NOT EXISTS timezone VARCHAR(100),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT NOW();

-- Add constraints for new columns
ALTER TABLE user_sessions 
ADD CONSTRAINT IF NOT EXISTS check_session_status CHECK (status IN ('active', 'idle', 'offline')),
ADD CONSTRAINT IF NOT EXISTS check_location_source_sessions CHECK (location_source IN ('gps', 'ip', 'manual'));

-- Create missing indexes
CREATE INDEX IF NOT EXISTS idx_user_sessions_status ON user_sessions(status);
CREATE INDEX IF NOT EXISTS idx_user_sessions_location_consent ON user_sessions(location_consent_given);
CREATE INDEX IF NOT EXISTS idx_user_sessions_location_source ON user_sessions(location_source);

-- Update existing sessions with default values
UPDATE user_sessions 
SET 
  login_time = created_at,
  status = 'offline',
  location_source = 'ip',
  location_consent_given = false,
  updated_at = NOW()
WHERE login_time IS NULL;

-- Create or replace the update function with proper syntax
CREATE OR REPLACE FUNCTION update_user_session_activity(
    p_session_token VARCHAR(255),
    p_latitude DECIMAL(10, 8) DEFAULT NULL,
    p_longitude DECIMAL(11, 8) DEFAULT NULL,
    p_accuracy INTEGER DEFAULT NULL,
    p_source VARCHAR(20) DEFAULT 'ip'
)
RETURNS BOOLEAN AS $$
DECLARE
    session_exists BOOLEAN := FALSE;
BEGIN
    -- Update session activity
    UPDATE user_sessions 
    SET 
        last_activity = NOW(),
        status = 'active',
        current_latitude = COALESCE(p_latitude, current_latitude),
        current_longitude = COALESCE(p_longitude, current_longitude),
        current_accuracy = COALESCE(p_accuracy, current_accuracy),
        location_source = COALESCE(p_source, location_source),
        location_last_updated = CASE 
            WHEN p_latitude IS NOT NULL AND p_longitude IS NOT NULL 
            THEN NOW() 
            ELSE location_last_updated 
        END,
        updated_at = NOW()
    WHERE session_token = p_session_token;
    
    -- Check if any row was updated
    IF FOUND THEN
        session_exists := TRUE;
    END IF;
    
    RETURN session_exists;
END;
$$ LANGUAGE plpgsql;

-- Recreate the views with correct column references
DROP VIEW IF EXISTS active_user_sessions;
CREATE OR REPLACE VIEW active_user_sessions AS
SELECT 
    us.id as session_id,
    us.user_id,
    u.email,
    u.full_name,
    us.status,
    us.last_activity,
    us.login_time,
    us.current_latitude,
    us.current_longitude,
    us.current_accuracy,
    us.location_source,
    us.location_consent_given,
    us.location_last_updated,
    us.ip_address,
    us.device_type,
    us.browser_name,
    us.country_name,
    us.city_name,
    us.timezone,
    EXTRACT(EPOCH FROM (NOW() - us.last_activity)) as seconds_since_activity
FROM user_sessions us
JOIN users u ON us.user_id = u.id
WHERE us.status IN ('active', 'idle')
ORDER BY us.last_activity DESC;

DROP VIEW IF EXISTS user_location_summary;
CREATE OR REPLACE VIEW user_location_summary AS
SELECT 
    u.id as user_id,
    u.email,
    u.full_name,
    u.role,
    -- Current session info
    us.status as current_status,
    us.last_activity,
    us.current_latitude,
    us.current_longitude,
    us.location_consent_given,
    us.country_name,
    us.city_name,
    -- Location statistics
    COUNT(ulu.id) as total_location_updates,
    MAX(ulu.timestamp) as last_location_update,
    COUNT(DISTINCT ulu.country_code) as countries_visited,
    COUNT(DISTINCT ulu.city_name) as cities_visited,
    -- Activity statistics
    COUNT(ual.id) as total_activities,
    MAX(ual.timestamp) as last_activity_time
FROM users u
LEFT JOIN user_sessions us ON u.id = us.user_id AND us.status IN ('active', 'idle')
LEFT JOIN user_location_updates ulu ON u.id = ulu.user_id
LEFT JOIN user_activity_log ual ON u.id = ual.user_id
GROUP BY u.id, u.email, u.full_name, u.role, us.status, us.last_activity, 
         us.current_latitude, us.current_longitude, us.location_consent_given,
         us.country_name, us.city_name
ORDER BY us.last_activity DESC NULLS LAST;

-- Create user_permissions table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    permission_name VARCHAR(100) NOT NULL,
    granted_at TIMESTAMP DEFAULT NOW(),
    granted_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_id, permission_name)
);

-- Create index for user_permissions
CREATE INDEX IF NOT EXISTS idx_user_permissions_user_id ON user_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_permissions_permission_name ON user_permissions(permission_name);

-- Add the track_user_locations permission for admin users
INSERT INTO user_permissions (user_id, permission_name)
SELECT id, 'track_user_locations'
FROM users 
WHERE role = 'admin'
AND NOT EXISTS (
    SELECT 1 FROM user_permissions 
    WHERE user_id = users.id 
    AND permission_name = 'track_user_locations'
);

-- Add comments for new columns
COMMENT ON COLUMN user_sessions.status IS 'Current session status: active (< 15min), idle (15min-1hr), offline (> 1hr)';
COMMENT ON COLUMN user_sessions.location_consent_given IS 'Whether user has given consent for location tracking in this session';
COMMENT ON COLUMN user_sessions.current_latitude IS 'Current latitude coordinate of the user';
COMMENT ON COLUMN user_sessions.current_longitude IS 'Current longitude coordinate of the user';
COMMENT ON COLUMN user_sessions.location_source IS 'Source of location data: gps, ip, or manual';

SELECT 'User sessions table updated for location tracking successfully' as result;
