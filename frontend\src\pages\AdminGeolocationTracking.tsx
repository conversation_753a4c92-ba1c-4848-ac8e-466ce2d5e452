import React, { useState, useEffect, useRef } from 'react';
// import { useAuth } from '../services/AuthContext';

interface UserSession {
  session_id: string;
  user_id: string;
  email: string;
  full_name: string;
  role: string;
  status: 'active' | 'idle' | 'offline';
  last_activity: string;
  login_time: string;
  current_latitude: number;
  current_longitude: number;
  current_accuracy: number;
  location_source: string;
  location_consent_given: boolean;
  location_last_updated: string;
  ip_address: string;
  device_type: string;
  browser_name: string;
  country_name: string;
  city_name: string;
  timezone: string;
  seconds_since_activity: number;
  location_updates_count: number;
}

interface FilterOptions {
  status: string;
  search: string;
  locationConsent: string;
  deviceType: string;
}

const AdminGeolocationTracking: React.FC = () => {
  // const { user } = useAuth(); // Keep for potential future use
  const [sessions, setSessions] = useState<UserSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedUser, setSelectedUser] = useState<UserSession | null>(null);
  const [showMap, setShowMap] = useState(true);
  const [filters, setFilters] = useState<FilterOptions>({
    status: 'all',
    search: '',
    locationConsent: 'all',
    deviceType: 'all'
  });
  const [pagination, setPagination] = useState({
    total: 0,
    limit: 50,
    offset: 0,
    hasMore: false
  });
  const [realTimeUpdates, setRealTimeUpdates] = useState(true);
  const wsRef = useRef<WebSocket | null>(null);
  const mapRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    fetchUserSessions();
    if (realTimeUpdates) {
      connectWebSocket();
    }

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filters, pagination.offset, realTimeUpdates]);

  useEffect(() => {
    if (realTimeUpdates) {
      connectWebSocket();
    } else if (wsRef.current) {
      wsRef.current.close();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [realTimeUpdates]);

  const fetchUserSessions = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        status: filters.status,
        search: filters.search,
        limit: pagination.limit.toString(),
        offset: pagination.offset.toString()
      });

      const response = await fetch(`/api/location/admin/sessions?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const result = await response.json();
        setSessions(result.data.sessions);
        setPagination(prev => ({
          ...prev,
          total: result.data.pagination.total,
          hasMore: result.data.pagination.hasMore
        }));
        setError(null);
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'خطأ في جلب البيانات');
      }
    } catch (err) {
      setError('خطأ في الاتصال بالخادم');
      console.error('Error fetching user sessions:', err);
    } finally {
      setLoading(false);
    }
  };

  const connectWebSocket = () => {
    const token = localStorage.getItem('token');
    if (!token) return;

    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    const wsUrl = `${protocol}//${host}/ws/location-tracking?token=${token}`;

    try {
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('📍 Connected to admin location tracking');
        // Request current user locations
        wsRef.current?.send(JSON.stringify({ type: 'request_user_locations' }));
      };

      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          handleWebSocketMessage(data);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      wsRef.current.onclose = () => {
        console.log('📍 Admin location tracking disconnected');
        // Attempt to reconnect after 5 seconds
        setTimeout(() => {
          if (realTimeUpdates) {
            connectWebSocket();
          }
        }, 5000);
      };

    } catch (error) {
      console.error('Error connecting to WebSocket:', error);
    }
  };

  const handleWebSocketMessage = (data: any) => {
    switch (data.type) {
      case 'user_location_update':
        // Update specific user's location in real-time
        setSessions(prev => prev.map(session => 
          session.user_id === data.userId 
            ? {
                ...session,
                current_latitude: data.latitude,
                current_longitude: data.longitude,
                current_accuracy: data.accuracy,
                location_source: data.source,
                location_last_updated: data.timestamp,
                status: 'active',
                seconds_since_activity: 0
              }
            : session
        ));
        break;
      case 'user_status_change':
        // Update user status
        setSessions(prev => prev.map(session => 
          session.user_id === data.userId 
            ? { ...session, status: data.status }
            : session
        ));
        break;
      case 'user_locations':
        // Full location data update
        setSessions(data.data);
        break;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'idle': return 'bg-yellow-100 text-yellow-800';
      case 'offline': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'نشط';
      case 'idle': return 'خامل';
      case 'offline': return 'غير متصل';
      default: return 'غير معروف';
    }
  };

  const formatLastActivity = (seconds: number) => {
    if (seconds < 60) return 'الآن';
    if (seconds < 3600) return `${Math.floor(seconds / 60)} دقيقة`;
    if (seconds < 86400) return `${Math.floor(seconds / 3600)} ساعة`;
    return `${Math.floor(seconds / 86400)} يوم`;
  };

  const handleFilterChange = (key: keyof FilterOptions, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, offset: 0 }));
  };

  const handleUserSelect = (session: UserSession) => {
    setSelectedUser(session);
  };

  const exportData = async () => {
    try {
      const params = new URLSearchParams({
        status: filters.status,
        search: filters.search,
        format: 'csv'
      });

      const response = await fetch(`/api/location/admin/export?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `user-locations-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Error exporting data:', error);
      alert('خطأ في تصدير البيانات');
    }
  };

  if (loading && sessions.length === 0) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2 font-['Almarai']">
            تتبع المواقع الجغرافية للمستخدمين
          </h1>
          <p className="text-gray-600 font-['Almarai']">
            مراقبة مواقع المستخدمين النشطين في الوقت الفعلي
          </p>
        </div>

        {/* Controls */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            {/* Status Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 font-['Almarai']">
                حالة المستخدم
              </label>
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 font-['Almarai']"
              >
                <option value="all">جميع الحالات</option>
                <option value="active">نشط</option>
                <option value="idle">خامل</option>
                <option value="offline">غير متصل</option>
              </select>
            </div>

            {/* Search */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 font-['Almarai']">
                البحث
              </label>
              <input
                type="text"
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                placeholder="البحث بالاسم أو البريد الإلكتروني"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 font-['Almarai']"
              />
            </div>

            {/* Real-time Updates Toggle */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 font-['Almarai']">
                التحديثات المباشرة
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={realTimeUpdates}
                  onChange={(e) => setRealTimeUpdates(e.target.checked)}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="mr-2 text-sm text-gray-700 font-['Almarai']">
                  تفعيل التحديثات المباشرة
                </span>
              </label>
            </div>

            {/* View Toggle */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 font-['Almarai']">
                طريقة العرض
              </label>
              <div className="flex gap-2">
                <button
                  onClick={() => setShowMap(true)}
                  className={`px-3 py-2 rounded-md text-sm font-['Almarai'] transition-colors ${
                    showMap 
                      ? 'bg-primary-600 text-white' 
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  خريطة
                </button>
                <button
                  onClick={() => setShowMap(false)}
                  className={`px-3 py-2 rounded-md text-sm font-['Almarai'] transition-colors ${
                    !showMap 
                      ? 'bg-primary-600 text-white' 
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  قائمة
                </button>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 justify-end">
            <button
              onClick={fetchUserSessions}
              className="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-md transition-colors font-['Almarai']"
            >
              تحديث
            </button>
            <button
              onClick={exportData}
              className="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-md transition-colors font-['Almarai']"
            >
              تصدير البيانات
            </button>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <p className="text-red-800 font-['Almarai']">{error}</p>
          </div>
        )}

        {/* Content Area */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {showMap ? (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 font-['Almarai']">
                  خريطة المواقع
                </h3>
                <div 
                  ref={mapRef}
                  className="w-full h-96 bg-gray-100 rounded-lg flex items-center justify-center"
                >
                  <p className="text-gray-500 font-['Almarai']">
                    سيتم تحميل الخريطة هنا
                  </p>
                </div>
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="p-6 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900 font-['Almarai']">
                    قائمة المستخدمين ({pagination.total})
                  </h3>
                </div>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider font-['Almarai']">
                          المستخدم
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider font-['Almarai']">
                          الحالة
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider font-['Almarai']">
                          الموقع
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider font-['Almarai']">
                          آخر نشاط
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider font-['Almarai']">
                          الإجراءات
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {sessions.map((session) => (
                        <tr key={session.session_id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900 font-['Almarai']">
                                {session.full_name || session.email}
                              </div>
                              <div className="text-sm text-gray-500 font-['Almarai']">
                                {session.email}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full font-['Almarai'] ${getStatusColor(session.status)}`}>
                              {getStatusText(session.status)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-['Almarai']">
                            {session.city_name && session.country_name 
                              ? `${session.city_name}, ${session.country_name}`
                              : 'غير محدد'
                            }
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-['Almarai']">
                            {formatLastActivity(session.seconds_since_activity)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button
                              onClick={() => handleUserSelect(session)}
                              className="text-primary-600 hover:text-primary-900 font-['Almarai']"
                            >
                              عرض التفاصيل
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Statistics */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 font-['Almarai']">
                إحصائيات سريعة
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600 font-['Almarai']">المستخدمون النشطون:</span>
                  <span className="font-semibold text-green-600 font-['Almarai']">
                    {sessions.filter(s => s.status === 'active').length}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 font-['Almarai']">المستخدمون الخاملون:</span>
                  <span className="font-semibold text-yellow-600 font-['Almarai']">
                    {sessions.filter(s => s.status === 'idle').length}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 font-['Almarai']">إجمالي الجلسات:</span>
                  <span className="font-semibold text-gray-900 font-['Almarai']">
                    {sessions.length}
                  </span>
                </div>
              </div>
            </div>

            {/* Selected User Details */}
            {selectedUser && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 font-['Almarai']">
                  تفاصيل المستخدم
                </h3>
                <div className="space-y-3 text-sm">
                  <div>
                    <span className="text-gray-600 font-['Almarai']">الاسم: </span>
                    <span className="font-medium font-['Almarai']">{selectedUser.full_name || 'غير محدد'}</span>
                  </div>
                  <div>
                    <span className="text-gray-600 font-['Almarai']">البريد الإلكتروني: </span>
                    <span className="font-medium font-['Almarai']">{selectedUser.email}</span>
                  </div>
                  <div>
                    <span className="text-gray-600 font-['Almarai']">الحالة: </span>
                    <span className={`font-medium font-['Almarai'] ${
                      selectedUser.status === 'active' ? 'text-green-600' :
                      selectedUser.status === 'idle' ? 'text-yellow-600' : 'text-gray-600'
                    }`}>
                      {getStatusText(selectedUser.status)}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600 font-['Almarai']">الموقع: </span>
                    <span className="font-medium font-['Almarai']">
                      {selectedUser.city_name && selectedUser.country_name 
                        ? `${selectedUser.city_name}, ${selectedUser.country_name}`
                        : 'غير محدد'
                      }
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600 font-['Almarai']">الجهاز: </span>
                    <span className="font-medium font-['Almarai']">{selectedUser.device_type || 'غير محدد'}</span>
                  </div>
                  <div>
                    <span className="text-gray-600 font-['Almarai']">المتصفح: </span>
                    <span className="font-medium font-['Almarai']">{selectedUser.browser_name || 'غير محدد'}</span>
                  </div>
                  <div>
                    <span className="text-gray-600 font-['Almarai']">آخر تحديث للموقع: </span>
                    <span className="font-medium font-['Almarai']">
                      {selectedUser.location_last_updated 
                        ? new Date(selectedUser.location_last_updated).toLocaleString('ar-SA')
                        : 'غير محدد'
                      }
                    </span>
                  </div>
                </div>
                
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <button
                    onClick={() => window.open(`/admin/users/${selectedUser.user_id}/activity`, '_blank')}
                    className="w-full px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-md transition-colors font-['Almarai']"
                  >
                    عرض سجل الأنشطة
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminGeolocationTracking;
