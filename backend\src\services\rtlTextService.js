// Optional bidi-js import for bidirectional text processing
let bidi = null;
try {
  bidi = require('bidi-js');
} catch (error) {
  console.warn('bidi-js not available in RTL service - using fallback processing');
}

// RTL language codes (Arabic-only system)
const RTL_LANGUAGES = ['ar'];

// Unicode ranges for Arabic script
const RTL_UNICODE_RANGES = {
  ARABIC: [0x0600, 0x06FF],
  ARABIC_SUPPLEMENT: [0x0750, 0x077F],
  ARABIC_EXTENDED_A: [0x08A0, 0x08FF],
  ARABIC_PRESENTATION_FORMS_A: [0xFB50, 0xFDFF],
  ARABIC_PRESENTATION_FORMS_B: [0xFE70, 0xFEFF]
};

// Check if character is RTL
const isRTLCharacter = (charCode) => {
  for (const [start, end] of Object.values(RTL_UNICODE_RANGES)) {
    if (charCode >= start && charCode <= end) {
      return true;
    }
  }
  return false;
};

// Detect text direction based on content
const detectTextDirection = (text) => {
  if (!text || typeof text !== 'string') return 'ltr';

  // Check if text contains RTL characters
  for (let i = 0; i < text.length; i++) {
    if (isRTLCharacter(text.charCodeAt(i))) {
      return 'rtl';
    }
  }
  return 'ltr';
};

// Process bidirectional text
const processBidiText = (text) => {
  if (!text) return null;

  // If bidi-js is not available, return original text
  if (!bidi) {
    return text;
  }

  try {
    // For now, just return the original text since bidi-js might not be properly configured
    // In production, you would properly configure and use bidi-js
    return text;
  } catch (error) {
    console.warn('Bidirectional text processing failed:', error);
    return text;
  }
};

// Calculate text metrics for RTL positioning
const calculateTextMetrics = (text, fontSize = 12, fontFamily = 'Almarai') => {
  // Approximate character width calculations
  const charWidths = {
    'Arial': 0.6,
    'Helvetica': 0.6,
    'Times': 0.5,
    'Courier': 0.6,
    'Almarai': 0.65, // Almarai font character width
  };
  
  const avgCharWidth = (charWidths[fontFamily] || 0.6) * fontSize;
  const textWidth = text.length * avgCharWidth;
  const textHeight = fontSize * 1.2; // Line height
  
  return {
    width: textWidth,
    height: textHeight,
    avgCharWidth,
    characterCount: text.length
  };
};

// Get optimal positioning for Arabic RTL text
const getOptimalTextPosition = (arabicText, x, y, pageWidth, pageHeight, options = {}) => {
  const {
    fontSize = 12,
    fontFamily = 'Almarai',
    alignment = 'start', // 'start', 'center', 'end'
    margin = 10
  } = options;

  const metrics = calculateTextMetrics(arabicText, fontSize, fontFamily);

  let finalX = x;
  let finalY = y;
  
  // Adjust X position based on RTL alignment for Arabic
  switch (alignment) {
    case 'start':
      // For RTL, start means right side
      finalX = Math.min(pageWidth - margin, x);
      break;
    case 'center':
      finalX = (pageWidth - metrics.width) / 2;
      break;
    case 'end':
      // For RTL, end means left side
      finalX = Math.max(margin, x - metrics.width);
      break;
    default:
      // Default RTL positioning
      finalX = Math.min(pageWidth - metrics.width - margin, x);
  }
  
  // Ensure text fits within page bounds
  finalX = Math.max(margin, Math.min(finalX, pageWidth - metrics.width - margin));
  finalY = Math.max(margin, Math.min(finalY, pageHeight - metrics.height - margin));
  
  return {
    x: finalX,
    y: finalY,
    direction: 'rtl',
    metrics,
    processedText: processBidiText(arabicText)
  };
};

// Analyze document layout for optimal signature placement
const analyzeDocumentLayout = (content, pageWidth = 600, pageHeight = 800) => {
  if (!content || pageWidth <= 0 || pageHeight <= 0) {
    // Return default positions for invalid input
    return {
      direction: 'ltr',
      hasArabicContent: false,
      language: 'en',
      suggestedPositions: [
        { x: 50, y: pageHeight - 100, reason: 'Default bottom-left position' },
        { x: pageWidth - 200, y: pageHeight - 100, reason: 'Default bottom-right position' },
        { x: pageWidth / 2 - 100, y: pageHeight - 100, reason: 'Default bottom-center position' }
      ]
    };
  }

  const direction = detectTextDirection(content);
  const hasArabicContent = direction === 'rtl';

  const suggestedPositions = [];

  if (hasArabicContent) {
    // RTL document - suggest positions on the left side
    suggestedPositions.push(
      { x: 50, y: pageHeight - 100, reason: 'RTL bottom-left position' },
      { x: 50, y: pageHeight / 2, reason: 'RTL middle-left position' },
      { x: pageWidth / 2 - 100, y: pageHeight - 100, reason: 'RTL bottom-center position' }
    );
  } else {
    // LTR document - suggest positions on the right side
    suggestedPositions.push(
      { x: pageWidth - 200, y: pageHeight - 100, reason: 'LTR bottom-right position' },
      { x: pageWidth - 200, y: pageHeight / 2, reason: 'LTR middle-right position' },
      { x: pageWidth / 2 - 100, y: pageHeight - 100, reason: 'LTR bottom-center position' }
    );
  }

  return {
    direction,
    hasArabicContent,
    language: hasArabicContent ? 'ar' : 'en',
    suggestedPositions
  };
};

// Format multilingual text with proper separation
const formatMultilingualText = (arabicText, englishText) => {
  if (!arabicText && !englishText) return '';

  const parts = [];

  if (arabicText) {
    parts.push(processBidiText(arabicText) || arabicText);
  }

  if (englishText) {
    parts.push(englishText);
  }

  return parts.join(' | ');
};

module.exports = {
  isRTLCharacter,
  detectTextDirection,
  processBidiText,
  calculateTextMetrics,
  getOptimalTextPosition,
  analyzeDocumentLayout,
  formatMultilingualText,
  RTL_LANGUAGES,
  RTL_UNICODE_RANGES
};
