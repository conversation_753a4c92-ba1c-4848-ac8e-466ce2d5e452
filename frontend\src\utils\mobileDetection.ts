/**
 * Mobile Detection Utilities
 * Provides device detection and mobile-specific capabilities
 */

export interface MobileCapabilities {
  isMobile: boolean;
  isIOS: boolean;
  isAndroid: boolean;
  isTablet: boolean;
  isPWA: boolean;
  browserName: string;
  osVersion: string;
  deviceModel: string;
}

export class MobileDetection {
  private userAgent: string;
  private capabilities: MobileCapabilities;

  constructor() {
    this.userAgent = navigator.userAgent;
    this.capabilities = this.detectCapabilities();
  }

  /**
   * Detect device and browser capabilities
   */
  private detectCapabilities(): MobileCapabilities {
    const isMobile = this.isMobileDevice();
    const isIOS = this.isIOSDevice();
    const isAndroid = this.isAndroidDevice();
    const isTablet = this.isTabletDevice();
    const isPWA = this.isPWAMode();

    return {
      isMobile,
      isIOS,
      isAndroid,
      isTablet,
      isPWA,
      browserName: this.getBrowserName(),
      osVersion: this.getOSVersion(),
      deviceModel: this.getDeviceModel()
    };
  }

  /**
   * Check if device is mobile
   */
  private isMobileDevice(): boolean {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(this.userAgent) ||
           (window.innerWidth <= 768);
  }

  /**
   * Check if device is iOS
   */
  private isIOSDevice(): boolean {
    return /iPad|iPhone|iPod/.test(this.userAgent) ||
           (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
  }

  /**
   * Check if device is Android
   */
  private isAndroidDevice(): boolean {
    return /Android/.test(this.userAgent);
  }

  /**
   * Check if device is tablet
   */
  private isTabletDevice(): boolean {
    return /iPad/.test(this.userAgent) ||
           (this.isAndroidDevice() && !/Mobile/.test(this.userAgent)) ||
           (window.innerWidth >= 768 && window.innerWidth <= 1024);
  }

  /**
   * Check if running as PWA
   */
  private isPWAMode(): boolean {
    return window.matchMedia('(display-mode: standalone)').matches ||
           (window.navigator as any).standalone === true ||
           document.referrer.includes('android-app://');
  }





  /**
   * Get iOS version
   */
  private getIOSVersion(): number {
    const match = this.userAgent.match(/OS (\d+)_(\d+)_?(\d+)?/);
    if (match) {
      return parseFloat(`${match[1]}.${match[2]}`);
    }
    return 0;
  }

  /**
   * Get browser name
   */
  private getBrowserName(): string {
    if (this.userAgent.includes('Chrome')) return 'Chrome';
    if (this.userAgent.includes('Firefox')) return 'Firefox';
    if (this.userAgent.includes('Safari') && !this.userAgent.includes('Chrome')) return 'Safari';
    if (this.userAgent.includes('Edge')) return 'Edge';
    if (this.userAgent.includes('Opera')) return 'Opera';
    return 'Unknown';
  }

  /**
   * Get OS version
   */
  private getOSVersion(): string {
    if (this.isIOSDevice()) {
      const match = this.userAgent.match(/OS (\d+)_(\d+)_?(\d+)?/);
      return match ? `${match[1]}.${match[2]}${match[3] ? '.' + match[3] : ''}` : 'Unknown';
    }
    
    if (this.isAndroidDevice()) {
      const match = this.userAgent.match(/Android (\d+(?:\.\d+)?)/);
      return match ? match[1] : 'Unknown';
    }
    
    return 'Unknown';
  }

  /**
   * Get device model
   */
  private getDeviceModel(): string {
    if (this.isIOSDevice()) {
      if (this.userAgent.includes('iPhone')) {
        // Try to detect iPhone model
        if (this.userAgent.includes('iPhone15')) return 'iPhone 15';
        if (this.userAgent.includes('iPhone14')) return 'iPhone 14';
        if (this.userAgent.includes('iPhone13')) return 'iPhone 13';
        if (this.userAgent.includes('iPhone12')) return 'iPhone 12';
        if (this.userAgent.includes('iPhone11')) return 'iPhone 11';
        if (this.userAgent.includes('iPhoneX')) return 'iPhone X';
        return 'iPhone';
      }
      if (this.userAgent.includes('iPad')) return 'iPad';
      if (this.userAgent.includes('iPod')) return 'iPod';
    }
    
    if (this.isAndroidDevice()) {
      // Try to extract Android device model
      const match = this.userAgent.match(/\(([^)]+)\)/);
      if (match && match[1]) {
        const parts = match[1].split(';');
        for (const part of parts) {
          const trimmed = part.trim();
          if (trimmed && !trimmed.includes('Android') && !trimmed.includes('wv')) {
            return trimmed;
          }
        }
      }
      return 'Android Device';
    }
    
    return 'Unknown Device';
  }

  /**
   * Get current capabilities
   */
  public getCapabilities(): MobileCapabilities {
    return { ...this.capabilities };
  }



  /**
   * Check if device should use mobile-optimized UI
   */
  public shouldUseMobileUI(): boolean {
    return this.capabilities.isMobile || this.capabilities.isPWA;
  }

  /**
   * Get device orientation
   */
  public getOrientation(): 'portrait' | 'landscape' {
    return window.innerHeight > window.innerWidth ? 'portrait' : 'landscape';
  }

  /**
   * Check if device supports vibration
   */
  public supportsVibration(): boolean {
    return 'vibrate' in navigator;
  }

  /**
   * Trigger haptic feedback (if supported)
   */
  public triggerHapticFeedback(type: 'success' | 'error' | 'warning' = 'success'): void {
    if (this.supportsVibration()) {
      const patterns = {
        success: [100],
        error: [100, 50, 100],
        warning: [50, 50, 50]
      };
      navigator.vibrate(patterns[type]);
    }
  }

  /**
   * Check if device is in dark mode
   */
  public isDarkMode(): boolean {
    return window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
  }

  /**
   * Get safe area insets for notched devices
   */
  public getSafeAreaInsets(): { top: number; bottom: number; left: number; right: number } {
    const style = getComputedStyle(document.documentElement);
    return {
      top: parseInt(style.getPropertyValue('--sat') || '0'),
      bottom: parseInt(style.getPropertyValue('--sab') || '0'),
      left: parseInt(style.getPropertyValue('--sal') || '0'),
      right: parseInt(style.getPropertyValue('--sar') || '0')
    };
  }
}

// Export singleton instance
export const mobileDetection = new MobileDetection();
export default mobileDetection;
