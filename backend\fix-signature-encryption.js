require('dotenv').config({ path: require('path').join(__dirname, '.env') });
const { query } = require('./src/models/database');
const { readFile, encryptData, saveFile } = require('./src/services/encryptionService');
const fs = require('fs');
const path = require('path');

async function fixSignatureEncryption() {
  try {
    console.log('🔧 Starting signature encryption fix...');
    
    // Get all signatures
    const result = await query('SELECT id, filename, file_path, file_size, mime_type FROM signatures ORDER BY upload_date DESC');
    
    console.log(`Found ${result.rows.length} signatures to check:`);
    
    for (const signature of result.rows) {
      console.log(`\n🔍 Processing signature: ${signature.filename} (ID: ${signature.id})`);
      
      try {
        // Check if file exists
        if (!fs.existsSync(signature.file_path)) {
          console.log('❌ File does not exist at path:', signature.file_path);
          continue;
        }
        
        // Read the current file
        const fileBuffer = await readFile(signature.file_path);
        console.log('📁 File read successfully, size:', fileBuffer.length, 'bytes');
        
        // Try to decrypt to see if it's corrupted
        try {
          const crypto = require('crypto-js');
          const encryptionKey = process.env.ENCRYPTION_KEY;
          const bytes = crypto.AES.decrypt(fileBuffer.toString(), encryptionKey);
          const decryptedData = bytes.toString(crypto.enc.Utf8);
          
          if (decryptedData && decryptedData.length > 0) {
            console.log('✅ Signature is already properly encrypted, skipping...');
            continue;
          }
        } catch (error) {
          console.log('🔧 Signature needs fixing, proceeding with repair...');
        }
        
        // The file is corrupted, we need to fix it
        // Since we can't recover the original image data from corrupted encryption,
        // we'll need to delete this signature and ask the user to re-upload
        console.log('⚠️  Signature is corrupted and cannot be recovered.');
        console.log('⚠️  This signature needs to be deleted and re-uploaded.');
        
        // Delete the corrupted signature
        await query('DELETE FROM signatures WHERE id = $1', [signature.id]);
        
        // Delete the corrupted file
        try {
          fs.unlinkSync(signature.file_path);
          console.log('🗑️  Deleted corrupted signature file and database record');
        } catch (fileError) {
          console.log('⚠️  Could not delete file:', fileError.message);
        }
        
      } catch (error) {
        console.log('❌ Error processing signature:', error.message);
      }
    }
    
    console.log('\n✅ Signature encryption fix completed!');
    console.log('📝 Note: Corrupted signatures have been removed. Please re-upload signatures.');
    
  } catch (error) {
    console.error('❌ Fix process error:', error);
  } finally {
    process.exit(0);
  }
}

fixSignatureEncryption();
