-- Migration: User Geolocation Tracking System
-- Description: Comprehensive user location tracking for admin monitoring
-- Date: 2025-07-18

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- User Sessions Table for Real-time Tracking
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    
    -- Session status
    status VARCHAR(20) DEFAULT 'active', -- 'active', 'idle', 'offline'
    last_activity TIMESTAMP DEFAULT NOW(),
    login_time TIMESTAMP DEFAULT NOW(),
    logout_time TIMESTAMP,
    
    -- Location data
    current_latitude DECIMAL(10, 8),
    current_longitude DECIMAL(11, 8),
    current_accuracy INTEGER, -- meters
    location_source VARCHAR(20) DEFAULT 'ip', -- 'gps', 'ip', 'manual'
    location_consent_given BOOLEAN DEFAULT FALSE,
    location_last_updated TIMESTAMP,
    
    -- Device and network info
    ip_address INET,
    user_agent TEXT,
    device_type VARCHAR(50), -- 'desktop', 'mobile', 'tablet'
    browser_name VARCHAR(100),
    os_name VARCHAR(100),
    
    -- Geographic details
    country_code CHAR(2),
    country_name VARCHAR(100),
    region_name VARCHAR(100),
    city_name VARCHAR(100),
    timezone VARCHAR(100),
    
    -- Metadata
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT check_session_status CHECK (status IN ('active', 'idle', 'offline')),
    CONSTRAINT check_location_source CHECK (location_source IN ('gps', 'ip', 'manual'))
);

-- Real-time Location Updates Table
CREATE TABLE IF NOT EXISTS user_location_updates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_id UUID REFERENCES user_sessions(id) ON DELETE CASCADE,
    
    -- Location coordinates
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    accuracy INTEGER, -- meters
    altitude DECIMAL(8, 2), -- meters
    heading DECIMAL(5, 2), -- degrees
    speed DECIMAL(8, 2), -- m/s
    
    -- Location metadata
    source VARCHAR(20) DEFAULT 'gps', -- 'gps', 'ip', 'manual'
    timestamp TIMESTAMP DEFAULT NOW(),
    
    -- Geographic details (from reverse geocoding)
    country_code CHAR(2),
    country_name VARCHAR(100),
    region_name VARCHAR(100),
    city_name VARCHAR(100),
    address TEXT,
    
    -- Quality indicators
    is_accurate BOOLEAN DEFAULT TRUE,
    confidence_level INTEGER DEFAULT 100, -- 0-100
    
    -- Metadata
    created_at TIMESTAMP DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT check_location_source_updates CHECK (source IN ('gps', 'ip', 'manual')),
    CONSTRAINT check_confidence_level CHECK (confidence_level >= 0 AND confidence_level <= 100)
);

-- User Activity Log for Timeline
CREATE TABLE IF NOT EXISTS user_activity_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_id UUID REFERENCES user_sessions(id) ON DELETE SET NULL,
    
    -- Activity details
    activity_type VARCHAR(50) NOT NULL, -- 'login', 'logout', 'document_upload', 'document_sign', 'location_update', 'page_view'
    activity_description TEXT,
    activity_data JSONB, -- Additional structured data
    
    -- Location at time of activity
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    location_accuracy INTEGER,
    
    -- Context
    ip_address INET,
    user_agent TEXT,
    page_url TEXT,
    referrer_url TEXT,
    
    -- Timing
    timestamp TIMESTAMP DEFAULT NOW(),
    duration_seconds INTEGER, -- For activities with duration
    
    -- Metadata
    created_at TIMESTAMP DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT check_activity_type CHECK (activity_type IN (
        'login', 'logout', 'document_upload', 'document_sign', 'document_view', 
        'document_download', 'location_update', 'page_view', 'settings_change',
        'password_change', 'profile_update', 'signature_upload'
    ))
);

-- Location Consent Management
CREATE TABLE IF NOT EXISTS user_location_consent (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Consent details
    consent_given BOOLEAN NOT NULL DEFAULT FALSE,
    consent_type VARCHAR(30) NOT NULL, -- 'browser_geolocation', 'ip_tracking', 'full_tracking'
    consent_date TIMESTAMP DEFAULT NOW(),
    consent_withdrawn_date TIMESTAMP,
    
    -- Consent context
    consent_ip_address INET,
    consent_user_agent TEXT,
    privacy_policy_version VARCHAR(20),
    
    -- Data retention preferences
    data_retention_days INTEGER DEFAULT 90,
    allow_admin_monitoring BOOLEAN DEFAULT TRUE,
    allow_location_history BOOLEAN DEFAULT TRUE,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT check_consent_type CHECK (consent_type IN ('browser_geolocation', 'ip_tracking', 'full_tracking')),
    CONSTRAINT unique_user_consent_type UNIQUE (user_id, consent_type)
);

-- Admin Location Access Log
CREATE TABLE IF NOT EXISTS admin_location_access_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    admin_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    accessed_user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    
    -- Access details
    access_type VARCHAR(30) NOT NULL, -- 'view_location', 'export_data', 'view_timeline', 'view_map'
    access_description TEXT,
    
    -- Data accessed
    data_type VARCHAR(50), -- 'current_location', 'location_history', 'activity_timeline', 'session_data'
    date_range_start TIMESTAMP,
    date_range_end TIMESTAMP,
    records_accessed INTEGER,
    
    -- Context
    ip_address INET,
    user_agent TEXT,
    
    -- Metadata
    timestamp TIMESTAMP DEFAULT NOW(),
    created_at TIMESTAMP DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT check_access_type CHECK (access_type IN (
        'view_location', 'export_data', 'view_timeline', 'view_map',
        'view_session', 'view_activity', 'generate_report'
    ))
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_status ON user_sessions(status);
CREATE INDEX IF NOT EXISTS idx_user_sessions_last_activity ON user_sessions(last_activity);
CREATE INDEX IF NOT EXISTS idx_user_sessions_session_token ON user_sessions(session_token);

CREATE INDEX IF NOT EXISTS idx_user_location_updates_user_id ON user_location_updates(user_id);
CREATE INDEX IF NOT EXISTS idx_user_location_updates_session_id ON user_location_updates(session_id);
CREATE INDEX IF NOT EXISTS idx_user_location_updates_timestamp ON user_location_updates(timestamp);
CREATE INDEX IF NOT EXISTS idx_user_location_updates_coordinates ON user_location_updates(latitude, longitude);

CREATE INDEX IF NOT EXISTS idx_user_activity_log_user_id ON user_activity_log(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_log_session_id ON user_activity_log(session_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_log_activity_type ON user_activity_log(activity_type);
CREATE INDEX IF NOT EXISTS idx_user_activity_log_timestamp ON user_activity_log(timestamp);

CREATE INDEX IF NOT EXISTS idx_user_location_consent_user_id ON user_location_consent(user_id);
CREATE INDEX IF NOT EXISTS idx_user_location_consent_type ON user_location_consent(consent_type);

CREATE INDEX IF NOT EXISTS idx_admin_location_access_log_admin_user_id ON admin_location_access_log(admin_user_id);
CREATE INDEX IF NOT EXISTS idx_admin_location_access_log_accessed_user_id ON admin_location_access_log(accessed_user_id);
CREATE INDEX IF NOT EXISTS idx_admin_location_access_log_timestamp ON admin_location_access_log(timestamp);

-- Create views for admin monitoring
CREATE OR REPLACE VIEW active_user_sessions AS
SELECT
    us.id as session_id,
    us.user_id,
    u.email,
    u.full_name,
    us.status,
    us.last_activity,
    us.login_time,
    us.current_latitude,
    us.current_longitude,
    us.current_accuracy,
    us.location_source,
    us.location_consent_given,
    us.location_last_updated,
    us.ip_address,
    us.device_type,
    us.browser_name,
    us.country_name,
    us.city_name,
    us.timezone,
    EXTRACT(EPOCH FROM (NOW() - us.last_activity)) as seconds_since_activity
FROM user_sessions us
JOIN users u ON us.user_id = u.id
WHERE us.status IN ('active', 'idle')
ORDER BY us.last_activity DESC;

CREATE OR REPLACE VIEW user_location_summary AS
SELECT
    u.id as user_id,
    u.email,
    u.full_name,
    u.role,
    -- Current session info
    us.status as current_status,
    us.last_activity,
    us.current_latitude,
    us.current_longitude,
    us.location_consent_given,
    us.country_name,
    us.city_name,
    -- Location statistics
    COUNT(ulu.id) as total_location_updates,
    MAX(ulu.timestamp) as last_location_update,
    COUNT(DISTINCT ulu.country_code) as countries_visited,
    COUNT(DISTINCT ulu.city_name) as cities_visited,
    -- Activity statistics
    COUNT(ual.id) as total_activities,
    MAX(ual.timestamp) as last_activity_time
FROM users u
LEFT JOIN user_sessions us ON u.id = us.user_id AND us.status IN ('active', 'idle')
LEFT JOIN user_location_updates ulu ON u.id = ulu.user_id
LEFT JOIN user_activity_log ual ON u.id = ual.user_id
GROUP BY u.id, u.email, u.full_name, u.role, us.status, us.last_activity,
         us.current_latitude, us.current_longitude, us.location_consent_given,
         us.country_name, us.city_name
ORDER BY us.last_activity DESC NULLS LAST;

-- Create functions for location tracking
CREATE OR REPLACE FUNCTION update_user_session_activity(
    p_session_token VARCHAR(255),
    p_latitude DECIMAL(10, 8) DEFAULT NULL,
    p_longitude DECIMAL(11, 8) DEFAULT NULL,
    p_accuracy INTEGER DEFAULT NULL,
    p_source VARCHAR(20) DEFAULT 'ip'
)
RETURNS BOOLEAN AS $$
DECLARE
    session_exists BOOLEAN;
BEGIN
    -- Update session activity
    UPDATE user_sessions
    SET
        last_activity = NOW(),
        status = 'active',
        current_latitude = COALESCE(p_latitude, current_latitude),
        current_longitude = COALESCE(p_longitude, current_longitude),
        current_accuracy = COALESCE(p_accuracy, current_accuracy),
        location_source = COALESCE(p_source, location_source),
        location_last_updated = CASE
            WHEN p_latitude IS NOT NULL AND p_longitude IS NOT NULL
            THEN NOW()
            ELSE location_last_updated
        END,
        updated_at = NOW()
    WHERE session_token = p_session_token;

    GET DIAGNOSTICS session_exists = FOUND;
    RETURN session_exists;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION log_user_activity(
    p_user_id UUID,
    p_session_id UUID,
    p_activity_type VARCHAR(50),
    p_activity_description TEXT DEFAULT NULL,
    p_activity_data JSONB DEFAULT NULL,
    p_latitude DECIMAL(10, 8) DEFAULT NULL,
    p_longitude DECIMAL(11, 8) DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_page_url TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    activity_id UUID;
BEGIN
    INSERT INTO user_activity_log (
        user_id, session_id, activity_type, activity_description, activity_data,
        latitude, longitude, ip_address, user_agent, page_url
    ) VALUES (
        p_user_id, p_session_id, p_activity_type, p_activity_description, p_activity_data,
        p_latitude, p_longitude, p_ip_address, p_user_agent, p_page_url
    ) RETURNING id INTO activity_id;

    RETURN activity_id;
END;
$$ LANGUAGE plpgsql;

-- Function to mark sessions as idle/offline
CREATE OR REPLACE FUNCTION update_session_status()
RETURNS VOID AS $$
BEGIN
    -- Mark sessions as idle if no activity for 15 minutes
    UPDATE user_sessions
    SET status = 'idle', updated_at = NOW()
    WHERE status = 'active'
    AND last_activity < NOW() - INTERVAL '15 minutes';

    -- Mark sessions as offline if no activity for 1 hour
    UPDATE user_sessions
    SET status = 'offline', logout_time = last_activity, updated_at = NOW()
    WHERE status IN ('active', 'idle')
    AND last_activity < NOW() - INTERVAL '1 hour';
END;
$$ LANGUAGE plpgsql;

-- Function for data retention cleanup
CREATE OR REPLACE FUNCTION cleanup_location_data()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
    temp_count INTEGER;
BEGIN
    -- Clean up old location updates (older than user's retention preference)
    DELETE FROM user_location_updates
    WHERE created_at < NOW() - INTERVAL '1 day' * (
        SELECT COALESCE(MIN(data_retention_days), 90)
        FROM user_location_consent
        WHERE user_id = user_location_updates.user_id
        AND consent_given = TRUE
    );
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;

    -- Clean up old activity logs (keep for 1 year max)
    DELETE FROM user_activity_log
    WHERE created_at < NOW() - INTERVAL '365 days';
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;

    -- Clean up old offline sessions (older than 30 days)
    DELETE FROM user_sessions
    WHERE status = 'offline'
    AND logout_time < NOW() - INTERVAL '30 days';
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;

    -- Clean up admin access logs (keep for 2 years)
    DELETE FROM admin_location_access_log
    WHERE created_at < NOW() - INTERVAL '730 days';
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;

    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_user_sessions_updated_at
    BEFORE UPDATE ON user_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER trigger_user_location_consent_updated_at
    BEFORE UPDATE ON user_location_consent
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();

-- Add table comments for documentation
COMMENT ON TABLE user_sessions IS 'Real-time user session tracking with location data';
COMMENT ON TABLE user_location_updates IS 'Detailed location update history for users';
COMMENT ON TABLE user_activity_log IS 'Comprehensive user activity timeline';
COMMENT ON TABLE user_location_consent IS 'User consent management for location tracking';
COMMENT ON TABLE admin_location_access_log IS 'Audit log for admin access to user location data';

-- Add column comments
COMMENT ON COLUMN user_sessions.status IS 'Current session status: active (< 15min), idle (15min-1hr), offline (> 1hr)';
COMMENT ON COLUMN user_sessions.location_consent_given IS 'Whether user has given consent for location tracking in this session';
COMMENT ON COLUMN user_location_updates.confidence_level IS 'Confidence level of location accuracy (0-100)';
COMMENT ON COLUMN user_activity_log.activity_data IS 'Additional structured data specific to the activity type';
COMMENT ON COLUMN user_location_consent.data_retention_days IS 'Number of days to retain location data for this user';

-- Grant permissions (adjust based on your application user)
-- GRANT SELECT, INSERT, UPDATE ON user_sessions TO app_user;
-- GRANT SELECT, INSERT ON user_location_updates TO app_user;
-- GRANT SELECT, INSERT ON user_activity_log TO app_user;
-- GRANT SELECT, INSERT, UPDATE ON user_location_consent TO app_user;
-- GRANT SELECT, INSERT ON admin_location_access_log TO app_user;

-- Create initial admin permission for location tracking
INSERT INTO user_permissions (user_id, permission_name)
SELECT id, 'track_user_locations'
FROM users
WHERE role = 'admin'
AND NOT EXISTS (
    SELECT 1 FROM user_permissions
    WHERE user_id = users.id
    AND permission_name = 'track_user_locations'
);

-- Migration complete
SELECT 'User Geolocation Tracking migration completed successfully' as result;
