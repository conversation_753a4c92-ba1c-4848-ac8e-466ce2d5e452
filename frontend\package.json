{"name": "esign-frontend", "version": "1.0.0", "private": true, "dependencies": {"@heroicons/react": "^2.2.0", "@pdf-lib/fontkit": "^1.1.1", "@types/node": "^16.18.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "axios": "^1.3.0", "bidi-js": "^1.0.3", "fontkit": "^2.0.4", "pdfjs-dist": "^3.11.174", "react": "^18.2.0", "react-dom": "^18.2.0", "react-pdf": "^7.7.3", "react-router-dom": "^6.8.0", "typescript": "^4.9.0"}, "devDependencies": {"@types/react-router-dom": "^5.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "react-scripts": "^5.0.1", "tailwindcss": "^3.2.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3001"}