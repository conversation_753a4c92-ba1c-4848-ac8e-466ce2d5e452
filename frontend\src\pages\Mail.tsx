import React, { useState, useEffect } from 'react';
import { useAuth } from '../services/AuthContext';
import { documentAPI } from '../services/api';

interface PendingDocument {
  id: string;
  original_filename: string;
  file_size: number;
  uploaded_by: string;
  uploader_email: string;
  uploaded_at: string;
  status: 'pending' | 'signed' | 'rejected';
  notes?: string;
}

const Mail: React.FC = () => {
  const { user, hasPermission } = useAuth();
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [pendingDocuments, setPendingDocuments] = useState<PendingDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [notes, setNotes] = useState('');

  useEffect(() => {
    if (hasPermission('sign_documents')) {
      fetchPendingDocuments();
    } else {
      setLoading(false);
    }
  }, [user, hasPermission]);

  const fetchPendingDocuments = async () => {
    try {
      setLoading(true);
      const response = await documentAPI.getPendingDocuments();
      setPendingDocuments(response.data.documents || []);
    } catch (error) {
      console.error('Error fetching pending documents:', error);
      setError('فشل في تحميل المستندات المعلقة');
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      if (selectedFile.type !== 'application/pdf') {
        setError('يرجى اختيار ملف PDF فقط');
        return;
      }
      setFile(selectedFile);
      setError('');
    }
  };

  const handleUpload = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!file) {
      setError('يرجى اختيار ملف PDF');
      return;
    }

    setUploading(true);
    setError('');
    setSuccess('');

    try {
      const formData = new FormData();
      formData.append('document', file);
      if (notes.trim()) {
        formData.append('notes', notes.trim());
      }

      await documentAPI.uploadForReview(formData);
      
      setSuccess('تم رفع المستند بنجاح! سيتم مراجعته من قبل الإدارة.');
      setFile(null);
      setNotes('');
      
      // Reset file input
      const fileInput = document.getElementById('file-input') as HTMLInputElement;
      if (fileInput) fileInput.value = '';
      
      // Refresh pending documents if user is admin
      if (hasPermission('sign_documents')) {
        fetchPendingDocuments();
      }
      
    } catch (error: any) {
      console.error('Upload error:', error);
      setError(error.response?.data?.error || 'فشل في رفع المستند');
    } finally {
      setUploading(false);
    }
  };

  const handleSignDocument = async (documentId: string) => {
    try {
      setError('');
      await documentAPI.signPendingDocument(documentId);
      setSuccess('تم توقيع المستند بنجاح!');
      fetchPendingDocuments();
    } catch (error: any) {
      console.error('Signing error:', error);
      setError(error.response?.data?.error || 'فشل في توقيع المستند');
    }
  };

  const handleRejectDocument = async (documentId: string, reason: string) => {
    try {
      setError('');
      await documentAPI.rejectPendingDocument(documentId, reason);
      setSuccess('تم رفض المستند');
      fetchPendingDocuments();
    } catch (error: any) {
      console.error('Rejection error:', error);
      setError(error.response?.data?.error || 'فشل في رفض المستند');
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 بايت';
    const k = 1024;
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8 font-['Almarai'] max-w-6xl" dir="rtl">
      {/* Header */}
      <div className="mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-2 leading-tight">
          {hasPermission('sign_documents')
            ? 'صندوق الوارد - مراجعة المستندات'
            : 'إرسال المستندات للمراجعة'
          }
        </h1>
        <p className="text-sm sm:text-base text-gray-600 leading-relaxed">
          {hasPermission('sign_documents')
            ? 'مراجعة وتوقيع المستندات المرسلة من المستخدمين'
            : 'إرسال مستندات PDF للمراجعة والتوقيع من قبل الإدارة'
          }
        </p>
      </div>

      {/* Error/Success Messages */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-3 sm:px-4 py-2 sm:py-3 rounded mb-3 sm:mb-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-4 w-4 sm:h-5 sm:w-5 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="mr-2 sm:mr-3 min-w-0 flex-1">
              <p className="text-xs sm:text-sm break-words">{error}</p>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-3 sm:px-4 py-2 sm:py-3 rounded mb-3 sm:mb-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-4 w-4 sm:h-5 sm:w-5 text-green-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="mr-2 sm:mr-3 min-w-0 flex-1">
              <p className="text-xs sm:text-sm break-words">{success}</p>
            </div>
          </div>
        </div>
      )}

      {/* Upload Form - Only for regular users */}
      {!hasPermission('sign_documents') && (
        <div className="bg-white rounded-lg shadow-md p-4 sm:p-6 mb-6 sm:mb-8">
          <h2 className="text-lg sm:text-xl font-bold text-gray-800 mb-4">إرسال مستند للمراجعة</h2>
          
          <form onSubmit={handleUpload} className="space-y-4 sm:space-y-6">
            <div>
              <label htmlFor="file-input" className="block text-sm sm:text-base font-medium text-gray-700 mb-2">
                اختر ملف PDF
              </label>
              <input
                id="file-input"
                type="file"
                accept=".pdf"
                onChange={handleFileChange}
                className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm sm:text-base"
                disabled={uploading}
              />
            </div>

            {file && (
              <div className="bg-blue-50 border border-blue-200 rounded-md p-3 sm:p-4">
                <p className="text-blue-800 text-sm sm:text-base">
                  <strong>الملف المحدد:</strong> {file.name}
                </p>
                <p className="text-blue-600 text-xs sm:text-sm">
                  الحجم: {formatFileSize(file.size)}
                </p>
              </div>
            )}

            <div>
              <label htmlFor="notes" className="block text-sm sm:text-base font-medium text-gray-700 mb-2">
                ملاحظات (اختياري)
              </label>
              <textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="أضف أي ملاحظات أو تعليمات خاصة بالمستند..."
                className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm sm:text-base"
                rows={3}
                disabled={uploading}
              />
            </div>

            <button
              type="submit"
              disabled={uploading || !file}
              className="w-full sm:w-auto px-4 sm:px-6 py-2 sm:py-3 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 text-sm sm:text-base font-medium"
            >
              {uploading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                  جاري الرفع...
                </div>
              ) : (
                'إرسال للمراجعة'
              )}
            </button>
          </form>
        </div>
      )}

      {/* Pending Documents - Only for admins */}
      {hasPermission('sign_documents') && (
        <div className="bg-white rounded-lg shadow-md p-4 sm:p-6">
          <h2 className="text-lg sm:text-xl font-bold text-gray-800 mb-4">المستندات المعلقة للمراجعة</h2>
          
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          ) : pendingDocuments.length === 0 ? (
            <div className="text-center py-8">
              <div className="w-16 h-16 mx-auto mb-4 text-gray-300">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <p className="text-gray-600">لا توجد مستندات معلقة للمراجعة</p>
            </div>
          ) : (
            <div className="space-y-4">
              {pendingDocuments.map((doc) => (
                <div key={doc.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3">
                    <div className="mb-2 sm:mb-0">
                      <h3 className="font-medium text-gray-900 text-sm sm:text-base">{doc.original_filename}</h3>
                      <p className="text-xs sm:text-sm text-gray-600">
                        من: {doc.uploader_email} | {formatFileSize(doc.file_size)} | {formatDate(doc.uploaded_at)}
                      </p>
                    </div>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      doc.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      doc.status === 'signed' ? 'bg-green-100 text-green-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {doc.status === 'pending' ? 'معلق' : doc.status === 'signed' ? 'موقع' : 'مرفوض'}
                    </span>
                  </div>
                  
                  {doc.notes && (
                    <div className="mb-3 p-2 bg-gray-50 rounded text-xs sm:text-sm text-gray-700">
                      <strong>ملاحظات:</strong> {doc.notes}
                    </div>
                  )}
                  
                  {doc.status === 'pending' && (
                    <div className="flex flex-col sm:flex-row gap-2">
                      <button
                        onClick={() => handleSignDocument(doc.id)}
                        className="px-3 py-1 bg-green-500 text-white rounded text-xs sm:text-sm hover:bg-green-600 transition-colors"
                      >
                        توقيع
                      </button>
                      <button
                        onClick={() => {
                          const reason = prompt('سبب الرفض:');
                          if (reason) handleRejectDocument(doc.id, reason);
                        }}
                        className="px-3 py-1 bg-red-500 text-white rounded text-xs sm:text-sm hover:bg-red-600 transition-colors"
                      >
                        رفض
                      </button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Mail;
