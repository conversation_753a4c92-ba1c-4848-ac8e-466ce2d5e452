require('dotenv').config({ path: require('path').join(__dirname, '.env') });
const { query } = require('./src/models/database');
const { readFile, decryptData } = require('./src/services/encryptionService');
const fs = require('fs');

async function debugSignatureEncryption() {
  try {
    console.log('🔍 Environment check...');
    console.log('ENCRYPTION_KEY exists:', !!process.env.ENCRYPTION_KEY);
    console.log('ENCRYPTION_KEY length:', process.env.ENCRYPTION_KEY ? process.env.ENCRYPTION_KEY.length : 0);

    console.log('🔍 Checking signatures in database...');
    
    // Get all signatures
    const result = await query('SELECT id, filename, file_path, file_size, mime_type FROM signatures ORDER BY upload_date DESC LIMIT 5');
    
    console.log(`Found ${result.rows.length} signatures:`);
    result.rows.forEach((sig, index) => {
      console.log(`${index + 1}. ID: ${sig.id}, File: ${sig.filename}, Path: ${sig.file_path}`);
    });
    
    if (result.rows.length > 0) {
      const signature = result.rows[0];
      console.log(`\n🔍 Testing decryption for signature: ${signature.filename}`);
      
      try {
        // Check if file exists
        if (!fs.existsSync(signature.file_path)) {
          console.log('❌ File does not exist at path:', signature.file_path);
          return;
        }
        
        console.log('✅ File exists at path:', signature.file_path);
        
        // Read the file
        const fileBuffer = await readFile(signature.file_path);
        console.log('📁 File read successfully, size:', fileBuffer.length, 'bytes');
        console.log('📁 File content type:', typeof fileBuffer);
        console.log('📁 File content preview (first 100 chars):', fileBuffer.toString().substring(0, 100));
        
        // Try to decrypt
        console.log('\n🔓 Attempting to decrypt...');
        const decryptedData = decryptData(fileBuffer.toString());
        console.log('✅ Decryption successful!');
        console.log('📄 Decrypted data length:', decryptedData.length);
        console.log('📄 Decrypted data preview (first 50 chars):', decryptedData.substring(0, 50));
        
        // Check if it's valid base64
        try {
          const buffer = Buffer.from(decryptedData, 'base64');
          console.log('✅ Decrypted data is valid base64, buffer size:', buffer.length);
        } catch (e) {
          console.log('❌ Decrypted data is not valid base64:', e.message);
        }
        
      } catch (error) {
        console.log('❌ Error during decryption test:', error.message);
        console.log('Error details:', error);
      }
    }
    
  } catch (error) {
    console.error('❌ Database error:', error);
  } finally {
    process.exit(0);
  }
}

debugSignatureEncryption();
