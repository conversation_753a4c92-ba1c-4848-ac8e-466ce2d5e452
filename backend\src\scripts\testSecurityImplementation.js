const axios = require('axios');
require('dotenv').config();
const { query } = require('../models/database');

/**
 * Comprehensive security implementation test
 */
class SecurityTester {
  constructor(baseUrl = 'http://localhost:3001') {
    this.baseUrl = baseUrl;
    this.testResults = {
      passed: 0,
      failed: 0,
      warnings: 0,
      tests: []
    };
  }

  log(status, category, message, details = null) {
    const result = { status, category, message, details, timestamp: new Date().toISOString() };
    this.testResults.tests.push(result);
    
    const icon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
    console.log(`${icon} [${category}] ${message}`);
    
    if (details) {
      console.log(`   Details: ${JSON.stringify(details, null, 2)}`);
    }

    if (status === 'PASS') this.testResults.passed++;
    else if (status === 'FAIL') this.testResults.failed++;
    else this.testResults.warnings++;
  }

  async testSecurityHeaders() {
    console.log('\n🛡️  Testing Security Headers...');
    
    try {
      const response = await axios.get(`${this.baseUrl}/health`, {
        validateStatus: () => true
      });

      const headers = response.headers;

      // Test CSP header
      if (headers['content-security-policy'] || headers['content-security-policy-report-only']) {
        this.log('PASS', 'CSP', 'Content Security Policy header present');
        
        const csp = headers['content-security-policy'] || headers['content-security-policy-report-only'];
        if (csp.includes("default-src 'self'")) {
          this.log('PASS', 'CSP', 'Default-src properly restricted');
        } else {
          this.log('FAIL', 'CSP', 'Default-src not properly restricted');
        }
      } else {
        this.log('FAIL', 'CSP', 'Content Security Policy header missing');
      }

      // Test HSTS
      if (headers['strict-transport-security']) {
        this.log('PASS', 'HSTS', 'HSTS header present');
      } else {
        this.log('WARN', 'HSTS', 'HSTS header missing (expected in development)');
      }

      // Test X-Frame-Options
      if (headers['x-frame-options']) {
        this.log('PASS', 'Frame Protection', 'X-Frame-Options header present');
      } else {
        this.log('FAIL', 'Frame Protection', 'X-Frame-Options header missing');
      }

      // Test X-Content-Type-Options
      if (headers['x-content-type-options']) {
        this.log('PASS', 'MIME Protection', 'X-Content-Type-Options header present');
      } else {
        this.log('FAIL', 'MIME Protection', 'X-Content-Type-Options header missing');
      }

      // Test X-XSS-Protection
      if (headers['x-xss-protection']) {
        this.log('PASS', 'XSS Protection', 'X-XSS-Protection header present');
      } else {
        this.log('FAIL', 'XSS Protection', 'X-XSS-Protection header missing');
      }

    } catch (error) {
      this.log('FAIL', 'Headers', 'Failed to test security headers', error.message);
    }
  }

  async testInputSanitization() {
    console.log('\n🧹 Testing Input Sanitization...');

    const maliciousInputs = [
      '<script>alert("xss")</script>',
      'javascript:alert("xss")',
      '"><script>alert("xss")</script>',
      "'; DROP TABLE users; --",
      'SELECT * FROM users WHERE id = 1',
      '<img src=x onerror=alert("xss")>',
      'data:text/html,<script>alert("xss")</script>'
    ];

    for (const input of maliciousInputs) {
      try {
        const response = await axios.post(`${this.baseUrl}/api/auth/register`, {
          email: `test${Date.now()}@example.com`,
          password: input
        }, {
          validateStatus: () => true
        });

        if (response.status === 400 && response.data.message.includes('كلمة المرور')) {
          this.log('PASS', 'Input Sanitization', `Malicious input rejected: ${input.substring(0, 20)}...`);
        } else {
          this.log('FAIL', 'Input Sanitization', `Malicious input not properly handled: ${input.substring(0, 20)}...`);
        }
      } catch (error) {
        this.log('WARN', 'Input Sanitization', `Test error for input: ${input.substring(0, 20)}...`);
      }
    }
  }

  async testPasswordValidation() {
    console.log('\n🔐 Testing Password Validation...');

    const weakPasswords = [
      'password',
      '123456',
      'abc123',
      'Password1', // Missing special character
      'password123!', // Missing uppercase
      'PASSWORD123!', // Missing lowercase
      'Password!', // Too short
      'aA1!' // Too short
    ];

    const strongPassword = 'MyStr0ng!P@ssw0rd2024';

    for (const password of weakPasswords) {
      try {
        const response = await axios.post(`${this.baseUrl}/api/auth/register`, {
          email: `test${Date.now()}@example.com`,
          password: password
        }, {
          validateStatus: () => true
        });

        if (response.status === 400) {
          this.log('PASS', 'Password Validation', `Weak password rejected: ${password}`);
        } else {
          this.log('FAIL', 'Password Validation', `Weak password accepted: ${password}`);
        }
      } catch (error) {
        this.log('WARN', 'Password Validation', `Test error for password: ${password}`);
      }
    }

    // Test strong password
    try {
      const response = await axios.post(`${this.baseUrl}/api/auth/register`, {
        email: `strongtest${Date.now()}@example.com`,
        password: strongPassword
      }, {
        validateStatus: () => true
      });

      if (response.status === 201 || response.status === 400) { // 400 might be due to existing email
        this.log('PASS', 'Password Validation', 'Strong password accepted');
      } else {
        this.log('FAIL', 'Password Validation', 'Strong password rejected');
      }
    } catch (error) {
      this.log('WARN', 'Password Validation', 'Strong password test error');
    }
  }

  async testAccountLockout() {
    console.log('\n🔒 Testing Account Lockout...');

    try {
      // Check if lockout columns exist
      const columns = await query(`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name IN ('failed_login_attempts', 'locked_until')
      `);

      if (columns.rows.length === 2) {
        this.log('PASS', 'Account Lockout', 'Database columns for account lockout exist');
      } else {
        this.log('FAIL', 'Account Lockout', 'Database columns for account lockout missing');
        return;
      }

      // Test failed login attempts
      const testEmail = `lockouttest${Date.now()}@example.com`;
      
      // First register a user
      await axios.post(`${this.baseUrl}/api/auth/register`, {
        email: testEmail,
        password: 'TestPassword123!'
      }, { validateStatus: () => true });

      // Attempt multiple failed logins
      for (let i = 0; i < 6; i++) {
        const response = await axios.post(`${this.baseUrl}/api/auth/login`, {
          email: testEmail,
          password: 'wrongpassword'
        }, { validateStatus: () => true });

        if (i < 5 && response.status === 401) {
          // Expected failed login
          continue;
        } else if (i === 5 && response.status === 423) {
          this.log('PASS', 'Account Lockout', 'Account locked after 5 failed attempts');
          return;
        }
      }

      this.log('FAIL', 'Account Lockout', 'Account lockout not working properly');

    } catch (error) {
      this.log('FAIL', 'Account Lockout', 'Account lockout test failed', error.message);
    }
  }

  async testRateLimiting() {
    console.log('\n⏱️  Testing Rate Limiting...');

    try {
      const requests = [];
      const startTime = Date.now();

      // Send multiple requests quickly
      for (let i = 0; i < 20; i++) {
        requests.push(
          axios.get(`${this.baseUrl}/health`, {
            validateStatus: () => true
          })
        );
      }

      const responses = await Promise.all(requests);
      const rateLimitedResponses = responses.filter(r => r.status === 429);

      if (rateLimitedResponses.length > 0) {
        this.log('PASS', 'Rate Limiting', `Rate limiting active - ${rateLimitedResponses.length} requests blocked`);
      } else {
        this.log('WARN', 'Rate Limiting', 'No rate limiting detected (may be disabled in test environment)');
      }

    } catch (error) {
      this.log('FAIL', 'Rate Limiting', 'Rate limiting test failed', error.message);
    }
  }

  async runAllTests() {
    console.log('🔍 Starting Comprehensive Security Test Suite...\n');

    await this.testSecurityHeaders();
    await this.testInputSanitization();
    await this.testPasswordValidation();
    await this.testAccountLockout();
    await this.testRateLimiting();

    this.printSummary();
  }

  printSummary() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 SECURITY TEST SUMMARY');
    console.log('='.repeat(60));
    console.log(`✅ Passed: ${this.testResults.passed}`);
    console.log(`❌ Failed: ${this.testResults.failed}`);
    console.log(`⚠️  Warnings: ${this.testResults.warnings}`);
    console.log(`📝 Total Tests: ${this.testResults.tests.length}`);
    
    const successRate = ((this.testResults.passed / this.testResults.tests.length) * 100).toFixed(1);
    console.log(`📈 Success Rate: ${successRate}%`);

    if (this.testResults.failed === 0) {
      console.log('\n🎉 All critical security tests passed!');
    } else {
      console.log('\n⚠️  Some security tests failed. Please review and fix the issues.');
    }

    console.log('\n📋 Recommendations:');
    if (this.testResults.failed > 0) {
      console.log('• Fix all failed security tests before deploying to production');
    }
    console.log('• Run this test suite regularly as part of your CI/CD pipeline');
    console.log('• Monitor security logs for suspicious activity');
    console.log('• Keep security dependencies updated');
  }
}

// Run tests if script is executed directly
if (require.main === module) {
  const tester = new SecurityTester();
  tester.runAllTests()
    .then(() => {
      process.exit(tester.testResults.failed > 0 ? 1 : 0);
    })
    .catch((error) => {
      console.error('Test suite failed:', error);
      process.exit(1);
    });
}

module.exports = { SecurityTester };
