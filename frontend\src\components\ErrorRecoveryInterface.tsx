import React, { useState, useEffect } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import errorRecoveryService, { ErrorContext, RecoveryOption } from '../services/errorRecoveryService';
import PinAuthComponent from './PinAuthComponent';
import PatternAuthComponent from './PatternAuthComponent';
import mobileDetection from '../utils/mobileDetection';

interface ErrorRecoveryInterfaceProps {
  error: any;
  context?: Partial<ErrorContext>;
  onRecovery?: (result: any) => void;
  onCancel?: () => void;
  className?: string;
}

const ErrorRecoveryInterface: React.FC<ErrorRecoveryInterfaceProps> = ({
  error,
  context = {},
  onRecovery,
  onCancel,
  className = ''
}) => {
  const { t } = useLanguage();
  const [errorContext, setErrorContext] = useState<ErrorContext | null>(null);
  const [recoveryOptions, setRecoveryOptions] = useState<RecoveryOption[]>([]);
  const [selectedOption, setSelectedOption] = useState<RecoveryOption | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [recoveryStep, setRecoveryStep] = useState<'options' | 'fallback' | 'device_recovery' | 'contact'>('options');
  const [email, setEmail] = useState(context.email || '');

  // Analyze error and get recovery options
  useEffect(() => {
    const analyzeAndSetup = async () => {
      try {
        setIsLoading(true);
        
        // Analyze the error
        const analyzedError = errorRecoveryService.analyzeError(error, context);
        setErrorContext(analyzedError);

        // Get recovery options
        const options = await errorRecoveryService.getRecoveryOptions(analyzedError);
        setRecoveryOptions(options);

      } catch (err) {
        console.error('Error analyzing error:', err);
      } finally {
        setIsLoading(false);
      }
    };

    analyzeAndSetup();
  }, [error, context]);

  // Handle recovery option selection
  const handleOptionSelect = async (option: RecoveryOption) => {
    try {
      setIsLoading(true);
      setSelectedOption(option);

      if (option.type === 'retry') {
        // Trigger retry in parent component
        onRecovery?.({ type: 'retry' });
        return;
      }

      if (option.type === 'fallback') {
        setRecoveryStep('fallback');
        return;
      }

      if (option.type === 'device_recovery') {
        setRecoveryStep('device_recovery');
        return;
      }

      if (option.type === 'contact_support') {
        setRecoveryStep('contact');
        return;
      }

      // Execute the option's action
      const result = await option.action();
      onRecovery?.(result);

    } catch (err: any) {
      console.error('Error executing recovery option:', err);
      // Handle error in recovery option
    } finally {
      setIsLoading(false);
    }
  };

  // Handle fallback authentication success
  const handleFallbackSuccess = (result: any) => {
    mobileDetection.triggerHapticFeedback('success');
    onRecovery?.(result);
  };

  // Handle fallback authentication error
  const handleFallbackError = (errorMessage: string) => {
    mobileDetection.triggerHapticFeedback('error');
    // Could show error or go back to options
    setRecoveryStep('options');
  };

  // Get error icon based on type
  const getErrorIcon = (errorType: string) => {
    switch (errorType) {
      case 'biometric_failure': return '🔐';
      case 'device_loss': return '📱';
      case 'account_locked': return '🔒';
      case 'network_error': return '🌐';
      case 'server_error': return '⚠️';
      default: return '❌';
    }
  };

  // Get error title in Arabic
  const getErrorTitle = (errorType: string) => {
    switch (errorType) {
      case 'biometric_failure': return 'فشل في المصادقة البيومترية';
      case 'device_loss': return 'فقدان الجهاز';
      case 'account_locked': return 'الحساب مؤقت';
      case 'network_error': return 'خطأ في الشبكة';
      case 'server_error': return 'خطأ في الخادم';
      default: return 'خطأ غير معروف';
    }
  };

  if (!errorContext) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className={`error-recovery-interface ${className}`} style={{ direction: 'rtl' }}>
      {recoveryStep === 'options' && (
        <div className="bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto">
          <div className="text-center mb-6">
            <div className="text-6xl mb-4">{getErrorIcon(errorContext.errorType)}</div>
            <h3 className="text-xl font-bold text-red-700 font-['Almarai'] mb-2">
              {getErrorTitle(errorContext.errorType)}
            </h3>
            <p className="text-gray-600 font-['Almarai']">
              {errorContext.errorMessage}
            </p>
          </div>

          {/* Suggested Actions */}
          {errorContext.suggestedActions.length > 0 && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <h4 className="font-semibold text-blue-800 mb-2 font-['Almarai']">نصائح للحل:</h4>
              <ul className="text-blue-700 text-sm space-y-1 font-['Almarai']">
                {errorContext.suggestedActions.map((action, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span>{action}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Recovery Options */}
          <div className="space-y-3 mb-6">
            <h4 className="font-semibold text-gray-800 font-['Almarai']">خيارات الاسترداد:</h4>
            {recoveryOptions.map((option, index) => (
              <button
                key={index}
                onClick={() => handleOptionSelect(option)}
                disabled={!option.available || isLoading}
                className={`
                  w-full flex items-center gap-3 p-4 rounded-lg border transition-colors
                  ${option.priority === 1 ? 'border-blue-500 bg-blue-50 hover:bg-blue-100' :
                    option.priority === 2 ? 'border-yellow-500 bg-yellow-50 hover:bg-yellow-100' :
                    'border-gray-300 bg-gray-50 hover:bg-gray-100'}
                  disabled:opacity-50 disabled:cursor-not-allowed
                  font-['Almarai']
                `}
              >
                <span className="text-2xl">{option.icon}</span>
                <div className="flex-1 text-right">
                  <div className="font-medium">{option.titleArabic}</div>
                  <div className="text-sm text-gray-600">{option.descriptionArabic}</div>
                </div>
              </button>
            ))}
          </div>

          {/* Cancel Button */}
          <div className="text-center">
            <button
              onClick={onCancel}
              className="text-gray-500 hover:text-gray-700 font-['Almarai']"
            >
              إلغاء
            </button>
          </div>
        </div>
      )}

      {recoveryStep === 'fallback' && selectedOption && (
        <div>
          {selectedOption.titleArabic.includes('رقم سري') && (
            <PinAuthComponent
              mode="authenticate"
              email={email}
              onSuccess={handleFallbackSuccess}
              onError={handleFallbackError}
              onCancel={() => setRecoveryStep('options')}
            />
          )}
          
          {selectedOption.titleArabic.includes('نمط') && (
            <PatternAuthComponent
              mode="authenticate"
              email={email}
              onSuccess={handleFallbackSuccess}
              onError={handleFallbackError}
              onCancel={() => setRecoveryStep('options')}
            />
          )}

          {selectedOption.titleArabic.includes('كلمة المرور') && (
            <div className="bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto" style={{ direction: 'rtl' }}>
              <div className="text-center mb-6">
                <div className="text-4xl mb-4">🔑</div>
                <h3 className="text-xl font-bold font-['Almarai']">تسجيل الدخول بكلمة المرور</h3>
                <p className="text-gray-600 mt-2 font-['Almarai']">
                  سيتم إعادة توجيهك إلى صفحة تسجيل الدخول العادية
                </p>
              </div>
              <div className="flex gap-3 justify-center">
                <button
                  onClick={() => {
                    window.location.href = '/login';
                  }}
                  className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-['Almarai']"
                >
                  المتابعة
                </button>
                <button
                  onClick={() => setRecoveryStep('options')}
                  className="bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 transition-colors font-['Almarai']"
                >
                  رجوع
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {recoveryStep === 'device_recovery' && (
        <div className="bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto" style={{ direction: 'rtl' }}>
          <div className="text-center mb-6">
            <div className="text-4xl mb-4">📱</div>
            <h3 className="text-xl font-bold font-['Almarai']">استرداد الجهاز</h3>
            <p className="text-gray-600 mt-2 font-['Almarai']">
              سيتم إرسال رمز استرداد إلى بريدك الإلكتروني لتسجيل جهاز جديد
            </p>
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2 font-['Almarai']">
              البريد الإلكتروني
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 font-['Almarai']"
              placeholder="أدخل بريدك الإلكتروني"
            />
          </div>

          <div className="flex gap-3 justify-center">
            <button
              onClick={async () => {
                try {
                  setIsLoading(true);
                  await errorRecoveryService.handleDeviceLossRecovery(email);
                  // Handle success - could show next step
                  alert('تم إرسال رمز الاسترداد إلى بريدك الإلكتروني');
                } catch (error: any) {
                  alert(error.message);
                } finally {
                  setIsLoading(false);
                }
              }}
              disabled={!email || isLoading}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-['Almarai'] disabled:opacity-50"
            >
              {isLoading ? 'جاري الإرسال...' : 'إرسال رمز الاسترداد'}
            </button>
            <button
              onClick={() => setRecoveryStep('options')}
              className="bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 transition-colors font-['Almarai']"
            >
              رجوع
            </button>
          </div>
        </div>
      )}

      {recoveryStep === 'contact' && (
        <div className="bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto" style={{ direction: 'rtl' }}>
          <div className="text-center mb-6">
            <div className="text-4xl mb-4">🆘</div>
            <h3 className="text-xl font-bold font-['Almarai']">تواصل مع الدعم الفني</h3>
            <p className="text-gray-600 mt-2 font-['Almarai']">
              فريق الدعم الفني جاهز لمساعدتك في حل المشكلة
            </p>
          </div>

          <div className="space-y-4 mb-6">
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-semibold font-['Almarai'] mb-2">معلومات المشكلة:</h4>
              <div className="text-sm text-gray-600 font-['Almarai'] space-y-1">
                <div>نوع الخطأ: {getErrorTitle(errorContext.errorType)}</div>
                <div>الرسالة: {errorContext.errorMessage}</div>
                <div>الوقت: {new Date(errorContext.timestamp).toLocaleString('ar-SA')}</div>
                <div>عدد المحاولات: {errorContext.retryCount}</div>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-semibold text-blue-800 font-['Almarai'] mb-2">طرق التواصل:</h4>
              <div className="text-sm text-blue-700 font-['Almarai'] space-y-2">
                <div>📧 البريد الإلكتروني: <EMAIL></div>
                <div>📞 الهاتف: 920000000</div>
                <div>💬 الدردشة المباشرة: متاحة 24/7</div>
              </div>
            </div>
          </div>

          <div className="flex gap-3 justify-center">
            <button
              onClick={() => {
                // Open support chat or email
                window.open('mailto:<EMAIL>?subject=مشكلة في المصادقة البيومترية');
              }}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-['Almarai']"
            >
              إرسال بريد إلكتروني
            </button>
            <button
              onClick={() => setRecoveryStep('options')}
              className="bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 transition-colors font-['Almarai']"
            >
              رجوع
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ErrorRecoveryInterface;
