# E-Signature System Documentation

This directory contains all documentation for the e-signature system.

## Documentation Structure

### Core Documentation
- [Main README](../README.md) - Project overview and quick start
- [Setup Guide](setup-guide.md) - Comprehensive setup instructions
- [API Documentation](api-documentation.md) - Complete API reference

### Feature Documentation
- [WhatsApp Notifications](features/whatsapp-notifications.md) - WhatsApp integration setup and usage
- [Arabic Support](features/arabic-support.md) - RTL and Arabic language features
- [Document Workflow](features/document-workflow.md) - Document upload, signing, and management
- [Security Features](features/security.md) - Authentication, authorization, and audit logging
- [User Management](features/user-management.md) - User roles, permissions, and verification

### Implementation Guides
- [Large File Upload](implementation/large-file-upload.md) - File upload system implementation
- [PDF Processing](implementation/pdf-processing.md) - PDF signing and processing
- [Database Schema](implementation/database-schema.md) - Database structure and migrations
- [Frontend Architecture](implementation/frontend-architecture.md) - React app structure and components

### Deployment & Maintenance
- [Deployment Guide](deployment/deployment-guide.md) - Production deployment instructions
- [Troubleshooting](deployment/troubleshooting.md) - Common issues and solutions
- [Maintenance](deployment/maintenance.md) - System maintenance and updates

## Quick Links

- **Getting Started**: See [Main README](../README.md)
- **Setup Issues**: Check [Troubleshooting](deployment/troubleshooting.md)
- **API Reference**: See [API Documentation](api-documentation.md)
- **Feature Requests**: Create an issue in the repository
