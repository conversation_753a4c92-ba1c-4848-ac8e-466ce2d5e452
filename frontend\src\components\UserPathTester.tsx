import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../services/AuthContext';
import { authAPI, documentAPI, signatureAPI } from '../services/api';

interface TestResult {
  testName: string;
  status: 'pending' | 'running' | 'passed' | 'failed';
  message: string;
  duration?: number;
}

interface UserPathTesterProps {
  className?: string;
}

const UserPathTester: React.FC<UserPathTesterProps> = ({ className = '' }) => {
  const { user, isAdmin } = useAuth();
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const updateTestResult = (testName: string, status: TestResult['status'], message: string, duration?: number) => {
    setTestResults(prev => prev.map(test => 
      test.testName === testName 
        ? { ...test, status, message, duration }
        : test
    ));
  };

  const runTest = async (testName: string, testFunction: () => Promise<void>) => {
    const startTime = Date.now();
    updateTestResult(testName, 'running', 'جاري التشغيل...');
    
    try {
      await testFunction();
      const duration = Date.now() - startTime;
      updateTestResult(testName, 'passed', 'نجح الاختبار', duration);
    } catch (error: any) {
      const duration = Date.now() - startTime;
      updateTestResult(testName, 'failed', error.message || 'فشل الاختبار', duration);
    }
  };

  const initializeTests = useCallback(() => {
    const commonTests = [
      { testName: 'اختبار الاتصال بالخادم', status: 'pending' as const, message: 'في الانتظار' },
      { testName: 'اختبار المصادقة', status: 'pending' as const, message: 'في الانتظار' },
      { testName: 'اختبار الملف الشخصي', status: 'pending' as const, message: 'في الانتظار' },
      { testName: 'اختبار الصلاحيات', status: 'pending' as const, message: 'في الانتظار' }
    ];

    const adminTests = [
      { testName: 'اختبار إدارة المستخدمين', status: 'pending' as const, message: 'في الانتظار' },
      { testName: 'اختبار رفع التوقيع', status: 'pending' as const, message: 'في الانتظار' },
      { testName: 'اختبار توقيع المستندات', status: 'pending' as const, message: 'في الانتظار' },
      { testName: 'اختبار عرض السجلات', status: 'pending' as const, message: 'في الانتظار' }
    ];

    const userTests = [
      { testName: 'اختبار رفع المستندات', status: 'pending' as const, message: 'في الانتظار' },
      { testName: 'اختبار عرض التاريخ', status: 'pending' as const, message: 'في الانتظار' },
      { testName: 'اختبار الإعدادات', status: 'pending' as const, message: 'في الانتظار' }
    ];

    setTestResults([
      ...commonTests,
      ...(isAdmin() ? adminTests : userTests)
    ]);
  }, [isAdmin]);

  useEffect(() => {
    initializeTests();
  }, [user, initializeTests]);

  const runAllTests = async () => {
    setIsRunning(true);
    
    // Common tests
    await runTest('اختبار الاتصال بالخادم', async () => {
      const response = await fetch('/health');
      if (!response.ok) throw new Error('فشل الاتصال بالخادم');
    });

    await runTest('اختبار المصادقة', async () => {
      if (!user) throw new Error('المستخدم غير مسجل الدخول');
      const token = localStorage.getItem('token');
      if (!token) throw new Error('رمز المصادقة غير موجود');
    });

    await runTest('اختبار الملف الشخصي', async () => {
      const response = await authAPI.getProfile();
      if (!response.data) throw new Error('فشل في جلب الملف الشخصي');
    });

    await runTest('اختبار الصلاحيات', async () => {
      const hasBasicPermission = user?.role === 'admin' || user?.role === 'user';
      if (!hasBasicPermission) throw new Error('صلاحيات غير صحيحة');
    });

    // Admin-specific tests
    if (isAdmin()) {
      await runTest('اختبار إدارة المستخدمين', async () => {
        const response = await authAPI.getAllUsers();
        if (!response.data) throw new Error('فشل في جلب قائمة المستخدمين');
      });

      await runTest('اختبار رفع التوقيع', async () => {
        // Check if the signature upload API exists
        if (!signatureAPI.upload) throw new Error('API رفع التوقيع غير متوفر');
        // Note: In a real test, we would create and upload a test file
      });

      await runTest('اختبار توقيع المستندات', async () => {
        // Check if document signing API exists
        if (!documentAPI.sign) throw new Error('API توقيع المستندات غير متوفر');
      });

      await runTest('اختبار عرض السجلات', async () => {
        const response = await documentAPI.getAll();
        if (!response.data) throw new Error('فشل في جلب السجلات');
      });
    } else {
      // User-specific tests
      await runTest('اختبار رفع المستندات', async () => {
        if (!documentAPI.uploadForReview) throw new Error('API رفع المستندات غير متوفر');
      });

      await runTest('اختبار عرض التاريخ', async () => {
        const response = await documentAPI.getAll();
        if (!response.data) throw new Error('فشل في جلب التاريخ');
      });

      await runTest('اختبار الإعدادات', async () => {
        const response = await authAPI.getProfile();
        if (!response.data) throw new Error('فشل في الوصول للإعدادات');
      });
    }

    setIsRunning(false);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return <div className="w-4 h-4 bg-gray-300 rounded-full"></div>;
      case 'running':
        return <div className="w-4 h-4 bg-blue-500 rounded-full animate-pulse"></div>;
      case 'passed':
        return <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
          <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        </div>;
      case 'failed':
        return <div className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
          <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </div>;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pending': return 'text-gray-600';
      case 'running': return 'text-blue-600';
      case 'passed': return 'text-green-600';
      case 'failed': return 'text-red-600';
    }
  };

  const passedTests = testResults.filter(test => test.status === 'passed').length;
  const totalTests = testResults.length;

  return (
    <div className={`p-6 bg-white rounded-lg shadow-lg ${className}`}>
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-xl font-bold">اختبار مسارات المستخدم</h3>
        <button
          onClick={runAllTests}
          disabled={isRunning}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isRunning ? 'جاري التشغيل...' : 'تشغيل جميع الاختبارات'}
        </button>
      </div>

      <div className="mb-4">
        <div className="flex justify-between text-sm text-gray-600 mb-2">
          <span>التقدم: {passedTests}/{totalTests}</span>
          <span>نوع المستخدم: {isAdmin() ? 'مدير' : 'مستخدم عادي'}</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${totalTests > 0 ? (passedTests / totalTests) * 100 : 0}%` }}
          ></div>
        </div>
      </div>

      <div className="space-y-3">
        {testResults.map((test, index) => (
          <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
            <div className="flex items-center space-x-3 space-x-reverse">
              {getStatusIcon(test.status)}
              <span className="font-medium">{test.testName}</span>
            </div>
            <div className="text-left">
              <span className={`text-sm ${getStatusColor(test.status)}`}>
                {test.message}
              </span>
              {test.duration && (
                <span className="text-xs text-gray-500 block">
                  {test.duration}ms
                </span>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default UserPathTester;
