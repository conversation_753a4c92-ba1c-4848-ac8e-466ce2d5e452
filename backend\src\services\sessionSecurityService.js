const { query } = require('../models/database');
const crypto = require('crypto');

/**
 * Session Security Service
 * Manages secure sessions with timeout, concurrent session limits, and monitoring
 */

class SessionSecurityService {
  constructor() {
    this.maxConcurrentSessions = 3; // Maximum concurrent sessions per user
    this.sessionTimeout = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
    this.inactivityTimeout = 2 * 60 * 60 * 1000; // 2 hours in milliseconds
    this.cleanupInterval = 15 * 60 * 1000; // 15 minutes
    
    // Start cleanup process
    this.startCleanupProcess();
  }

  /**
   * Create a new secure session
   */
  async createSession(userId, deviceInfo = {}) {
    try {
      const sessionId = crypto.randomBytes(32).toString('hex');
      const now = new Date();
      const expiresAt = new Date(now.getTime() + this.sessionTimeout);
      const lastActivity = now;

      // Check concurrent session limit
      await this.enforceConcurrentSessionLimit(userId);

      // Create session record
      await query(
        `INSERT INTO user_sessions 
         (session_id, user_id, created_at, expires_at, last_activity, device_info, is_active)
         VALUES ($1, $2, $3, $4, $5, $6, $7)`,
        [sessionId, userId, now, expiresAt, lastActivity, JSON.stringify(deviceInfo), true]
      );

      // Log session creation
      await query(
        'INSERT INTO logs (user_id, action, details) VALUES ($1, $2, $3)',
        [userId, 'SESSION_CREATED', {
          sessionId: sessionId.substring(0, 8) + '...',
          deviceInfo,
          expiresAt,
          ip: deviceInfo.ip,
          userAgent: deviceInfo.userAgent
        }]
      );

      return {
        sessionId,
        expiresAt,
        maxInactivity: this.inactivityTimeout
      };

    } catch (error) {
      console.error('Error creating session:', error);
      throw new Error('فشل في إنشاء الجلسة');
    }
  }

  /**
   * Validate and refresh session
   */
  async validateSession(sessionId, userId) {
    try {
      const result = await query(
        `SELECT session_id, user_id, created_at, expires_at, last_activity, 
                device_info, is_active, login_count
         FROM user_sessions 
         WHERE session_id = $1 AND user_id = $2 AND is_active = true`,
        [sessionId, userId]
      );

      if (result.rows.length === 0) {
        return {
          isValid: false,
          reason: 'SESSION_NOT_FOUND',
          message: 'الجلسة غير موجودة أو منتهية الصلاحية'
        };
      }

      const session = result.rows[0];
      const now = new Date();
      const expiresAt = new Date(session.expires_at);
      const lastActivity = new Date(session.last_activity);

      // Check if session has expired
      if (now > expiresAt) {
        await this.invalidateSession(sessionId, 'EXPIRED');
        return {
          isValid: false,
          reason: 'SESSION_EXPIRED',
          message: 'انتهت صلاحية الجلسة'
        };
      }

      // Check inactivity timeout
      const inactivityDuration = now - lastActivity;
      if (inactivityDuration > this.inactivityTimeout) {
        await this.invalidateSession(sessionId, 'INACTIVITY_TIMEOUT');
        return {
          isValid: false,
          reason: 'INACTIVITY_TIMEOUT',
          message: 'انتهت صلاحية الجلسة بسبب عدم النشاط'
        };
      }

      // Update last activity
      await this.updateSessionActivity(sessionId);

      // Calculate time remaining
      const timeUntilExpiry = expiresAt - now;
      const timeUntilInactivityTimeout = this.inactivityTimeout - inactivityDuration;

      return {
        isValid: true,
        session: {
          sessionId: session.session_id,
          createdAt: session.created_at,
          expiresAt: session.expires_at,
          lastActivity: now,
          deviceInfo: session.device_info,
          loginCount: session.login_count
        },
        timeRemaining: {
          untilExpiry: timeUntilExpiry,
          untilInactivityTimeout: timeUntilInactivityTimeout
        }
      };

    } catch (error) {
      console.error('Error validating session:', error);
      return {
        isValid: false,
        reason: 'VALIDATION_ERROR',
        message: 'خطأ في التحقق من الجلسة'
      };
    }
  }

  /**
   * Update session activity timestamp
   */
  async updateSessionActivity(sessionId) {
    try {
      await query(
        'UPDATE user_sessions SET last_activity = CURRENT_TIMESTAMP WHERE session_id = $1',
        [sessionId]
      );
    } catch (error) {
      console.error('Error updating session activity:', error);
    }
  }

  /**
   * Invalidate a session
   */
  async invalidateSession(sessionId, reason = 'USER_LOGOUT') {
    try {
      const result = await query(
        `UPDATE user_sessions 
         SET is_active = false, invalidated_at = CURRENT_TIMESTAMP, invalidation_reason = $2
         WHERE session_id = $1
         RETURNING user_id`,
        [sessionId, reason]
      );

      if (result.rows.length > 0) {
        const userId = result.rows[0].user_id;
        
        // Log session invalidation
        await query(
          'INSERT INTO logs (user_id, action, details) VALUES ($1, $2, $3)',
          [userId, 'SESSION_INVALIDATED', {
            sessionId: sessionId.substring(0, 8) + '...',
            reason,
            timestamp: new Date().toISOString()
          }]
        );
      }

      return true;
    } catch (error) {
      console.error('Error invalidating session:', error);
      return false;
    }
  }

  /**
   * Enforce concurrent session limit
   */
  async enforceConcurrentSessionLimit(userId) {
    try {
      // Get active sessions count
      const countResult = await query(
        'SELECT COUNT(*) as count FROM user_sessions WHERE user_id = $1 AND is_active = true',
        [userId]
      );

      const activeSessionsCount = parseInt(countResult.rows[0].count);

      if (activeSessionsCount >= this.maxConcurrentSessions) {
        // Invalidate oldest sessions
        const sessionsToInvalidate = activeSessionsCount - this.maxConcurrentSessions + 1;
        
        await query(
          `UPDATE user_sessions 
           SET is_active = false, invalidated_at = CURRENT_TIMESTAMP, invalidation_reason = 'CONCURRENT_LIMIT'
           WHERE session_id IN (
             SELECT session_id FROM user_sessions 
             WHERE user_id = $1 AND is_active = true 
             ORDER BY last_activity ASC 
             LIMIT $2
           )`,
          [userId, sessionsToInvalidate]
        );

        // Log concurrent session limit enforcement
        await query(
          'INSERT INTO logs (user_id, action, details) VALUES ($1, $2, $3)',
          [userId, 'CONCURRENT_SESSION_LIMIT_ENFORCED', {
            invalidatedSessions: sessionsToInvalidate,
            maxAllowed: this.maxConcurrentSessions
          }]
        );
      }
    } catch (error) {
      console.error('Error enforcing concurrent session limit:', error);
    }
  }

  /**
   * Get user's active sessions
   */
  async getUserSessions(userId) {
    try {
      const result = await query(
        `SELECT session_id, created_at, last_activity, device_info, login_count
         FROM user_sessions 
         WHERE user_id = $1 AND is_active = true 
         ORDER BY last_activity DESC`,
        [userId]
      );

      return result.rows.map(session => ({
        sessionId: session.session_id.substring(0, 8) + '...',
        createdAt: session.created_at,
        lastActivity: session.last_activity,
        deviceInfo: session.device_info,
        loginCount: session.login_count,
        isCurrentSession: false // Will be set by caller
      }));

    } catch (error) {
      console.error('Error getting user sessions:', error);
      throw new Error('فشل في الحصول على جلسات المستخدم');
    }
  }

  /**
   * Invalidate all user sessions except current
   */
  async invalidateAllUserSessions(userId, exceptSessionId = null) {
    try {
      let query_text = `UPDATE user_sessions 
                       SET is_active = false, invalidated_at = CURRENT_TIMESTAMP, invalidation_reason = 'USER_LOGOUT_ALL'
                       WHERE user_id = $1 AND is_active = true`;
      let params = [userId];

      if (exceptSessionId) {
        query_text += ' AND session_id != $2';
        params.push(exceptSessionId);
      }

      const result = await query(query_text, params);

      // Log bulk session invalidation
      await query(
        'INSERT INTO logs (user_id, action, details) VALUES ($1, $2, $3)',
        [userId, 'ALL_SESSIONS_INVALIDATED', {
          invalidatedCount: result.rowCount,
          exceptSession: exceptSessionId ? exceptSessionId.substring(0, 8) + '...' : null
        }]
      );

      return result.rowCount;

    } catch (error) {
      console.error('Error invalidating all user sessions:', error);
      throw new Error('فشل في إلغاء جميع الجلسات');
    }
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions() {
    try {
      const result = await query(
        `UPDATE user_sessions 
         SET is_active = false, invalidated_at = CURRENT_TIMESTAMP, invalidation_reason = 'EXPIRED'
         WHERE is_active = true AND (
           expires_at < CURRENT_TIMESTAMP OR 
           last_activity < CURRENT_TIMESTAMP - INTERVAL '${this.inactivityTimeout / 1000} seconds'
         )`
      );

      if (result.rowCount > 0) {
        console.log(`Cleaned up ${result.rowCount} expired sessions`);
        
        // Log cleanup
        await query(
          'INSERT INTO logs (action, details) VALUES ($1, $2)',
          ['SESSION_CLEANUP', {
            cleanedSessions: result.rowCount,
            timestamp: new Date().toISOString()
          }]
        );
      }

      return result.rowCount;

    } catch (error) {
      console.error('Error cleaning up expired sessions:', error);
      return 0;
    }
  }

  /**
   * Start automatic cleanup process
   */
  startCleanupProcess() {
    setInterval(async () => {
      await this.cleanupExpiredSessions();
    }, this.cleanupInterval);

    console.log('Session cleanup process started');
  }

  /**
   * Get session security statistics
   */
  async getSecurityStats() {
    try {
      const stats = await query(`
        SELECT 
          COUNT(*) as total_sessions,
          COUNT(*) FILTER (WHERE is_active = true) as active_sessions,
          COUNT(*) FILTER (WHERE invalidation_reason = 'EXPIRED') as expired_sessions,
          COUNT(*) FILTER (WHERE invalidation_reason = 'INACTIVITY_TIMEOUT') as timeout_sessions,
          COUNT(*) FILTER (WHERE invalidation_reason = 'CONCURRENT_LIMIT') as concurrent_limit_sessions,
          COUNT(DISTINCT user_id) as unique_users
        FROM user_sessions
        WHERE created_at > CURRENT_TIMESTAMP - INTERVAL '24 hours'
      `);

      return stats.rows[0];

    } catch (error) {
      console.error('Error getting security stats:', error);
      throw new Error('فشل في الحصول على إحصائيات الأمان');
    }
  }
}

module.exports = new SessionSecurityService();
