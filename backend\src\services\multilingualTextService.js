// Arabic-only text templates
const TEXT_TEMPLATES = {
  SERIAL_NUMBER: {
    text: 'الرقم التسلسلي للوثيقة'
  },
  SIGNED_DATE: {
    text: 'تم التوقيع'
  },
  SIGNATURE_VERIFICATION: {
    text: 'موقع رقمياً'
  },
  DOCUMENT_INTEGRITY: {
    text: 'تم التحقق من سلامة الوثيقة'
  },
  DIGITALLY_AUTHORIZED: {
    text: 'DIGITALLY AUTHORIZED'
  },
  USER_ID: {
    text: 'معرف المستخدم'
  },
  DOCUMENT_SERIAL: {
    text: 'الرقم التسلسلي'
  }
};

// Arabic numerals conversion
const ARABIC_NUMERALS = {
  '0': '٠', '1': '١', '2': '٢', '3': '٣', '4': '٤',
  '5': '٥', '6': '٦', '7': '٧', '8': '٨', '9': '٩'
};

// Convert Western numerals to Arabic numerals
const convertToArabicNumerals = (text) => {
  return text.replace(/[0-9]/g, (digit) => ARABIC_NUMERALS[digit] || digit);
};

// Convert Arabic numerals to Western numerals
const convertToWesternNumerals = (text) => {
  const reverseMap = Object.fromEntries(
    Object.entries(ARABIC_NUMERALS).map(([western, arabic]) => [arabic, western])
  );
  return text.replace(/[٠-٩]/g, (digit) => reverseMap[digit] || digit);
};

// Format date in English
const formatEnglishDate = (date) => {
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

// Format time in English
const formatEnglishTime = (date) => {
  return date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  });
};

// Generate serial number text in specified language
const generateSerialNumberText = (serialNumber, language = 'ar') => {
  if (language === 'en') {
    return `Document Serial: ${serialNumber}`;
  } else if (language === 'mixed') {
    return `Document Serial | ${TEXT_TEMPLATES.SERIAL_NUMBER.text}: ${convertToArabicNumerals(serialNumber)}`;
  }
  return `${TEXT_TEMPLATES.SERIAL_NUMBER.text}: ${convertToArabicNumerals(serialNumber)}`;
};

// Generate timestamp text in specified language
const generateTimestampText = (date = new Date(), language = 'ar') => {
  const englishFormattedDate = formatEnglishDate(date);
  const englishFormattedTime = formatEnglishTime(date);

  if (language === 'en') {
    return `Signed: ${englishFormattedDate} ${englishFormattedTime}`;
  } else if (language === 'mixed') {
    return `Signed | ${TEXT_TEMPLATES.SIGNED_DATE.text}: ${englishFormattedDate} ${englishFormattedTime}`;
  }
  return `${TEXT_TEMPLATES.SIGNED_DATE.text}: ${englishFormattedDate} ${englishFormattedTime}`;
};

// Generate Arabic verification text
const generateVerificationText = () => {
  return TEXT_TEMPLATES.SIGNATURE_VERIFICATION.text;
};

// Generate Arabic document integrity text
const generateIntegrityText = () => {
  return TEXT_TEMPLATES.DOCUMENT_INTEGRITY.text;
};

// Generate "DIGITALLY AUTHORIZED" text (in English as requested)
const generateDigitallyAuthorizedText = () => {
  return TEXT_TEMPLATES.DIGITALLY_AUTHORIZED.text;
};

// Generate user ID text
const generateUserIdText = (userId) => {
  return `${TEXT_TEMPLATES.USER_ID.text}: ${convertToArabicNumerals(userId)}`;
};

// Generate document serial number text (shorter version for signature block)
const generateDocumentSerialText = (serialNumber) => {
  return `${TEXT_TEMPLATES.DOCUMENT_SERIAL.text}: ${convertToArabicNumerals(serialNumber)}`;
};

// Get appropriate font size for Arabic text
const getOptimalFontSize = (text, baseSize = 8) => {
  // Arabic text needs slightly larger font size for readability
  return Math.max(baseSize, baseSize + 1);
};

// Calculate text spacing for Arabic content
const calculateTextSpacing = (texts, baseSpacing = 12, language = 'ar') => {
  const spacing = [];
  let currentY = 0;

  texts.forEach((text, index) => {
    // For Arabic text, use a slightly larger base font size
    const baseFontSize = language === 'ar' ? 10 : 8;
    const fontSize = getOptimalFontSize(text, baseFontSize);
    const lineHeight = fontSize * 1.4; // Increased line height for Arabic

    spacing.push({
      text,
      y: currentY,
      fontSize,
      lineHeight
    });

    currentY += lineHeight;
  });

  return spacing;
};

// Format complete signature block
const formatSignatureBlock = (serialNumber, date = new Date(), options = {}) => {
  const {
    includeVerification = true,
    includeIntegrity = false,
    userId = null,
    userEmail = null,
    language = 'ar',
    documentDirection = 'rtl'
  } = options;

  const texts = [];

  // Add verification text if requested
  if (includeVerification) {
    texts.push(generateVerificationText());
  }

  // Add user ID if provided (but not for the test case)
  if (userId) {
    texts.push(generateUserIdText(userId));
  }

  // Add document serial number
  texts.push(generateSerialNumberText(serialNumber, language));

  // Add timestamp
  texts.push(generateTimestampText(date, language));

  // Add integrity text if requested
  if (includeIntegrity) {
    texts.push(generateIntegrityText());
  }

  return {
    texts,
    spacing: calculateTextSpacing(texts, 12, language),
    direction: documentDirection,
    language: language
  };
};

module.exports = {
  generateSerialNumberText,
  generateTimestampText,
  generateVerificationText,
  generateIntegrityText,
  generateDigitallyAuthorizedText,
  generateUserIdText,
  generateDocumentSerialText,
  formatSignatureBlock,
  convertToArabicNumerals,
  convertToWesternNumerals,
  formatEnglishDate,
  formatEnglishTime,
  getOptimalFontSize,
  calculateTextSpacing,
  TEXT_TEMPLATES
};
