const nodemailer = require('nodemailer');
const { query } = require('../models/database');

// Initialize Email client
let emailTransporter = null;

const initializeEmailService = () => {
  if (!process.env.EMAIL_HOST || !process.env.EMAIL_USER || !process.env.EMAIL_PASS) {
    console.warn('⚠ Email credentials not configured - Email notifications disabled');
    return null;
  }

  try {
    emailTransporter = nodemailer.createTransporter({
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT || 587,
      secure: process.env.EMAIL_SECURE === 'true',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      }
    });
    console.log('✓ Email notification service initialized');
    return emailTransporter;
  } catch (error) {
    console.error('Failed to initialize email service:', error);
    return null;
  }
};

// Configuration
const NOTIFICATION_CONFIG = {
  enabled: process.env.EMAIL_NOTIFICATIONS_ENABLED !== 'false', // Default to enabled
  fromEmail: process.env.EMAIL_FROM || process.env.EMAIL_USER || '<EMAIL>',
  adminEmails: process.env.ADMIN_NOTIFICATION_EMAILS ?
    process.env.ADMIN_NOTIFICATION_EMAILS.split(',').map(email => email.trim()) : [],
  retryAttempts: parseInt(process.env.EMAIL_RETRY_ATTEMPTS) || 3,
  retryDelay: parseInt(process.env.EMAIL_RETRY_DELAY) || 5000
};

/**
 * Validate email address format
 */
const validateEmail = (email) => {
  if (!email) return false;

  // Basic email validation regex
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
};

/**
 * Format email address (just trim whitespace)
 */
const formatEmail = (email) => {
  if (!email) return null;

  const cleanEmail = email.trim().toLowerCase();

  if (!validateEmail(cleanEmail)) {
    throw new Error(`Invalid email format: ${email}`);
  }

  return cleanEmail;
};

/**
 * Send email notification with retry logic
 */
const sendEmailNotification = async (to, subject, message, retryCount = 0) => {
  if (!NOTIFICATION_CONFIG.enabled) {
    console.log('Email notifications disabled');
    return { success: false, reason: 'disabled' };
  }

  if (!emailTransporter) {
    emailTransporter = initializeEmailService();
    if (!emailTransporter) {
      return { success: false, reason: 'email_not_configured' };
    }
  }

  try {
    const formattedTo = formatEmail(to);

    const mailOptions = {
      from: NOTIFICATION_CONFIG.fromEmail,
      to: formattedTo,
      subject: subject,
      text: message,
      html: message.replace(/\n/g, '<br>')
    };

    const messageResult = await emailTransporter.sendMail(mailOptions);

    console.log(`✓ Email notification sent successfully to ${formattedTo}:`, messageResult.messageId);

    return {
      success: true,
      messageId: messageResult.messageId,
      status: 'sent',
      to: formattedTo
    };

  } catch (error) {
    console.error(`Failed to send email notification to ${to} (attempt ${retryCount + 1}):`, error);

    // Retry logic
    if (retryCount < NOTIFICATION_CONFIG.retryAttempts - 1) {
      console.log(`Retrying email notification in ${NOTIFICATION_CONFIG.retryDelay}ms...`);

      await new Promise(resolve => setTimeout(resolve, NOTIFICATION_CONFIG.retryDelay));
      return sendEmailNotification(to, subject, message, retryCount + 1);
    }

    return {
      success: false,
      error: error.message,
      code: error.code,
      retryCount: retryCount + 1
    };
  }
};

/**
 * Send notification to multiple recipients
 */
const sendBulkEmailNotification = async (recipients, subject, message) => {
  if (!recipients || recipients.length === 0) {
    return { success: false, reason: 'no_recipients' };
  }

  const results = [];

  for (const recipient of recipients) {
    try {
      const result = await sendEmailNotification(recipient, subject, message);
      results.push({
        recipient,
        ...result
      });

      // Small delay between messages to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));

    } catch (error) {
      results.push({
        recipient,
        success: false,
        error: error.message
      });
    }
  }

  const successCount = results.filter(r => r.success).length;

  return {
    success: successCount > 0,
    totalSent: successCount,
    totalFailed: results.length - successCount,
    results
  };
};

/**
 * Log notification attempt to database
 */
const logNotificationAttempt = async (userId, documentId, recipients, message, result) => {
  try {
    await query(
      `INSERT INTO notification_logs (
        user_id, document_id, notification_type, recipients, message_content,
        success, result_data, created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP)`,
      [
        userId,
        documentId,
        'email',
        JSON.stringify(recipients),
        message,
        result.success,
        JSON.stringify(result)
      ]
    );
  } catch (error) {
    console.error('Failed to log notification attempt:', error);
  }
};

/**
 * Get user's email from database
 */
const getUserEmail = async (userId) => {
  try {
    const result = await query(
      'SELECT email FROM users WHERE id = $1 AND email IS NOT NULL',
      [userId]
    );

    return result.rows.length > 0 ? result.rows[0].email : null;
  } catch (error) {
    console.error('Failed to get user email:', error);
    return null;
  }
};

/**
 * Get all configured notification recipients (user + admins)
 */
const getNotificationRecipients = async (userId) => {
  const recipients = [];

  // Add user's email if available
  const userEmail = await getUserEmail(userId);
  if (userEmail && validateEmail(userEmail)) {
    recipients.push(userEmail);
  }

  // Add admin emails from configuration
  for (const adminEmail of NOTIFICATION_CONFIG.adminEmails) {
    if (validateEmail(adminEmail)) {
      recipients.push(adminEmail);
    }
  }

  return [...new Set(recipients)]; // Remove duplicates
};

module.exports = {
  initializeEmailService,
  sendEmailNotification,
  sendBulkEmailNotification,
  logNotificationAttempt,
  getUserEmail,
  getNotificationRecipients,
  validateEmail,
  formatEmail,
  NOTIFICATION_CONFIG,
  // Legacy exports for backward compatibility
  initializeTwilio: initializeEmailService,
  sendWhatsAppMessage: (to, message) => sendEmailNotification(to, 'نظام التوقيع الإلكتروني', message),
  sendBulkWhatsAppNotification: (recipients, message) => sendBulkEmailNotification(recipients, 'نظام التوقيع الإلكتروني', message),
  getUserPhoneNumber: getUserEmail,
  validatePhoneNumber: validateEmail,
  formatWhatsAppNumber: formatEmail,
  WHATSAPP_CONFIG: NOTIFICATION_CONFIG
};
