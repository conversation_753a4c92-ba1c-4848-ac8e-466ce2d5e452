import React, { useState } from 'react';
import { Link } from 'react-router-dom';

const AdminLoginInfo: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-lg p-6 mb-6">
      <div className="flex items-start space-x-reverse space-x-4">
        <div className="flex-shrink-0">
          <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
            <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-semibold text-purple-900 font-['Almarai'] mb-2">
            تسجيل دخول المدير
          </h3>
          <p className="text-purple-700 font-['Almarai'] mb-4">
            للوصول إلى ميزات الإدارة مثل توقيع المستندات ورفع التوقيعات، يرجى تسجيل الدخول بحساب مدير.
          </p>
          
          <div className="space-y-3">
            <div className="bg-white rounded-md p-4 border border-purple-200">
              <h4 className="font-medium text-purple-900 font-['Almarai'] mb-2">
                حسابات المدير المتاحة:
              </h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center justify-between p-2 bg-purple-50 rounded">
                  <span className="font-['Almarai'] text-purple-800"><EMAIL></span>
                  <span className="text-xs text-purple-600 bg-purple-100 px-2 py-1 rounded">مدير</span>
                </div>
                <div className="flex items-center justify-between p-2 bg-purple-50 rounded">
                  <span className="font-['Almarai'] text-purple-800"><EMAIL></span>
                  <span className="text-xs text-purple-600 bg-purple-100 px-2 py-1 rounded">مدير</span>
                </div>
              </div>
            </div>

            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-purple-600 hover:text-purple-800 text-sm font-['Almarai'] flex items-center"
            >
              {isExpanded ? 'إخفاء التفاصيل' : 'عرض المزيد من التفاصيل'}
              <svg className={`w-4 h-4 mr-1 transform transition-transform ${isExpanded ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>

            {isExpanded && (
              <div className="bg-white rounded-md p-4 border border-purple-200 space-y-3">
                <div>
                  <h5 className="font-medium text-purple-900 font-['Almarai'] mb-2">
                    صلاحيات المدير:
                  </h5>
                  <ul className="text-sm text-purple-700 font-['Almarai'] space-y-1">
                    <li className="flex items-center">
                      <svg className="w-4 h-4 text-green-500 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      رفع وإدارة التوقيعات
                    </li>
                    <li className="flex items-center">
                      <svg className="w-4 h-4 text-green-500 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      توقيع المستندات
                    </li>
                    <li className="flex items-center">
                      <svg className="w-4 h-4 text-green-500 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      عرض جميع المستندات
                    </li>
                    <li className="flex items-center">
                      <svg className="w-4 h-4 text-green-500 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      إدارة المستخدمين
                    </li>
                    <li className="flex items-center">
                      <svg className="w-4 h-4 text-green-500 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      الوصول إلى جميع الميزات الإدارية
                    </li>
                  </ul>
                </div>
                
                <div className="pt-3 border-t border-purple-200">
                  <p className="text-xs text-purple-600 font-['Almarai']">
                    💡 نصيحة: إذا كنت بحاجة إلى صلاحيات إدارية، يرجى التواصل مع مدير النظام لترقية حسابك.
                  </p>
                </div>
              </div>
            )}

            <div className="flex space-x-reverse space-x-3">
              <Link
                to="/login"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 font-['Almarai']"
              >
                تسجيل دخول المدير
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminLoginInfo;
