const { query } = require('../src/models/database');

async function checkUserStatus() {
  try {
    console.log('🔍 Checking user status and roles...\n');
    
    // Get all users
    const allUsers = await query('SELECT id, email, role, created_at FROM users ORDER BY created_at ASC');
    
    if (allUsers.rows.length === 0) {
      console.log('❌ No users found in the database');
      console.log('📝 Please register a user first through the frontend at http://localhost:3000/register');
      process.exit(0);
    }
    
    console.log(`👥 Total users: ${allUsers.rows.length}\n`);
    
    // Show all users
    console.log('📋 All Users:');
    allUsers.rows.forEach((user, index) => {
      const roleIcon = user.role === 'admin' ? '👑' : '👤';
      console.log(`${index + 1}. ${roleIcon} ${user.email} - ${user.role} (${user.created_at.toISOString().split('T')[0]})`);
    });
    
    // Count by role
    const adminUsers = allUsers.rows.filter(user => user.role === 'admin');
    const regularUsers = allUsers.rows.filter(user => user.role === 'user');
    
    console.log(`\n📊 Role Summary:`);
    console.log(`   👑 Admin users: ${adminUsers.length}`);
    console.log(`   👤 Regular users: ${regularUsers.length}`);
    
    if (adminUsers.length === 0) {
      console.log('\n⚠️  WARNING: No admin users found!');
      console.log('📝 To make a user admin, you can:');
      console.log('   1. Update the update-user-role.js script with the user email');
      console.log('   2. Run: node backend/scripts/update-user-role.js');
      console.log('   3. Or manually update in database:');
      console.log(`      UPDATE users SET role = 'admin' WHERE email = '<EMAIL>';`);
    } else {
      console.log('\n✅ Admin users found! They can access:');
      console.log('   ✅ Upload signatures');
      console.log('   ✅ Sign documents');
      console.log('   ✅ View all documents');
      console.log('   ✅ Manage users');
      console.log('   ✅ Admin features');
    }
    
    console.log('\n🔐 Permission Summary:');
    console.log('   👑 Admin permissions: upload_signatures, verify_documents, sign_documents, view_history, manage_users');
    console.log('   👤 User permissions: view_dashboard, change_password');
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Error checking user status:', error);
    process.exit(1);
  }
}

// Run the check
checkUserStatus();
