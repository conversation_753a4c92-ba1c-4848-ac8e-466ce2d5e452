const express = require('express');
const {
  getFallbackMethods,
  setupPin,
  authenticateWithPin,
  setupPattern,
  authenticateWithPattern,
  sendEmailRecovery,
  verifyEmailRecovery
} = require('../controllers/fallbackAuthController');
const { authenticateToken } = require('../middleware/auth');
const rateLimit = require('express-rate-limit');

const router = express.Router();

// Rate limiting for fallback authentication
const fallbackAuthRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each IP to 5 attempts per windowMs
  message: {
    success: false,
    message: 'تم تجاوز الحد المسموح من محاولات المصادقة. حاول مرة أخرى لاحقاً'
  },
  standardHeaders: true,
  legacyHeaders: false
});

const setupRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // Limit setup attempts
  message: {
    success: false,
    message: 'تم تجاوز الحد المسموح من محاولات الإعداد. حاول مرة أخرى لاحقاً'
  },
  standardHeaders: true,
  legacyHeaders: false
});

const recoveryRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // Limit recovery requests
  message: {
    success: false,
    message: 'تم تجاوز الحد المسموح من طلبات الاسترداد. حاول مرة أخرى لاحقاً'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Public routes (no authentication required)

// PIN authentication
router.post('/auth/pin', fallbackAuthRateLimit, authenticateWithPin);

// Pattern authentication
router.post('/auth/pattern', fallbackAuthRateLimit, authenticateWithPattern);

// Email recovery - send code
router.post('/recovery/email/send', recoveryRateLimit, sendEmailRecovery);

// Email recovery - verify code
router.post('/recovery/email/verify', fallbackAuthRateLimit, verifyEmailRecovery);

// Protected routes (authentication required)

// Get available fallback methods for user
router.get('/methods/:userId', authenticateToken, getFallbackMethods);

// Setup PIN
router.post('/setup/pin', authenticateToken, setupRateLimit, setupPin);

// Setup pattern
router.post('/setup/pattern', authenticateToken, setupRateLimit, setupPattern);

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'خدمة المصادقة البديلة تعمل بشكل طبيعي',
    timestamp: new Date().toISOString(),
    methods: {
      pin: true,
      pattern: true,
      email_recovery: true
    }
  });
});

module.exports = router;
