const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config({ path: '.env' });

// Database connection using the same configuration as the backend
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'esign',
  password: process.env.DB_PASSWORD || 'admin',
  port: process.env.DB_PORT || 5432,
});

async function runMigrations() {
  const client = await pool.connect();
  
  try {
    console.log('🚀 Running security migrations...');
    console.log('================================\n');
    
    // Migration 1: Password History
    console.log('1️⃣ Running password history migration...');
    const passwordHistorySQL = fs.readFileSync(
      path.join(__dirname, '..', 'database', 'migrations', '013_add_password_history.sql'),
      'utf8'
    );
    
    await client.query(passwordHistorySQL);
    console.log('✅ Password history migration completed successfully!\n');
    
    // Migration 2: Session Management
    console.log('2️⃣ Running session management migration...');
    const sessionManagementSQL = fs.readFileSync(
      path.join(__dirname, '..', 'database', 'migrations', '014_add_session_management.sql'),
      'utf8'
    );
    
    await client.query(sessionManagementSQL);
    console.log('✅ Session management migration completed successfully!\n');
    
    // Verify migrations
    console.log('3️⃣ Verifying migrations...');
    
    // Check password_history table
    const passwordHistoryCheck = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_name = 'password_history'
    `);
    
    if (passwordHistoryCheck.rows.length > 0) {
      console.log('✅ password_history table created');
    } else {
      console.log('❌ password_history table not found');
    }
    
    // Check user_sessions table
    const sessionCheck = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_name = 'user_sessions'
    `);
    
    if (sessionCheck.rows.length > 0) {
      console.log('✅ user_sessions table created');
    } else {
      console.log('❌ user_sessions table not found');
    }
    
    // Check session_activity table
    const sessionActivityCheck = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_name = 'session_activity'
    `);
    
    if (sessionActivityCheck.rows.length > 0) {
      console.log('✅ session_activity table created');
    } else {
      console.log('❌ session_activity table not found');
    }
    
    console.log('\n🎉 All security migrations completed successfully!');
    console.log('🛡️ Your e-signature system now has military-grade security!');
    
  } catch (error) {
    console.error('❌ Migration error:', error.message);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

// Run migrations
runMigrations()
  .then(() => {
    console.log('\n✅ Migration process completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Migration process failed:', error);
    process.exit(1);
  });
