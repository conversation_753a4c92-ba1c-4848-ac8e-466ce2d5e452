const { sanitizeInput, sanitizeEmail, sanitizeFilename } = require('../utils/validation');
const fileValidationService = require('../services/fileValidationService');

/**
 * Comprehensive security validation middleware
 * Sanitizes and validates all incoming request data
 */

// Request body sanitization middleware
const sanitizeRequestBody = (options = {}) => {
  const {
    allowHtml = false,
    maxStringLength = 1000,
    maxObjectDepth = 5
  } = options;

  return (req, res, next) => {
    try {
      if (req.body && typeof req.body === 'object') {
        req.body = sanitizeObject(req.body, {
          allowHtml,
          maxStringLength,
          maxObjectDepth,
          currentDepth: 0
        });
      }
      next();
    } catch (error) {
      console.error('Request body sanitization error:', error);
      return res.status(400).json({
        success: false,
        message: 'بيانات الطلب غير صالحة',
        code: 'INVALID_REQUEST_DATA'
      });
    }
  };
};

// Query parameters sanitization middleware
const sanitizeQueryParams = (req, res, next) => {
  try {
    if (req.query && typeof req.query === 'object') {
      for (const [key, value] of Object.entries(req.query)) {
        if (typeof value === 'string') {
          req.query[key] = sanitizeInput(value, { maxLength: 200 });
        }
      }
    }
    next();
  } catch (error) {
    console.error('Query parameters sanitization error:', error);
    return res.status(400).json({
      success: false,
      message: 'معاملات الاستعلام غير صالحة',
      code: 'INVALID_QUERY_PARAMS'
    });
  }
};

// Request headers validation middleware
const validateRequestHeaders = (req, res, next) => {
  try {
    // Check for suspicious headers
    const suspiciousHeaders = [
      'x-forwarded-host',
      'x-real-ip',
      'x-cluster-client-ip'
    ];

    for (const header of suspiciousHeaders) {
      if (req.headers[header]) {
        console.warn(`Suspicious header detected: ${header}`, {
          value: req.headers[header],
          ip: req.ip,
          userAgent: req.get('User-Agent')
        });
      }
    }

    // Validate User-Agent (skip for health checks and internal requests)
    const userAgent = req.get('User-Agent');
    const isHealthCheck = req.path === '/health' || req.path === '/api/health';
    const isInternalRequest = req.ip === '127.0.0.1' && !userAgent;

    if (!isHealthCheck && !isInternalRequest && (!userAgent || userAgent.length > 500)) {
      console.warn('Invalid or missing User-Agent', {
        userAgent: userAgent ? userAgent.substring(0, 100) + '...' : 'missing',
        ip: req.ip,
        path: req.path
      });
    }

    // Validate Content-Type for POST/PUT requests
    if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
      const contentType = req.get('Content-Type');
      if (contentType && !isValidContentType(contentType)) {
        return res.status(400).json({
          success: false,
          message: 'نوع المحتوى غير مدعوم',
          code: 'UNSUPPORTED_CONTENT_TYPE'
        });
      }
    }

    next();
  } catch (error) {
    console.error('Request headers validation error:', error);
    return res.status(400).json({
      success: false,
      message: 'رؤوس الطلب غير صالحة',
      code: 'INVALID_HEADERS'
    });
  }
};

// Enhanced file upload security validation
const validateFileUpload = async (req, res, next) => {
  try {
    if (req.file) {
      // Sanitize filename
      if (req.file.originalname) {
        req.file.originalname = sanitizeFilename(req.file.originalname);
      }

      // Perform comprehensive file validation
      const validationResult = await fileValidationService.validateFile(req.file, {
        checkContent: true,
        checkSignature: true,
        checkSize: true,
        allowedTypes: ['pdf', 'docx', 'doc', 'png', 'jpeg', 'gif'],
        customMaxSize: req.maxFileSize || 100 * 1024 * 1024
      });

      // Check validation results
      if (!validationResult.isValid) {
        console.warn('File validation failed:', {
          filename: req.file.originalname,
          errors: validationResult.errors,
          fileInfo: validationResult.fileInfo,
          ip: req.ip,
          userAgent: req.get('User-Agent')
        });

        return res.status(400).json({
          success: false,
          message: 'فشل في التحقق من الملف',
          errors: validationResult.errors,
          warnings: validationResult.warnings,
          code: 'FILE_VALIDATION_FAILED',
          details: {
            securityChecks: validationResult.securityChecks,
            fileInfo: {
              detectedType: validationResult.fileInfo.detectedType,
              size: validationResult.fileInfo.size,
              hash: validationResult.fileInfo.hash
            }
          }
        });
      }

      // Log warnings if any
      if (validationResult.warnings.length > 0) {
        console.warn('File validation warnings:', {
          filename: req.file.originalname,
          warnings: validationResult.warnings,
          fileInfo: validationResult.fileInfo
        });
      }

      // Attach validation results to request for later use
      req.fileValidation = validationResult;

      // Log successful validation
      console.log('File validation successful:', {
        filename: req.file.originalname,
        type: validationResult.fileInfo.detectedType,
        size: validationResult.fileInfo.size,
        hash: validationResult.fileInfo.hash.substring(0, 16) + '...'
      });
    }

    next();
  } catch (error) {
    console.error('File upload validation error:', error);
    return res.status(500).json({
      success: false,
      message: 'فشل في التحقق من الملف',
      code: 'FILE_VALIDATION_ERROR'
    });
  }
};

// SQL injection detection middleware
const detectSQLInjection = (req, res, next) => {
  try {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
      /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi,
      /(--|\/\*|\*\/|;)/g,
      /(\b(SCRIPT|JAVASCRIPT|VBSCRIPT|ONLOAD|ONERROR)\b)/gi
    ];

    const checkForSQLInjection = (obj, path = '') => {
      if (typeof obj === 'string') {
        for (const pattern of sqlPatterns) {
          if (pattern.test(obj)) {
            console.warn('Potential SQL injection detected', {
              path,
              value: obj.substring(0, 100),
              ip: req.ip,
              userAgent: req.get('User-Agent')
            });
            return true;
          }
        }
      } else if (typeof obj === 'object' && obj !== null) {
        for (const [key, value] of Object.entries(obj)) {
          if (checkForSQLInjection(value, `${path}.${key}`)) {
            return true;
          }
        }
      }
      return false;
    };

    if (checkForSQLInjection(req.body, 'body') || 
        checkForSQLInjection(req.query, 'query') ||
        checkForSQLInjection(req.params, 'params')) {
      return res.status(400).json({
        success: false,
        message: 'طلب غير صالح',
        code: 'INVALID_REQUEST'
      });
    }

    next();
  } catch (error) {
    console.error('SQL injection detection error:', error);
    next(); // Continue on error to avoid blocking legitimate requests
  }
};

// Helper functions
const sanitizeObject = (obj, options) => {
  const { allowHtml, maxStringLength, maxObjectDepth, currentDepth } = options;

  if (currentDepth >= maxObjectDepth) {
    return '[Object too deep]';
  }

  if (Array.isArray(obj)) {
    return obj.map(item => sanitizeObject(item, { ...options, currentDepth: currentDepth + 1 }));
  }

  if (typeof obj === 'object' && obj !== null) {
    const sanitized = {};
    for (const [key, value] of Object.entries(obj)) {
      const sanitizedKey = sanitizeInput(key, { allowHtml: false, maxLength: 100 });
      sanitized[sanitizedKey] = sanitizeObject(value, { ...options, currentDepth: currentDepth + 1 });
    }
    return sanitized;
  }

  if (typeof obj === 'string') {
    return sanitizeInput(obj, { allowHtml, maxLength: maxStringLength });
  }

  return obj;
};

const isValidContentType = (contentType) => {
  const allowedTypes = [
    'application/json',
    'application/x-www-form-urlencoded',
    'multipart/form-data',
    'text/plain',
    'application/pdf',
    'image/png',
    'image/jpeg',
    'image/jpg',
    'image/svg+xml'
  ];

  return allowedTypes.some(type => contentType.toLowerCase().includes(type));
};

const containsSuspiciousPatterns = (file) => {
  if (!file.buffer) return false;

  // Check for embedded scripts in files
  const suspiciousPatterns = [
    /<script/gi,
    /javascript:/gi,
    /vbscript:/gi,
    /data:text\/html/gi,
    /data:application\/javascript/gi
  ];

  const fileContent = file.buffer.toString('utf8', 0, Math.min(file.buffer.length, 1024));
  return suspiciousPatterns.some(pattern => pattern.test(fileContent));
};

module.exports = {
  sanitizeRequestBody,
  sanitizeQueryParams,
  validateRequestHeaders,
  validateFileUpload,
  detectSQLInjection
};
