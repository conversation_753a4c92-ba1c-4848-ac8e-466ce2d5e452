import React, { useState, useEffect } from 'react';
import { useAuth } from '../services/AuthContext';

interface LocationConsentProps {
  onConsentChange?: (granted: boolean) => void;
  showModal?: boolean;
  onClose?: () => void;
}

interface ConsentData {
  consentGiven: boolean;
  consentType: string;
  consentDate: string;
  dataRetentionDays: number;
  allowAdminMonitoring: boolean;
  allowLocationHistory: boolean;
}

const LocationConsent: React.FC<LocationConsentProps> = ({ 
  onConsentChange, 
  showModal = false, 
  onClose 
}) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [consents, setConsents] = useState<ConsentData[]>([]);
  const [showConsentModal, setShowConsentModal] = useState(showModal);
  const [consentForm, setConsentForm] = useState({
    consentType: 'browser_geolocation',
    dataRetentionDays: 90,
    allowAdminMonitoring: true,
    allowLocationHistory: true
  });

  useEffect(() => {
    if (user) {
      fetchConsentStatus();
    }
  }, [user]);

  useEffect(() => {
    setShowConsentModal(showModal);
  }, [showModal]);

  const fetchConsentStatus = async () => {
    try {
      const response = await fetch('/api/location/consent', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const result = await response.json();
        setConsents(result.data || []);
      }
    } catch (error) {
      console.error('Error fetching consent status:', error);
    }
  };

  const handleConsentSubmit = async (granted: boolean) => {
    setLoading(true);
    try {
      const response = await fetch('/api/location/consent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          consentGiven: granted,
          ...consentForm
        })
      });

      if (response.ok) {
        const result = await response.json();
        await fetchConsentStatus();
        setShowConsentModal(false);
        onConsentChange?.(granted);
        onClose?.();
        
        // Show success message
        alert(result.message);
      } else {
        const error = await response.json();
        alert(error.message || 'خطأ في تحديث الموافقة');
      }
    } catch (error) {
      console.error('Error updating consent:', error);
      alert('خطأ في الاتصال بالخادم');
    } finally {
      setLoading(false);
    }
  };

  const hasActiveConsent = (type: string) => {
    return consents.some(consent => 
      consent.consentType === type && consent.consentGiven
    );
  };

  const ConsentModal = () => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" dir="rtl">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold text-gray-900 font-['Almarai']">
              موافقة تتبع الموقع الجغرافي
            </h2>
            <button
              onClick={() => {
                setShowConsentModal(false);
                onClose?.();
              }}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Privacy Notice */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h3 className="font-semibold text-blue-900 mb-2 font-['Almarai']">
              إشعار الخصوصية
            </h3>
            <div className="text-sm text-blue-800 space-y-2 font-['Almarai']">
              <p>• سيتم استخدام موقعك الجغرافي لأغراض الأمان ومراقبة النشاط</p>
              <p>• يمكن للمديرين مشاهدة موقعك الحالي وتاريخ المواقع</p>
              <p>• سيتم تشفير وحماية جميع بيانات الموقع</p>
              <p>• يمكنك سحب الموافقة في أي وقت من الإعدادات</p>
              <p>• سيتم حذف البيانات القديمة تلقائياً حسب المدة المحددة</p>
            </div>
          </div>

          {/* Consent Form */}
          <div className="space-y-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 font-['Almarai']">
                نوع التتبع
              </label>
              <select
                value={consentForm.consentType}
                onChange={(e) => setConsentForm({...consentForm, consentType: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 font-['Almarai']"
              >
                <option value="browser_geolocation">تتبع GPS من المتصفح</option>
                <option value="ip_tracking">تتبع عبر عنوان IP فقط</option>
                <option value="full_tracking">تتبع شامل (GPS + IP)</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 font-['Almarai']">
                مدة الاحتفاظ بالبيانات (بالأيام)
              </label>
              <select
                value={consentForm.dataRetentionDays}
                onChange={(e) => setConsentForm({...consentForm, dataRetentionDays: parseInt(e.target.value)})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 font-['Almarai']"
              >
                <option value={30}>30 يوم</option>
                <option value={90}>90 يوم</option>
                <option value={180}>180 يوم</option>
                <option value={365}>سنة واحدة</option>
              </select>
            </div>

            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={consentForm.allowAdminMonitoring}
                  onChange={(e) => setConsentForm({...consentForm, allowAdminMonitoring: e.target.checked})}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="mr-2 text-sm text-gray-700 font-['Almarai']">
                  السماح للمديرين بمراقبة موقعي في الوقت الفعلي
                </span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={consentForm.allowLocationHistory}
                  onChange={(e) => setConsentForm({...consentForm, allowLocationHistory: e.target.checked})}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="mr-2 text-sm text-gray-700 font-['Almarai']">
                  السماح بحفظ تاريخ المواقع
                </span>
              </label>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 justify-end">
            <button
              onClick={() => handleConsentSubmit(false)}
              disabled={loading}
              className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors font-['Almarai'] disabled:opacity-50"
            >
              رفض
            </button>
            <button
              onClick={() => handleConsentSubmit(true)}
              disabled={loading}
              className="px-6 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-md transition-colors font-['Almarai'] disabled:opacity-50"
            >
              {loading ? 'جاري الحفظ...' : 'موافق'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const ConsentStatus = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4" dir="rtl">
      <h3 className="font-semibold text-gray-900 mb-3 font-['Almarai']">
        حالة موافقة تتبع الموقع
      </h3>
      
      {consents.length === 0 ? (
        <div className="text-center py-4">
          <p className="text-gray-500 mb-3 font-['Almarai']">لم يتم منح أي موافقة لتتبع الموقع</p>
          <button
            onClick={() => setShowConsentModal(true)}
            className="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-md transition-colors font-['Almarai']"
          >
            إدارة الموافقة
          </button>
        </div>
      ) : (
        <div className="space-y-3">
          {consents.map((consent, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
              <div>
                <p className="font-medium text-gray-900 font-['Almarai']">
                  {consent.consentType === 'browser_geolocation' && 'تتبع GPS من المتصفح'}
                  {consent.consentType === 'ip_tracking' && 'تتبع عبر عنوان IP'}
                  {consent.consentType === 'full_tracking' && 'تتبع شامل'}
                </p>
                <p className="text-sm text-gray-600 font-['Almarai']">
                  تاريخ الموافقة: {new Date(consent.consentDate).toLocaleDateString('ar-SA')}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <span className={`px-2 py-1 rounded-full text-xs font-['Almarai'] ${
                  consent.consentGiven 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {consent.consentGiven ? 'مفعل' : 'معطل'}
                </span>
              </div>
            </div>
          ))}
          
          <button
            onClick={() => setShowConsentModal(true)}
            className="w-full px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-md transition-colors font-['Almarai']"
          >
            تعديل الموافقة
          </button>
        </div>
      )}
    </div>
  );

  if (showModal || showConsentModal) {
    return <ConsentModal />;
  }

  return <ConsentStatus />;
};

export default LocationConsent;
