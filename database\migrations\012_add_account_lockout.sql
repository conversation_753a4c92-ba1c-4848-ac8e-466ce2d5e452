-- Migration: Add account lockout functionality
-- Description: Add fields to track failed login attempts and account lockout status

-- Add account lockout columns to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS failed_login_attempts INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS locked_until TIMESTAMP NULL;

-- Create index for efficient lockout queries
CREATE INDEX IF NOT EXISTS idx_users_locked_until ON users(locked_until);
CREATE INDEX IF NOT EXISTS idx_users_failed_attempts ON users(failed_login_attempts);

-- Add comments for documentation
COMMENT ON COLUMN users.failed_login_attempts IS 'Number of consecutive failed login attempts';
COMMENT ON COLUMN users.locked_until IS 'Timestamp until which the account is locked (NULL if not locked)';

-- Update existing users to have 0 failed attempts
UPDATE users SET failed_login_attempts = 0 WHERE failed_login_attempts IS NULL;

-- Create function to automatically unlock expired accounts
CREATE OR REPLACE FUNCTION unlock_expired_accounts()
RETURNS INTEGER AS $$
DECLARE
    unlocked_count INTEGER;
BEGIN
    UPDATE users 
    SET failed_login_attempts = 0, locked_until = NULL 
    WHERE locked_until IS NOT NULL AND locked_until <= NOW();
    
    GET DIAGNOSTICS unlocked_count = ROW_COUNT;
    
    IF unlocked_count > 0 THEN
        INSERT INTO logs (action, details) 
        VALUES ('ACCOUNTS_AUTO_UNLOCKED', jsonb_build_object('count', unlocked_count));
    END IF;
    
    RETURN unlocked_count;
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to run the unlock function (optional - can be run via cron)
-- This is commented out as it requires pg_cron extension
-- SELECT cron.schedule('unlock-expired-accounts', '*/5 * * * *', 'SELECT unlock_expired_accounts();');

COMMENT ON FUNCTION unlock_expired_accounts() IS 'Automatically unlock accounts whose lockout period has expired';
