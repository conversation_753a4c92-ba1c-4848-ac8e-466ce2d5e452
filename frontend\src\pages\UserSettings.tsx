import React, { useState } from 'react';
import { useAuth } from '../services/AuthContext';

const UserSettings: React.FC = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState('');
  const [error, setError] = useState('');
  
  // Password change form state
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordForm(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear errors when user starts typing
    if (error) setError('');
  };

  const validatePasswordForm = () => {
    if (!passwordForm.currentPassword) {
      setError('كلمة المرور الحالية مطلوبة');
      return false;
    }
    
    if (!passwordForm.newPassword) {
      setError('كلمة المرور الجديدة مطلوبة');
      return false;
    }
    
    if (passwordForm.newPassword.length < 8) {
      setError('كلمة المرور الجديدة يجب أن تكون 8 أحرف على الأقل');
      return false;
    }
    
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      setError('كلمات المرور غير متطابقة');
      return false;
    }
    
    if (passwordForm.currentPassword === passwordForm.newPassword) {
      setError('كلمة المرور الجديدة يجب أن تكون مختلفة عن الحالية');
      return false;
    }
    
    return true;
  };

  const handleSubmitPasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validatePasswordForm()) {
      return;
    }
    
    setLoading(true);
    setError('');
    setSuccess('');
    
    try {
      const response = await fetch('/api/auth/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          currentPassword: passwordForm.currentPassword,
          newPassword: passwordForm.newPassword
        })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'فشل في تغيير كلمة المرور');
      }
      
      setSuccess('تم تغيير كلمة المرور بنجاح');
      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
      
      // Auto-clear success message after 5 seconds
      setTimeout(() => setSuccess(''), 5000);
      
    } catch (error: any) {
      console.error('Password change error:', error);
      setError(error.message || 'فشل في تغيير كلمة المرور');
      
      // Auto-clear error after 5 seconds
      setTimeout(() => setError(''), 5000);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setPasswordForm({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
    setError('');
    setSuccess('');
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6" dir="rtl">
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h1 className="text-2xl font-bold text-gray-800 mb-4 font-['Almarai']">إعدادات المستخدم</h1>
        <p className="text-gray-600 mb-6 font-['Almarai']">إدارة معلومات حسابك وإعداداتك</p>

        {/* User Information Section */}
        <div className="mb-8 p-4 bg-gray-50 rounded-lg">
          <h2 className="text-lg font-semibold text-gray-800 mb-4 font-['Almarai']">معلومات الحساب</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 font-['Almarai']">
                البريد الإلكتروني
              </label>
              <div className="px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-600 font-['Almarai']">
                {user?.email}
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 font-['Almarai']">
                تاريخ إنشاء الحساب
              </label>
              <div className="px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-600 font-['Almarai']">
                {user?.createdAt ? new Date(user.createdAt).toLocaleDateString('en-US') : 'غير متاح'}
              </div>
            </div>
          </div>
        </div>

        {/* Password Change Section */}
        <div className="border-t border-gray-200 pt-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4 font-['Almarai']">تغيير كلمة المرور</h2>
          
          {/* Success Message */}
          {success && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4 font-['Almarai']">
              {success}
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 font-['Almarai']">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmitPasswordChange} className="space-y-4">
            <div>
              <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-700 mb-1 font-['Almarai']">
                كلمة المرور الحالية
              </label>
              <input
                type="password"
                id="currentPassword"
                name="currentPassword"
                value={passwordForm.currentPassword}
                onChange={handlePasswordChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 font-['Almarai']"
                placeholder="أدخل كلمة المرور الحالية"
                required
              />
            </div>

            <div>
              <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700 mb-1 font-['Almarai']">
                كلمة المرور الجديدة
              </label>
              <input
                type="password"
                id="newPassword"
                name="newPassword"
                value={passwordForm.newPassword}
                onChange={handlePasswordChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 font-['Almarai']"
                placeholder="أدخل كلمة المرور الجديدة"
                required
                minLength={8}
              />
              <p className="text-xs text-gray-500 mt-1 font-['Almarai']">
                يجب أن تحتوي على 8 أحرف على الأقل
              </p>
            </div>

            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1 font-['Almarai']">
                تأكيد كلمة المرور الجديدة
              </label>
              <input
                type="password"
                id="confirmPassword"
                name="confirmPassword"
                value={passwordForm.confirmPassword}
                onChange={handlePasswordChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 font-['Almarai']"
                placeholder="أعد إدخال كلمة المرور الجديدة"
                required
              />
            </div>

            <div className="flex items-center space-x-reverse space-x-4 pt-4">
              <button
                type="submit"
                disabled={loading}
                className={`px-6 py-2 rounded-md font-medium font-['Almarai'] transition-colors duration-200 ${
                  loading
                    ? 'bg-gray-400 text-white cursor-not-allowed'
                    : 'bg-primary-600 hover:bg-primary-700 text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
                }`}
              >
                {loading ? 'جاري التغيير...' : 'تغيير كلمة المرور'}
              </button>
              
              <button
                type="button"
                onClick={resetForm}
                className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 font-medium font-['Almarai'] transition-colors duration-200"
              >
                إلغاء
              </button>
            </div>
          </form>
        </div>



      </div>
    </div>
  );
};

export default UserSettings;
