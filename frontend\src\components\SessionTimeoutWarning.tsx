import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '../services/AuthContext';
import { authAPI } from '../services/api';

interface SessionTimeoutWarningProps {
  warningThreshold?: number; // milliseconds before expiry to show warning
}

const SessionTimeoutWarning: React.FC<SessionTimeoutWarningProps> = ({ 
  warningThreshold = 5 * 60 * 1000 // 5 minutes default
}) => {
  const { sessionTimeLeft, logout } = useAuth();
  const [showWarning, setShowWarning] = useState(false);
  const [timeLeft, setTimeLeft] = useState<number>(0);
  const [isExtending, setIsExtending] = useState(false);

  // Memoize logout function to prevent unnecessary re-renders
  const memoizedLogout = useCallback(() => {
    logout();
  }, [logout]);

  useEffect(() => {
    if (!sessionTimeLeft) return;

    if (sessionTimeLeft <= warningThreshold && sessionTimeLeft > 0) {
      setShowWarning(true);
      setTimeLeft(sessionTimeLeft);

      const interval = setInterval(() => {
        setTimeLeft(prev => {
          const newTime = prev - 1000;
          if (newTime <= 0) {
            clearInterval(interval);
            setShowWarning(false);
            memoizedLogout();
            return 0;
          }
          return newTime;
        });
      }, 1000);

      return () => clearInterval(interval);
    } else {
      setShowWarning(false);
    }
  }, [sessionTimeLeft, warningThreshold, memoizedLogout]);

  const handleExtendSession = async () => {
    setIsExtending(true);
    try {
      // Make a simple API call to trigger token refresh
      await authAPI.getProfile();
      setShowWarning(false);
    } catch (error: any) {
      console.error('Failed to extend session:', error);
      // Only logout if it's an authentication error, not a network error
      if (error.response?.status === 401 || error.response?.status === 403) {
        logout();
      } else {
        // For network errors, just hide the warning and let the user try again later
        setShowWarning(false);
      }
    } finally {
      setIsExtending(false);
    }
  };

  const handleLogout = useCallback(() => {
    setShowWarning(false);
    memoizedLogout();
  }, [memoizedLogout]);

  const formatTime = useMemo(() => {
    return (milliseconds: number): string => {
      const minutes = Math.floor(milliseconds / 60000);
      const seconds = Math.floor((milliseconds % 60000) / 1000);
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    };
  }, []);

  if (!showWarning) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md mx-4 text-center">
        <div className="mb-4">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 mb-4">
            <svg className="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            تحذير انتهاء الجلسة
          </h3>
          <p className="text-sm text-gray-500 mb-4">
            ستنتهي جلستك خلال {formatTime(timeLeft)}
          </p>
          <p className="text-sm text-gray-600">
            هل تريد تمديد جلستك أم تسجيل الخروج؟
          </p>
        </div>
        
        <div className="flex space-x-3 space-x-reverse">
          <button
            onClick={handleExtendSession}
            disabled={isExtending}
            className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isExtending ? 'جاري التمديد...' : 'تمديد الجلسة'}
          </button>
          <button
            onClick={handleLogout}
            className="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400"
          >
            تسجيل الخروج
          </button>
        </div>
      </div>
    </div>
  );
};

export default SessionTimeoutWarning;
