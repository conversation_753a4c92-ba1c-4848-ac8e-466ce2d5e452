interface UserEvent {
  type: 'page_view' | 'click' | 'form_submit' | 'error' | 'performance' | 'user_action';
  timestamp: number;
  sessionId: string;
  userId?: string;
  page: string;
  data: Record<string, any>;
  userAgent: string;
  viewport: {
    width: number;
    height: number;
  };
}

interface PerformanceMetrics {
  pageLoadTime: number;
  domContentLoaded: number;
  firstContentfulPaint?: number;
  largestContentfulPaint?: number;
  cumulativeLayoutShift?: number;
  firstInputDelay?: number;
}

interface UserJourney {
  sessionId: string;
  userId?: string;
  startTime: number;
  events: UserEvent[];
  currentPage: string;
  referrer: string;
  userAgent: string;
  language: string;
  timezone: string;
}

class AnalyticsService {
  private sessionId: string;
  private userId?: string;
  private journey: UserJourney;
  private eventQueue: UserEvent[] = [];
  private isEnabled: boolean = true;
  private batchSize: number = 10;
  private flushInterval: number = 30000; // 30 seconds
  private flushTimer?: NodeJS.Timeout;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.journey = this.initializeJourney();
    this.setupEventListeners();
    this.startPerformanceMonitoring();
    this.scheduleFlush();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private initializeJourney(): UserJourney {
    return {
      sessionId: this.sessionId,
      startTime: Date.now(),
      events: [],
      currentPage: window.location.pathname,
      referrer: document.referrer,
      userAgent: navigator.userAgent,
      language: navigator.language,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    };
  }

  private setupEventListeners(): void {
    // Page visibility changes
    document.addEventListener('visibilitychange', () => {
      this.trackEvent('user_action', {
        action: document.hidden ? 'page_hidden' : 'page_visible'
      });
    });

    // Unload events
    window.addEventListener('beforeunload', () => {
      this.flush(true); // Synchronous flush on unload
    });

    // Error tracking
    window.addEventListener('error', (event) => {
      this.trackError({
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack
      });
    });

    // Unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.trackError({
        message: 'Unhandled Promise Rejection',
        reason: event.reason?.toString(),
        stack: event.reason?.stack
      });
    });
  }

  private startPerformanceMonitoring(): void {
    // Wait for page load
    if (document.readyState === 'complete') {
      this.collectPerformanceMetrics();
    } else {
      window.addEventListener('load', () => {
        setTimeout(() => this.collectPerformanceMetrics(), 1000);
      });
    }
  }

  private collectPerformanceMetrics(): PerformanceMetrics | null {
    try {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;

      if (!navigation) return null;

      const metrics: PerformanceMetrics = {
        pageLoadTime: navigation.loadEventEnd - navigation.fetchStart,
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
        firstContentfulPaint: 0,
        largestContentfulPaint: 0
      };

      // Get paint metrics
      const paintEntries = performance.getEntriesByType('paint');
      const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint');
      if (fcpEntry) {
        metrics.firstContentfulPaint = fcpEntry.startTime;
      }

      // Get LCP metric
      const lcpEntries = performance.getEntriesByType('largest-contentful-paint');
      if (lcpEntries.length > 0) {
        metrics.largestContentfulPaint = lcpEntries[lcpEntries.length - 1].startTime;
      }

      // Web Vitals (if available)
      if ('PerformanceObserver' in window) {
        // CLS
        new PerformanceObserver((list) => {
          let clsValue = 0;
          for (const entry of list.getEntries()) {
            if (!(entry as any).hadRecentInput) {
              clsValue += (entry as any).value;
            }
          }
          metrics.cumulativeLayoutShift = clsValue;
        }).observe({ entryTypes: ['layout-shift'] });

        // FID
        new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            metrics.firstInputDelay = (entry as any).processingStart - entry.startTime;
          }
        }).observe({ entryTypes: ['first-input'] });
      }

      this.trackEvent('performance', { metrics });
      return metrics;
    } catch (error) {
      console.error('Failed to collect performance metrics:', error);
      return null;
    }
  }

  private scheduleFlush(): void {
    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.flushInterval);
  }

  public setUserId(userId: string): void {
    this.userId = userId;
    this.journey.userId = userId;
  }

  public trackPageView(page: string, additionalData?: Record<string, any>): void {
    this.journey.currentPage = page;
    this.trackEvent('page_view', {
      page,
      title: document.title,
      url: window.location.href,
      ...additionalData
    });
  }

  public trackClick(element: string, additionalData?: Record<string, any>): void {
    this.trackEvent('click', {
      element,
      page: this.journey.currentPage,
      ...additionalData
    });
  }

  public trackFormSubmit(formName: string, success: boolean, additionalData?: Record<string, any>): void {
    this.trackEvent('form_submit', {
      formName,
      success,
      page: this.journey.currentPage,
      ...additionalData
    });
  }

  public trackUserAction(action: string, additionalData?: Record<string, any>): void {
    this.trackEvent('user_action', {
      action,
      page: this.journey.currentPage,
      ...additionalData
    });
  }

  public trackError(error: {
    message: string;
    filename?: string;
    lineno?: number;
    colno?: number;
    stack?: string;
    [key: string]: any;
  }): void {
    this.trackEvent('error', {
      ...error,
      page: this.journey.currentPage,
      url: window.location.href
    });
  }

  private trackEvent(type: UserEvent['type'], data: Record<string, any>): void {
    if (!this.isEnabled) return;

    const event: UserEvent = {
      type,
      timestamp: Date.now(),
      sessionId: this.sessionId,
      userId: this.userId,
      page: this.journey.currentPage,
      data,
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      }
    };

    this.eventQueue.push(event);
    this.journey.events.push(event);

    // Auto-flush if queue is full
    if (this.eventQueue.length >= this.batchSize) {
      this.flush();
    }
  }

  private async flush(synchronous: boolean = false): Promise<void> {
    if (this.eventQueue.length === 0) return;

    const events = [...this.eventQueue];
    this.eventQueue = [];

    const payload = {
      sessionId: this.sessionId,
      userId: this.userId,
      events,
      journey: {
        ...this.journey,
        events: this.journey.events.slice(-50) // Keep last 50 events
      }
    };

    try {
      const token = localStorage.getItem('token');
      const headers: HeadersInit = {
        'Content-Type': 'application/json'
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      if (synchronous) {
        // Use sendBeacon for synchronous sending on page unload
        const blob = new Blob([JSON.stringify(payload)], { type: 'application/json' });
        navigator.sendBeacon('/api/analytics/events', blob);
      } else {
        await fetch('/api/analytics/events', {
          method: 'POST',
          headers,
          body: JSON.stringify(payload)
        });
      }
    } catch (error) {
      console.warn('Failed to send analytics events:', error);
      // Re-queue events on failure (but limit to prevent memory issues)
      this.eventQueue = [...events.slice(-this.batchSize), ...this.eventQueue];
    }
  }

  public getSessionSummary(): {
    sessionId: string;
    userId?: string;
    duration: number;
    pageViews: number;
    clicks: number;
    errors: number;
    currentPage: string;
  } {
    const now = Date.now();
    const events = this.journey.events;

    return {
      sessionId: this.sessionId,
      userId: this.userId,
      duration: now - this.journey.startTime,
      pageViews: events.filter(e => e.type === 'page_view').length,
      clicks: events.filter(e => e.type === 'click').length,
      errors: events.filter(e => e.type === 'error').length,
      currentPage: this.journey.currentPage
    };
  }

  public enable(): void {
    this.isEnabled = true;
  }

  public disable(): void {
    this.isEnabled = false;
  }

  public isAnalyticsEnabled(): boolean {
    return this.isEnabled;
  }

  public destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    this.flush(true);
    this.isEnabled = false;
  }
}

// Create singleton instance
const analyticsService = new AnalyticsService();

// React hook for analytics
export const useAnalytics = () => {
  const trackPageView = (page: string, additionalData?: Record<string, any>) => {
    analyticsService.trackPageView(page, additionalData);
  };

  const trackClick = (element: string, additionalData?: Record<string, any>) => {
    analyticsService.trackClick(element, additionalData);
  };

  const trackFormSubmit = (formName: string, success: boolean, additionalData?: Record<string, any>) => {
    analyticsService.trackFormSubmit(formName, success, additionalData);
  };

  const trackUserAction = (action: string, additionalData?: Record<string, any>) => {
    analyticsService.trackUserAction(action, additionalData);
  };

  const trackError = (error: Parameters<typeof analyticsService.trackError>[0]) => {
    analyticsService.trackError(error);
  };

  const getSessionSummary = () => {
    return analyticsService.getSessionSummary();
  };

  return {
    trackPageView,
    trackClick,
    trackFormSubmit,
    trackUserAction,
    trackError,
    getSessionSummary
  };
};

export default analyticsService;
