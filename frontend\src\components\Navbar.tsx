import React, { useState, useEffect, useRef } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../services/AuthContext';

const Navbar: React.FC = () => {
  const { user, logout } = useAuth();
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isAdminDropdownOpen, setIsAdminDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const isActivePage = (path: string) => location.pathname === path;
  const isAdmin = () => user?.role === 'admin';

  const handleLogout = () => {
    logout();
    setIsMobileMenuOpen(false);
    setIsAdminDropdownOpen(false);
  };

  // Close dropdown when clicking outside or on escape
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsAdminDropdownOpen(false);
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsAdminDropdownOpen(false);
      }
    };

    if (isAdminDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isAdminDropdownOpen]);

  return (
    <header className="bg-white shadow-md border-b border-gray-200 sticky top-0 z-40 overflow-visible" dir="rtl">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16 overflow-visible">

          {/* Logo */}
          <Link to="/dashboard" className="flex items-center space-x-reverse space-x-3 hover:opacity-90 transition-opacity flex-shrink-0">
            <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
              </svg>
            </div>
            <span className="text-xl font-bold text-gray-900 font-['Almarai'] whitespace-nowrap">
              نظام التوقيع الإلكتروني
            </span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-reverse space-x-6 flex-shrink-0 overflow-visible">
            {/* Main Navigation */}
            <Link
              to="/dashboard"
              className={`px-3 py-2 rounded-lg text-sm font-medium font-['Almarai'] transition-colors whitespace-nowrap ${
                isActivePage('/dashboard') || isActivePage('/dashboard-alt')
                  ? 'text-blue-600 bg-blue-50'
                  : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
              }`}
            >
              لوحة التحكم
            </Link>

            <Link
              to="/document-signing"
              className={`px-3 py-2 rounded-lg text-sm font-medium font-['Almarai'] transition-colors whitespace-nowrap ${
                isActivePage('/document-signing')
                  ? 'text-blue-600 bg-blue-50'
                  : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
              }`}
            >
              {isAdmin() ? 'رفع مستند' : 'توقيع مستند'}
            </Link>

            {/* Admin Dropdown Menu - Fixed Positioning */}
            {isAdmin() && (
              <div className="relative z-50" ref={dropdownRef}>
                <button
                  onClick={() => setIsAdminDropdownOpen(!isAdminDropdownOpen)}
                  className={`flex items-center px-3 py-2 rounded-lg text-sm font-medium font-['Almarai'] transition-colors whitespace-nowrap ${
                    isAdminDropdownOpen ||
                    isActivePage('/history') ||
                    isActivePage('/admin/records') ||
                    isActivePage('/admin/all-approved-documents') ||
                    isActivePage('/users') ||
                    isActivePage('/admin/geolocation-tracking') ||
                    isActivePage('/admin/document-signing') ||
                    isActivePage('/documentation')
                      ? 'text-blue-600 bg-blue-50'
                      : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                  }`}
                >
                  إدارة النظام
                  <svg className="mr-2 h-4 w-4 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                       style={{ transform: isAdminDropdownOpen ? 'rotate(180deg)' : 'rotate(0deg)' }}>
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {/* Dropdown Menu - Positioned outside header container */}
                {isAdminDropdownOpen && (
                  <>
                    {/* Backdrop */}
                    <div className="fixed inset-0 z-[9998]" onClick={() => setIsAdminDropdownOpen(false)}></div>

                    {/* Dropdown Content */}
                    <div className="fixed w-64 bg-white rounded-lg shadow-2xl border border-gray-200 py-3 z-[9999]"
                         style={{
                           position: 'fixed',
                           zIndex: 9999,
                           backgroundColor: 'white',
                           top: '64px', // Header height
                           left: dropdownRef.current ? dropdownRef.current.getBoundingClientRect().left : '0'
                         }}>
                      {/* Dropdown Links */}
                      <Link
                        to="/history"
                        onClick={() => setIsAdminDropdownOpen(false)}
                        className={`block px-4 py-2 text-sm font-medium font-['Almarai'] transition-colors ${
                          isActivePage('/history')
                            ? 'text-blue-600 bg-blue-50'
                            : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                        }`}
                      >
                        السجل
                      </Link>

                      <Link
                        to="/admin/records"
                        onClick={() => setIsAdminDropdownOpen(false)}
                        className={`block px-4 py-2 text-sm font-medium font-['Almarai'] transition-colors ${
                          isActivePage('/admin/records')
                            ? 'text-blue-600 bg-blue-50'
                            : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                        }`}
                      >
                        سجلات النظام
                      </Link>

                      <Link
                        to="/admin/all-approved-documents"
                        onClick={() => setIsAdminDropdownOpen(false)}
                        className={`block px-4 py-2 text-sm font-medium font-['Almarai'] transition-colors ${
                          isActivePage('/admin/all-approved-documents')
                            ? 'text-blue-600 bg-blue-50'
                            : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                        }`}
                      >
                        المستندات المعتمدة
                      </Link>

                      <Link
                        to="/users"
                        onClick={() => setIsAdminDropdownOpen(false)}
                        className={`block px-4 py-2 text-sm font-medium font-['Almarai'] transition-colors ${
                          isActivePage('/users')
                            ? 'text-blue-600 bg-blue-50'
                            : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                        }`}
                      >
                        إدارة المستخدمين
                      </Link>

                      <Link
                        to="/admin/geolocation-tracking"
                        onClick={() => setIsAdminDropdownOpen(false)}
                        className={`block px-4 py-2 text-sm font-medium font-['Almarai'] transition-colors ${
                          isActivePage('/admin/geolocation-tracking')
                            ? 'text-blue-600 bg-blue-50'
                            : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                        }`}
                      >
                        تتبع المواقع
                      </Link>

                      <Link
                        to="/admin/document-signing"
                        onClick={() => setIsAdminDropdownOpen(false)}
                        className={`block px-4 py-2 text-sm font-medium font-['Almarai'] transition-colors ${
                          isActivePage('/admin/document-signing')
                            ? 'text-blue-600 bg-blue-50'
                            : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                        }`}
                      >
                        توقيع المستندات (إداري)
                      </Link>

                      <Link
                        to="/documentation"
                        onClick={() => setIsAdminDropdownOpen(false)}
                        className={`block px-4 py-2 text-sm font-medium font-['Almarai'] transition-colors ${
                          isActivePage('/documentation')
                            ? 'text-blue-600 bg-blue-50'
                            : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                        }`}
                      >
                        التوثيق
                      </Link>
                    </div>
                  </>
                )}
              </div>
            )}

            {/* User Menu Items */}
            {!isAdmin() && (
              <>
                <Link
                  to="/my-approved-documents"
                  className={`px-3 py-2 rounded-lg text-sm font-medium font-['Almarai'] transition-colors whitespace-nowrap ${
                    isActivePage('/my-approved-documents')
                      ? 'text-blue-600 bg-blue-50'
                      : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                  }`}
                >
                  مستنداتي المعتمدة
                </Link>

                <Link
                  to="/documents"
                  className={`px-3 py-2 rounded-lg text-sm font-medium font-['Almarai'] transition-colors whitespace-nowrap ${
                    isActivePage('/documents')
                      ? 'text-blue-600 bg-blue-50'
                      : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                  }`}
                >
                  المستندات
                </Link>
              </>
            )}

            <Link
              to="/mail"
              className={`px-3 py-2 rounded-lg text-sm font-medium font-['Almarai'] transition-colors whitespace-nowrap ${
                isActivePage('/mail')
                  ? 'text-blue-600 bg-blue-50'
                  : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
              }`}
            >
              البريد
            </Link>

            <Link
              to="/settings"
              className={`px-3 py-2 rounded-lg text-sm font-medium font-['Almarai'] transition-colors whitespace-nowrap ${
                isActivePage('/settings')
                  ? 'text-blue-600 bg-blue-50'
                  : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
              }`}
            >
              الإعدادات
            </Link>
          </nav>

          {/* User Menu */}
          <div className="flex items-center space-x-reverse space-x-4">
            {/* Desktop User Menu */}
            <div className="hidden lg:flex items-center space-x-reverse space-x-4 flex-shrink-0">
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900 font-['Almarai']">
                  {user?.email?.split('@')[0] || 'مستخدم'}
                </p>
                <p className="text-xs text-gray-500 font-['Almarai']">
                  {isAdmin() ? 'مدير النظام' : 'مستخدم'}
                </p>
              </div>

              <div className="flex items-center space-x-reverse space-x-2">
                <Link
                  to="/signature-upload"
                  className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                  title="إدارة التوقيع"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                  </svg>
                </Link>

                <Link
                  to="/settings"
                  className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                  title="الإعدادات"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </Link>

                <button
                  onClick={handleLogout}
                  className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                  title="تسجيل الخروج"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 transition-colors flex-shrink-0"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {isMobileMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="lg:hidden border-t border-gray-200 py-4 max-h-96 overflow-y-auto">
            <div className="space-y-1">
              {/* Main Navigation */}
              <Link
                to="/dashboard"
                onClick={() => setIsMobileMenuOpen(false)}
                className={`block px-3 py-2 rounded-md text-base font-medium font-['Almarai'] ${
                  isActivePage('/dashboard') || isActivePage('/dashboard-alt')
                    ? 'text-blue-600 bg-blue-50'
                    : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                }`}
              >
                لوحة التحكم
              </Link>

              <Link
                to="/document-signing"
                onClick={() => setIsMobileMenuOpen(false)}
                className={`block px-3 py-2 rounded-md text-base font-medium font-['Almarai'] ${
                  isActivePage('/document-signing')
                    ? 'text-blue-600 bg-blue-50'
                    : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                }`}
              >
                {isAdmin() ? 'رفع مستند' : 'توقيع مستند'}
              </Link>

              {/* Admin Menu Items */}
              {isAdmin() && (
                <>
                  <div className="border-t border-gray-200 pt-2 mt-2">
                    <p className="text-xs font-semibold text-gray-500 font-['Almarai'] px-3 mb-1">إدارة النظام</p>
                  </div>

                  <Link
                    to="/history"
                    onClick={() => setIsMobileMenuOpen(false)}
                    className={`block px-3 py-2 rounded-md text-base font-medium font-['Almarai'] ${
                      isActivePage('/history')
                        ? 'text-blue-600 bg-blue-50'
                        : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                    }`}
                  >
                    السجل
                  </Link>

                  <Link
                    to="/admin/records"
                    onClick={() => setIsMobileMenuOpen(false)}
                    className={`block px-3 py-2 rounded-md text-base font-medium font-['Almarai'] ${
                      isActivePage('/admin/records')
                        ? 'text-blue-600 bg-blue-50'
                        : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                    }`}
                  >
                    سجلات النظام
                  </Link>

                  <Link
                    to="/admin/all-approved-documents"
                    onClick={() => setIsMobileMenuOpen(false)}
                    className={`block px-3 py-2 rounded-md text-base font-medium font-['Almarai'] ${
                      isActivePage('/admin/all-approved-documents')
                        ? 'text-blue-600 bg-blue-50'
                        : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                    }`}
                  >
                    المستندات المعتمدة
                  </Link>

                  <Link
                    to="/users"
                    onClick={() => setIsMobileMenuOpen(false)}
                    className={`block px-3 py-2 rounded-md text-base font-medium font-['Almarai'] ${
                      isActivePage('/users')
                        ? 'text-blue-600 bg-blue-50'
                        : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                    }`}
                  >
                    إدارة المستخدمين
                  </Link>

                  <Link
                    to="/admin/geolocation-tracking"
                    onClick={() => setIsMobileMenuOpen(false)}
                    className={`block px-3 py-2 rounded-md text-base font-medium font-['Almarai'] ${
                      isActivePage('/admin/geolocation-tracking')
                        ? 'text-blue-600 bg-blue-50'
                        : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                    }`}
                  >
                    تتبع المواقع
                  </Link>

                  <Link
                    to="/admin/document-signing"
                    onClick={() => setIsMobileMenuOpen(false)}
                    className={`block px-3 py-2 rounded-md text-base font-medium font-['Almarai'] ${
                      isActivePage('/admin/document-signing')
                        ? 'text-blue-600 bg-blue-50'
                        : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                    }`}
                  >
                    توقيع المستندات (إداري)
                  </Link>

                  <Link
                    to="/documentation"
                    onClick={() => setIsMobileMenuOpen(false)}
                    className={`block px-3 py-2 rounded-md text-base font-medium font-['Almarai'] ${
                      isActivePage('/documentation')
                        ? 'text-blue-600 bg-blue-50'
                        : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                    }`}
                  >
                    التوثيق
                  </Link>
                </>
              )}

              {/* User Menu Items */}
              {!isAdmin() && (
                <>
                  <Link
                    to="/my-approved-documents"
                    onClick={() => setIsMobileMenuOpen(false)}
                    className={`block px-3 py-2 rounded-md text-base font-medium font-['Almarai'] ${
                      isActivePage('/my-approved-documents')
                        ? 'text-blue-600 bg-blue-50'
                        : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                    }`}
                  >
                    مستنداتي المعتمدة
                  </Link>

                  <Link
                    to="/documents"
                    onClick={() => setIsMobileMenuOpen(false)}
                    className={`block px-3 py-2 rounded-md text-base font-medium font-['Almarai'] ${
                      isActivePage('/documents')
                        ? 'text-blue-600 bg-blue-50'
                        : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                    }`}
                  >
                    المستندات
                  </Link>
                </>
              )}

              <Link
                to="/mail"
                onClick={() => setIsMobileMenuOpen(false)}
                className={`block px-3 py-2 rounded-md text-base font-medium font-['Almarai'] ${
                  isActivePage('/mail')
                    ? 'text-blue-600 bg-blue-50'
                    : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                }`}
              >
                البريد
              </Link>

              <Link
                to="/signature-upload"
                onClick={() => setIsMobileMenuOpen(false)}
                className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 font-['Almarai']"
              >
                إدارة التوقيع
              </Link>

              <Link
                to="/settings"
                onClick={() => setIsMobileMenuOpen(false)}
                className={`block px-3 py-2 rounded-md text-base font-medium font-['Almarai'] ${
                  isActivePage('/settings')
                    ? 'text-blue-600 bg-blue-50'
                    : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                }`}
              >
                الإعدادات
              </Link>

              <button
                onClick={handleLogout}
                className="block w-full text-right px-3 py-2 rounded-md text-base font-medium text-red-600 hover:bg-red-50 font-['Almarai']"
              >
                تسجيل الخروج
              </button>
            </div>

            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="px-3">
                <p className="text-sm font-medium text-gray-900 font-['Almarai']">
                  {user?.email?.split('@')[0] || 'مستخدم'}
                </p>
                <p className="text-xs text-gray-500 font-['Almarai']">
                  {isAdmin() ? 'مدير النظام' : 'مستخدم'}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Navbar;