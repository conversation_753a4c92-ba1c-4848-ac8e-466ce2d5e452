import React from 'react';
import { Link, useLocation } from 'react-router-dom';

const DashboardSwitcher: React.FC = () => {
  const location = useLocation();
  const isAltDashboard = location.pathname === '/dashboard-alt';

  return (
    <div className="flex justify-center mb-6">
      <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-2 inline-flex">
        <div className="flex items-center space-x-2 space-x-reverse">
          <Link
            to="/dashboard"
            className={`
              flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 rounded-xl transition-all duration-300 group
              ${!isAltDashboard
                ? 'bg-gradient-to-br from-blue-500 to-blue-600 text-white shadow-lg'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }
            `}
            title="التخطيط الكلاسيكي"
          >
            <svg className="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
            </svg>
          </Link>

          <div className="w-px h-8 bg-gray-300"></div>

          <Link
            to="/dashboard-alt"
            className={`
              flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 rounded-xl transition-all duration-300 group
              ${isAltDashboard
                ? 'bg-gradient-to-br from-purple-500 to-purple-600 text-white shadow-lg'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }
            `}
            title="التخطيط البديل"
          >
            <svg className="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
          </Link>
        </div>
      </div>

      {/* Optional label below */}
      <div className="absolute mt-16 text-center">
        <span className="text-xs text-gray-500 font-['Almarai'] font-medium bg-white px-2 py-1 rounded-full shadow-sm">
          {isAltDashboard ? 'التخطيط البديل' : 'التخطيط الكلاسيكي'}
        </span>
      </div>
    </div>
  );
};

export default DashboardSwitcher;
