import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useLanguage } from '../contexts/LanguageContext';

interface BreadcrumbItem {
  label: string;
  path: string;
  isActive?: boolean;
  icon?: React.ReactNode;
  category?: string;
}

const Breadcrumb: React.FC = () => {
  const location = useLocation();
  const { t } = useLanguage();

  const getBreadcrumbItems = (): BreadcrumbItem[] => {
    const pathSegments = location.pathname.split('/').filter(segment => segment);
    const items: BreadcrumbItem[] = [];

    // Enhanced path mapping with icons and categories
    const pathMap: { [key: string]: { label: string; icon?: React.ReactNode; category?: string } } = {
      // Core Features
      'dashboard': {
        label: 'لوحة التحكم',
        icon: <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" /></svg>,
        category: 'core'
      },
      'dashboard-alt': {
        label: 'لوحة التحكم البديلة',
        icon: <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" /></svg>,
        category: 'core'
      },

      // Document Management
      'document-signing': {
        label: 'توقيع المستندات',
        icon: <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" /></svg>,
        category: 'documents'
      },
      'documents': {
        label: 'مستنداتي',
        icon: <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg>,
        category: 'documents'
      },
      'sign': {
        label: 'التوقيع المباشر',
        icon: <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" /></svg>,
        category: 'documents'
      },
      'my-approved-documents': {
        label: 'مستنداتي المعتمدة',
        icon: <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>,
        category: 'documents'
      },

      // Personal Services
      'signature-upload': {
        label: 'إدارة التوقيع',
        icon: <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" /></svg>,
        category: 'personal'
      },
      'mail': {
        label: 'صندوق الوارد',
        icon: <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2-2v10a2 2 0 002 2z" /></svg>,
        category: 'personal'
      },

      // System Tools
      'verify': {
        label: 'التحقق من المستند',
        icon: <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>,
        category: 'tools'
      },
      'history': {
        label: 'سجل العمليات',
        icon: <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>,
        category: 'tools'
      },
      'settings': {
        label: 'الإعدادات',
        icon: <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>,
        category: 'tools'
      },

      // Admin Functions
      'admin': {
        label: 'إدارة النظام',
        icon: <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.586-3H4a2 2 0 00-2 2v6a2 2 0 002 2h5.586l2.707-2.707a1 1 0 000-1.414L9.586 12z" /></svg>,
        category: 'admin'
      },
      'users': {
        label: 'إدارة المستخدمين',
        icon: <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" /></svg>,
        category: 'admin'
      },
      'records': {
        label: 'التقارير المتقدمة',
        icon: <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg>,
        category: 'admin'
      },
      'all-approved-documents': {
        label: 'جميع المستندات المعتمدة',
        icon: <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>,
        category: 'admin'
      },
      'geolocation-tracking': {
        label: 'تتبع المواقع',
        icon: <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" /><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" /></svg>,
        category: 'admin'
      },

      // Status Pages
      'signing-confirmation': {
        label: 'تأكيد التوقيع',
        icon: <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>,
        category: 'status'
      },
      'signing-error': {
        label: 'خطأ في التوقيع',
        icon: <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>,
        category: 'status'
      },
      'documentation': {
        label: 'دليل النظام',
        icon: <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" /></svg>,
        category: 'help'
      },
      'pdf-test': {
        label: 'اختبار PDF',
        icon: <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg>,
        category: 'tools'
      }
    };

    // If we're on the root dashboard, add it manually
    if (location.pathname === '/dashboard' || location.pathname === '/') {
      items.push({
        label: t.navigation.dashboard,
        path: '/dashboard',
        isActive: true,
        icon: pathMap['dashboard'].icon,
        category: pathMap['dashboard'].category
      });
    } else {
      // Add dashboard as first item for non-dashboard pages
      items.push({
        label: t.navigation.dashboard,
        path: '/dashboard',
        isActive: false,
        icon: pathMap['dashboard'].icon,
        category: pathMap['dashboard'].category
      });

      // Process path segments, skipping 'dashboard' to avoid duplication
      let currentPath = '';
      pathSegments.forEach((segment, index) => {
        currentPath += `/${segment}`;

        // Skip dashboard segment to avoid duplication
        if (segment === 'dashboard') {
          return;
        }

        // Special case for admin/document-signing
        if (currentPath === '/admin/document-signing') {
          const isLast = index === pathSegments.length - 1;
          items.push({
            label: 'إدارة المستندات المعلقة',
            path: currentPath,
            isActive: isLast
          });
        } else if (pathMap[segment]) {
          const isLast = index === pathSegments.length - 1;
          const pathInfo = pathMap[segment];
          items.push({
            label: pathInfo.label,
            path: currentPath,
            isActive: isLast,
            icon: pathInfo.icon,
            category: pathInfo.category
          });
        }
      });
    }

    return items;
  };

  const breadcrumbItems = getBreadcrumbItems();

  // Don't show breadcrumb on login/register pages
  if (location.pathname === '/login' || location.pathname === '/register') {
    return null;
  }

  return (
    <nav className="bg-gradient-to-r from-gray-50 to-white border-b border-gray-200/60 px-4 py-4 mb-6 backdrop-blur-sm" dir="rtl">
      <div className="container mx-auto">
        <ol className="flex items-center space-x-2 space-x-reverse text-sm">
          <li>
            <Link
              to="/"
              className="text-gray-600 hover:text-blue-600 flex items-center space-x-reverse space-x-2 font-['Almarai'] font-medium transition-all duration-200 hover:bg-blue-50 px-3 py-2 rounded-lg group"
            >
              <svg className="w-4 h-4 group-hover:text-blue-500 transition-colors duration-200" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
              </svg>
              <span>الرئيسية</span>
            </Link>
          </li>
          
          {breadcrumbItems.map((item, index) => (
            <li key={item.path} className="flex items-center">
              <svg className="w-4 h-4 text-gray-400 mx-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
              
              {item.isActive ? (
                <span className="text-gray-800 font-bold font-['Almarai'] flex items-center space-x-reverse space-x-2 bg-gray-100 px-3 py-2 rounded-lg">
                  {item.icon && <span className="text-gray-600">{item.icon}</span>}
                  <span>{item.label}</span>
                </span>
              ) : (
                <Link
                  to={item.path}
                  className="text-blue-600 hover:text-blue-800 font-medium font-['Almarai'] flex items-center space-x-reverse space-x-2 transition-all duration-200 hover:bg-blue-50 px-3 py-2 rounded-lg group"
                >
                  {item.icon && <span className="text-blue-500 group-hover:text-blue-600 transition-colors duration-200">{item.icon}</span>}
                  <span>{item.label}</span>
                </Link>
              )}
            </li>
          ))}
        </ol>
      </div>
    </nav>
  );
};

export default Breadcrumb;
