const { query } = require('./src/models/database');

async function simpleDebug() {
  try {
    console.log('Checking users...');
    const result = await query('SELECT email, role FROM users');
    console.log('Users:', result.rows);
    
    console.log('\nChecking admin user...');
    const admin = await query('SELECT email, role FROM users WHERE email = $1', ['<EMAIL>']);
    console.log('Admin user:', admin.rows);
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

simpleDebug();
