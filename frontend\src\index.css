@tailwind base;
@tailwind components;
@tailwind utilities;

/* Arabic RTL Styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  direction: rtl;
  text-align: right;
  font-family: 'Almarai', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
  overflow-x: hidden;
}

html {
  overflow-x: hidden;
}

code {
  font-family: 'Almarai', source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* RTL Layout Adjustments */
.sidebar {
  right: 0;
  left: auto;
}

.navbar {
  direction: rtl;
}

.navbar-brand {
  margin-right: 0;
  margin-left: auto;
}

/* Form Elements RTL */
input, select, textarea {
  text-align: right;
  direction: rtl;
}

input[type="email"] {
  direction: ltr;
  text-align: left;
}

/* Button Styles */
.btn {
  font-family: 'Almarai', sans-serif;
  border-radius: 4px;
  padding: 10px 20px;
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

/* Table RTL */
.table {
  direction: rtl;
}

.table th:first-child {
  border-top-right-radius: 4px;
  border-top-left-radius: 0;
}

.table th:last-child {
  border-top-left-radius: 4px;
  border-top-right-radius: 0;
}

/* Modal RTL */
.modal-header {
  direction: rtl;
}

.modal-header .close {
  margin-left: 0;
  margin-right: auto;
}

/* Upload Area */
.upload-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.upload-area {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  background-color: #fafafa;
}

/* Enhanced RTL Support */
.rtl-flex {
  display: flex;
  flex-direction: row-reverse;
}

.rtl-space-x > * + * {
  margin-right: 0.5rem;
  margin-left: 0;
}

.rtl-space-x-2 > * + * {
  margin-right: 0.5rem;
  margin-left: 0;
}

.rtl-space-x-4 > * + * {
  margin-right: 1rem;
  margin-left: 0;
}

/* Arabic Text Improvements */
.arabic-text {
  font-family: 'Almarai', sans-serif;
  line-height: 1.6;
  text-align: right;
  direction: rtl;
}

/* Form Improvements for Arabic */
.form-input-rtl {
  text-align: right;
  direction: rtl;
  padding-right: 12px;
  padding-left: 12px;
}

.form-input-ltr {
  text-align: left;
  direction: ltr;
  padding-left: 12px;
  padding-right: 12px;
}

/* Button Improvements */
.btn-rtl {
  font-family: 'Almarai', sans-serif;
  font-weight: 500;
}

/* Table RTL Improvements */
.table-rtl {
  direction: rtl;
}

.table-rtl th {
  text-align: right;
  padding-right: 1.5rem;
  padding-left: 1.5rem;
}

.table-rtl td {
  text-align: right;
  padding-right: 1.5rem;
  padding-left: 1.5rem;
}

/* Card and Container RTL */
.card-rtl {
  direction: rtl;
  text-align: right;
}

/* Navigation RTL */
.nav-rtl {
  direction: rtl;
}

.nav-rtl .nav-item {
  margin-left: 1rem;
  margin-right: 0;
}

/* Loading Spinner RTL */
.spinner-rtl {
  margin-left: 0.5rem;
  margin-right: 0;
}

/* Pagination RTL */
.pagination-rtl {
  direction: rtl;
}

.pagination-rtl .page-item {
  margin-left: 0.25rem;
  margin-right: 0;
}

/* Dropdown RTL */
.dropdown-rtl {
  text-align: right;
  direction: rtl;
}

/* Alert and Message RTL */
.alert-rtl {
  text-align: right;
  direction: rtl;
}

/* File Upload RTL */
.file-upload-rtl {
  text-align: center;
  direction: rtl;
}

/* Responsive RTL Adjustments */
@media (max-width: 768px) {
  .mobile-rtl {
    direction: rtl;
    text-align: right;
  }

  .mobile-rtl .flex {
    flex-direction: column;
  }

  .mobile-rtl .space-x-4 > * + * {
    margin-right: 0;
    margin-top: 1rem;
  }
}

/* Print Styles for Arabic */
@media print {
  body {
    font-family: 'Almarai', sans-serif;
    direction: rtl;
    text-align: right;
  }
}

.upload-label {
  display: inline-block;
  padding: 12px 24px;
  background-color: #007bff;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 20px;
}

.upload-input {
  display: none;
}

.error-message {
  color: #dc3545;
  margin-top: 10px;
  padding: 10px;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
}

/* Professional Header Styles */
.navbar-logo {
  transition: transform 0.2s ease-in-out;
}

.navbar-logo:hover {
  transform: scale(1.05);
}

/* Enhanced Mobile Navigation */
.mobile-menu-overlay {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(2px);
}

/* Professional Button Styles */
.btn-professional {
  font-family: 'Almarai', sans-serif;
  font-weight: 500;
  border-radius: 8px;
  padding: 10px 20px;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn-professional:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Enhanced Navigation Links */
.nav-link-professional {
  position: relative;
  font-family: 'Almarai', sans-serif;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

.nav-link-professional::after {
  content: '';
  position: absolute;
  bottom: -4px;
  right: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #3B82F6, #1D4ED8);
  transition: width 0.3s ease-in-out;
}

.nav-link-professional:hover::after {
  width: 100%;
}

/* User Avatar Styles */
.user-avatar {
  background: linear-gradient(135deg, #3B82F6, #1D4ED8);
  color: white;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Mobile Menu Animation */
.mobile-menu-enter {
  opacity: 0;
  transform: translateY(-10px);
}

.mobile-menu-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.2s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  .upload-area {
    padding: 20px;
  }

  /* Mobile Header Adjustments */
  .navbar-brand-mobile {
    font-size: 1rem;
    line-height: 1.2;
  }

  .navbar-logo-mobile {
    height: 2rem;
    width: 2rem;
  }
}

/* Large Screen Optimizations */
@media (min-width: 1280px) {
  .navbar-container-xl {
    max-width: 1280px;
  }
}

/* High DPI Display Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .navbar-logo {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Dashboard Animations and Effects */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slideInRight {
  animation: slideInRight 0.5s ease-out;
}

/* Enhanced hover effects for dashboard */
.hover-lift {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Dashboard card gradients */
.dashboard-card-gradient {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 1) 100%);
  backdrop-filter: blur(10px);
}

/* Pulse animation for loading states */
@keyframes pulse-subtle {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-pulse-subtle {
  animation: pulse-subtle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Enhanced responsive design for dashboard */
@media (max-width: 640px) {
  .dashboard-mobile-spacing {
    padding: 1rem;
  }

  .dashboard-card-mobile {
    padding: 1rem;
  }

  .dashboard-grid-mobile {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Prevent horizontal overflow and ensure content fits */
* {
  max-width: 100%;
  box-sizing: border-box;
}

.container, .max-w-7xl, .max-w-6xl, .max-w-5xl, .max-w-4xl, .max-w-3xl, .max-w-2xl, .max-w-xl {
  overflow-x: hidden;
  width: 100%;
}

/* Ensure text doesn't get cut off */
.text-truncate-safe {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* Responsive grid improvements */
.dashboard-grid {
  display: grid;
  gap: 1rem;
  width: 100%;
}

@media (min-width: 640px) {
  .dashboard-grid {
    gap: 1.5rem;
  }
}

/* Ensure cards maintain minimum height for content */
.dashboard-card {
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.dashboard-stat-card {
  min-height: 160px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Responsive typography */
.responsive-text-xs {
  font-size: 0.75rem;
  line-height: 1.2;
}

.responsive-text-sm {
  font-size: 0.875rem;
  line-height: 1.3;
}

.responsive-text-base {
  font-size: 1rem;
  line-height: 1.4;
}

.responsive-text-lg {
  font-size: 1.125rem;
  line-height: 1.4;
}

@media (min-width: 640px) {
  .responsive-text-xs {
    font-size: 0.875rem;
  }

  .responsive-text-sm {
    font-size: 1rem;
  }

  .responsive-text-base {
    font-size: 1.125rem;
  }

  .responsive-text-lg {
    font-size: 1.25rem;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .dashboard-tablet-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1025px) {
  .dashboard-desktop-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Word wrapping and text overflow handling */
.word-wrap {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.text-safe {
  word-break: break-word;
  overflow-wrap: anywhere;
}

/* Ensure flex items don't shrink too much */
.flex-safe {
  min-width: 0;
  flex-shrink: 1;
}

/* Grid improvements for better content distribution */
.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

/* Header and navigation fixes */
.navbar-container {
  max-width: 100vw;
  overflow-x: hidden;
}

.dashboard-header {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

/* Ensure dashboard switcher doesn't interfere */
.dashboard-switcher {
  position: relative;
  z-index: 10;
}

/* Debug and layout fixes */
.dashboard-container {
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  position: relative;
}

/* Ensure proper spacing after navbar */
.main-content {
  padding-top: 0;
  margin-top: 0;
}

/* Fix any potential layout issues */
.layout-fix {
  box-sizing: border-box;
  width: 100%;
  max-width: 100%;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .animate-shimmer,
  .animate-fadeInUp,
  .animate-slideInRight,
  .hover-lift {
    animation: none;
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .dashboard-card-gradient {
    background: white;
    border: 2px solid #000;
  }

  .animate-shimmer {
    background: #f0f0f0;
  }
}

/* Enhanced Navbar Animations */
@keyframes navSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.nav-slide-in {
  animation: navSlideIn 0.3s ease-out;
}

/* Mobile menu slide animation */
@keyframes mobileMenuSlide {
  from {
    opacity: 0;
    transform: translateY(-20px);
    max-height: 0;
  }
  to {
    opacity: 1;
    transform: translateY(0);
    max-height: 500px;
  }
}

.mobile-menu-slide {
  animation: mobileMenuSlide 0.4s ease-out;
}

/* Navbar backdrop blur effect */
.navbar-backdrop {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* Enhanced navigation link hover effects */
.nav-link-enhanced {
  position: relative;
  overflow: hidden;
}

.nav-link-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s ease;
}

.nav-link-enhanced:hover::before {
  left: 100%;
}

/* User avatar pulse effect */
@keyframes avatarPulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0);
  }
}

.avatar-pulse {
  animation: avatarPulse 2s infinite;
}

/* Mobile menu item stagger animation */
.mobile-menu-item {
  animation: slideInRight 0.3s ease-out;
}

.mobile-menu-item:nth-child(1) { animation-delay: 0.1s; }
.mobile-menu-item:nth-child(2) { animation-delay: 0.2s; }
.mobile-menu-item:nth-child(3) { animation-delay: 0.3s; }
.mobile-menu-item:nth-child(4) { animation-delay: 0.4s; }
.mobile-menu-item:nth-child(5) { animation-delay: 0.5s; }

/* Enhanced RTL Navigation Utilities */
.nav-group-separator {
  @apply h-6 w-px bg-gradient-to-b from-transparent via-gray-300 to-transparent;
}

.nav-dropdown-enter {
  @apply opacity-0 invisible transform translate-y-2 scale-95;
}

/* Enhanced Menu Animations */
@keyframes menuItemFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-2px);
  }
}

.menu-item-float {
  animation: menuItemFloat 3s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.gradient-shift {
  background-size: 200% 200%;
  animation: gradientShift 4s ease infinite;
}

/* Enhanced Navigation Pills */
.nav-pill {
  @apply relative overflow-hidden;
}

.nav-pill::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.nav-pill:hover::before {
  left: 100%;
}

/* Enhanced Dropdown Menu */
.dropdown-enhanced {
  @apply backdrop-blur-xl bg-white/95 border border-gray-200/30;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.dropdown-item-enhanced {
  @apply relative overflow-hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-item-enhanced:hover {
  transform: translateX(-4px);
}

.dropdown-item-enhanced::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background: linear-gradient(to bottom, #3b82f6, #8b5cf6);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.dropdown-item-enhanced:hover::after {
  transform: scaleY(1);
}

/* Enhanced User Profile Section */
.user-profile-enhanced {
  @apply relative;
}

.user-profile-enhanced::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4, #10b981);
  border-radius: 1rem;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.user-profile-enhanced:hover::before {
  opacity: 0.1;
}

/* Enhanced Logo Animation */
@keyframes logoGlow {
  0%, 100% {
    filter: drop-shadow(0 0 5px rgba(59, 130, 246, 0.3));
  }
  50% {
    filter: drop-shadow(0 0 20px rgba(59, 130, 246, 0.6));
  }
}

.logo-glow {
  animation: logoGlow 3s ease-in-out infinite;
}

.nav-dropdown-enter-active {
  @apply opacity-100 visible transform translate-y-0 scale-100 transition-all duration-300;
}

.mobile-nav-slide-enter {
  @apply transform translate-x-full;
}

.mobile-nav-slide-enter-active {
  @apply transform translate-x-0 transition-transform duration-300 ease-out;
}

/* RTL-specific spacing utilities */
.space-x-reverse-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-reverse-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-reverse-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}

/* Enhanced focus states for RTL */
.focus-ring-rtl {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-white;
}

/* Priority indicators for navigation items */
.nav-priority-high {
  @apply border-l-4 border-l-blue-500 bg-blue-50/30;
}

.nav-priority-medium {
  @apply border-l-2 border-l-gray-300;
}

.nav-priority-low {
  @apply border-l border-l-gray-200;
}

