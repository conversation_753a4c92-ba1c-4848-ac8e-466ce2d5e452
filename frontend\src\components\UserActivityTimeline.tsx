import React, { useState, useEffect } from 'react';
import { useAuth } from '../services/AuthContext';

interface ActivityItem {
  id: string;
  activity_type: string;
  activity_description: string;
  activity_data: any;
  latitude: number;
  longitude: number;
  location_accuracy: number;
  ip_address: string;
  page_url: string;
  timestamp: string;
  duration_seconds: number;
  device_type: string;
  browser_name: string;
}

interface UserInfo {
  id: string;
  email: string;
}

interface UserActivityTimelineProps {
  userId: string;
  showFilters?: boolean;
  maxHeight?: string;
}

const UserActivityTimeline: React.FC<UserActivityTimelineProps> = ({ 
  userId, 
  showFilters = true,
  maxHeight = '600px'
}) => {
  const { user } = useAuth();
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    startDate: '',
    endDate: '',
    activityType: 'all',
    limit: 100,
    offset: 0
  });
  const [pagination, setPagination] = useState({
    total: 0,
    hasMore: false
  });

  useEffect(() => {
    fetchActivityTimeline();
  }, [userId, filters]);

  const fetchActivityTimeline = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        startDate: filters.startDate,
        endDate: filters.endDate,
        activityType: filters.activityType,
        limit: filters.limit.toString(),
        offset: filters.offset.toString()
      });

      const response = await fetch(`/api/location/admin/users/${userId}/activity?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const result = await response.json();
        setActivities(result.data.activities);
        setUserInfo(result.data.user);
        setPagination({
          total: result.data.pagination.total,
          hasMore: result.data.pagination.hasMore
        });
        setError(null);
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'خطأ في جلب البيانات');
      }
    } catch (err) {
      setError('خطأ في الاتصال بالخادم');
      console.error('Error fetching activity timeline:', err);
    } finally {
      setLoading(false);
    }
  };

  const getActivityIcon = (activityType: string) => {
    switch (activityType) {
      case 'login':
        return (
          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
            </svg>
          </div>
        );
      case 'logout':
        return (
          <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
          </div>
        );
      case 'document_upload':
      case 'document_sign':
      case 'document_view':
      case 'document_download':
        return (
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
        );
      case 'location_update':
        return (
          <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>
        );
      case 'page_view':
        return (
          <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
          </div>
        );
      case 'settings_change':
      case 'password_change':
      case 'profile_update':
        return (
          <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        );
    }
  };

  const getActivityTypeText = (activityType: string) => {
    const types: { [key: string]: string } = {
      'login': 'تسجيل دخول',
      'logout': 'تسجيل خروج',
      'document_upload': 'رفع مستند',
      'document_sign': 'توقيع مستند',
      'document_view': 'عرض مستند',
      'document_download': 'تحميل مستند',
      'location_update': 'تحديث الموقع',
      'page_view': 'عرض صفحة',
      'settings_change': 'تغيير الإعدادات',
      'password_change': 'تغيير كلمة المرور',
      'profile_update': 'تحديث الملف الشخصي',
      'signature_upload': 'رفع توقيع'
    };
    return types[activityType] || activityType;
  };

  const formatDuration = (seconds: number) => {
    if (!seconds) return '';
    if (seconds < 60) return `${seconds} ثانية`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)} دقيقة`;
    return `${Math.floor(seconds / 3600)} ساعة`;
  };

  const exportTimeline = async () => {
    try {
      const params = new URLSearchParams({
        startDate: filters.startDate,
        endDate: filters.endDate,
        activityType: filters.activityType,
        format: 'csv'
      });

      const response = await fetch(`/api/location/admin/users/${userId}/activity/export?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `user-activity-${userId}-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Error exporting timeline:', error);
      alert('خطأ في تصدير البيانات');
    }
  };

  const loadMore = () => {
    setFilters(prev => ({ ...prev, offset: prev.offset + prev.limit }));
  };

  if (loading && activities.length === 0) {
    return (
      <div className="flex justify-center items-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200" dir="rtl">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 font-['Almarai']">
              سجل أنشطة المستخدم
            </h3>
            {userInfo && (
              <p className="text-sm text-gray-600 font-['Almarai']">
                {userInfo.email} - إجمالي الأنشطة: {pagination.total}
              </p>
            )}
          </div>
          <button
            onClick={exportTimeline}
            className="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-md transition-colors font-['Almarai']"
          >
            تصدير
          </button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="p-6 border-b border-gray-200 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 font-['Almarai']">
                من تاريخ
              </label>
              <input
                type="date"
                value={filters.startDate}
                onChange={(e) => setFilters(prev => ({ ...prev, startDate: e.target.value, offset: 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 font-['Almarai']"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 font-['Almarai']">
                إلى تاريخ
              </label>
              <input
                type="date"
                value={filters.endDate}
                onChange={(e) => setFilters(prev => ({ ...prev, endDate: e.target.value, offset: 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 font-['Almarai']"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 font-['Almarai']">
                نوع النشاط
              </label>
              <select
                value={filters.activityType}
                onChange={(e) => setFilters(prev => ({ ...prev, activityType: e.target.value, offset: 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 font-['Almarai']"
              >
                <option value="all">جميع الأنشطة</option>
                <option value="login">تسجيل دخول</option>
                <option value="logout">تسجيل خروج</option>
                <option value="document_upload">رفع مستند</option>
                <option value="document_sign">توقيع مستند</option>
                <option value="document_view">عرض مستند</option>
                <option value="location_update">تحديث الموقع</option>
                <option value="page_view">عرض صفحة</option>
                <option value="settings_change">تغيير الإعدادات</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 font-['Almarai']">
                عدد النتائج
              </label>
              <select
                value={filters.limit}
                onChange={(e) => setFilters(prev => ({ ...prev, limit: parseInt(e.target.value), offset: 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 font-['Almarai']"
              >
                <option value={50}>50</option>
                <option value={100}>100</option>
                <option value={200}>200</option>
                <option value={500}>500</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Timeline */}
      <div className="p-6" style={{ maxHeight, overflowY: 'auto' }}>
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <p className="text-red-800 font-['Almarai']">{error}</p>
          </div>
        )}

        {activities.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500 font-['Almarai']">لا توجد أنشطة للعرض</p>
          </div>
        ) : (
          <div className="space-y-4">
            {activities.map((activity, index) => (
              <div key={activity.id} className="flex gap-4">
                {/* Icon */}
                <div className="flex-shrink-0">
                  {getActivityIcon(activity.activity_type)}
                </div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 font-['Almarai']">
                        {getActivityTypeText(activity.activity_type)}
                      </h4>
                      <p className="text-sm text-gray-600 font-['Almarai']">
                        {activity.activity_description}
                      </p>
                    </div>
                    <div className="text-xs text-gray-500 font-['Almarai']">
                      {new Date(activity.timestamp).toLocaleString('ar-SA')}
                    </div>
                  </div>

                  {/* Additional Details */}
                  <div className="mt-2 text-xs text-gray-500 space-y-1">
                    {activity.page_url && (
                      <div className="font-['Almarai']">
                        الصفحة: {activity.page_url}
                      </div>
                    )}
                    {activity.duration_seconds > 0 && (
                      <div className="font-['Almarai']">
                        المدة: {formatDuration(activity.duration_seconds)}
                      </div>
                    )}
                    {activity.device_type && (
                      <div className="font-['Almarai']">
                        الجهاز: {activity.device_type} - {activity.browser_name}
                      </div>
                    )}
                    {activity.latitude && activity.longitude && (
                      <div className="font-['Almarai']">
                        الموقع: {activity.latitude.toFixed(6)}, {activity.longitude.toFixed(6)}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}

            {/* Load More Button */}
            {pagination.hasMore && (
              <div className="text-center pt-4">
                <button
                  onClick={loadMore}
                  disabled={loading}
                  className="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-md transition-colors font-['Almarai'] disabled:opacity-50"
                >
                  {loading ? 'جاري التحميل...' : 'تحميل المزيد'}
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default UserActivityTimeline;
