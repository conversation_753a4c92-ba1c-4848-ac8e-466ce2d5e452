import React, { useState, useEffect } from 'react';
import { documentAPI } from '../services/api';
// import { useAuth } from '../services/AuthContext';

interface ApprovedDocument {
  pending_id: number;
  original_filename: string;
  file_size: number;
  notes?: string;
  status: 'signed' | 'rejected';
  uploaded_at: string;
  signed_at?: string;
  rejected_at?: string;
  rejection_reason?: string;
  signed_document_id?: number;
  serial_number?: string;
  digital_signature?: string;
  file_path?: string;
  signer_email?: string;
  rejector_email?: string;
}

interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalDocuments: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

const MyApprovedDocuments: React.FC = () => {
  // const { user } = useAuth(); // Keep for potential future use
  const [documents, setDocuments] = useState<ApprovedDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [pagination, setPagination] = useState<PaginationInfo>({
    currentPage: 1,
    totalPages: 1,
    totalDocuments: 0,
    hasNextPage: false,
    hasPrevPage: false
  });
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [downloadingId, setDownloadingId] = useState<number | null>(null);

  const fetchDocuments = async (page: number = 1, status?: string) => {
    try {
      setLoading(true);
      setError('');
      
      const response = await documentAPI.getMyApprovedDocuments(page, 10, status);
      
      if (response.data.success) {
        setDocuments(response.data.documents);
        setPagination(response.data.pagination);
      } else {
        setError('فشل في جلب المستندات');
      }
    } catch (error: any) {
      console.error('Error fetching approved documents:', error);
      setError(error.response?.data?.error || 'فشل في جلب المستندات المعتمدة');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDocuments(1, statusFilter);
  }, [statusFilter]);

  const handleDownload = async (pendingId: number, filename: string) => {
    try {
      setDownloadingId(pendingId);
      setError('');

      const response = await documentAPI.downloadApprovedDocument(pendingId.toString());

      // Create blob and download
      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `approved_${filename}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error: any) {
      console.error('Download error:', error);
      setError(error.response?.data?.error || 'فشل في تحميل المستند');
    } finally {
      setDownloadingId(null);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 بايت';
    const k = 1024;
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status: string) => {
    if (status === 'signed') {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          ✅ معتمد
        </span>
      );
    } else if (status === 'rejected') {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
          ❌ مرفوض
        </span>
      );
    }
    return null;
  };

  return (
    <div className="max-w-6xl mx-auto p-6" dir="rtl">
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-4 sm:mb-0">
            مستنداتي المعتمدة
          </h1>
          
          <div className="flex flex-col sm:flex-row gap-4">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">جميع الحالات</option>
              <option value="signed">المعتمدة</option>
              <option value="rejected">المرفوضة</option>
            </select>
          </div>
        </div>

        {error && (
          <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        {loading ? (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="mt-2 text-gray-600">جاري التحميل...</p>
          </div>
        ) : documents.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-500 text-lg">
              📄 لا توجد مستندات معتمدة بعد
            </div>
            <p className="text-gray-400 mt-2">
              قم برفع مستندات للمراجعة من صفحة البريد
            </p>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      اسم المستند
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الحالة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      التاريخ
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الحجم
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {documents.map((doc) => (
                    <tr key={doc.pending_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {doc.original_filename}
                        </div>
                        {doc.serial_number && (
                          <div className="text-xs text-gray-500">
                            الرقم التسلسلي: {doc.serial_number}
                          </div>
                        )}
                        {doc.notes && (
                          <div className="text-xs text-gray-600 mt-1">
                            ملاحظات: {doc.notes}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(doc.status)}
                        {doc.status === 'rejected' && doc.rejection_reason && (
                          <div className="text-xs text-red-600 mt-1">
                            السبب: {doc.rejection_reason}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>
                          رفع: {formatDate(doc.uploaded_at)}
                        </div>
                        {doc.signed_at && (
                          <div className="text-green-600">
                            اعتماد: {formatDate(doc.signed_at)}
                          </div>
                        )}
                        {doc.rejected_at && (
                          <div className="text-red-600">
                            رفض: {formatDate(doc.rejected_at)}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatFileSize(doc.file_size)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        {doc.status === 'signed' && (
                          <button
                            onClick={() => handleDownload(doc.pending_id, doc.original_filename)}
                            disabled={downloadingId === doc.pending_id}
                            className="text-blue-600 hover:text-blue-900 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            {downloadingId === doc.pending_id ? (
                              <span className="flex items-center">
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 ml-2"></div>
                                جاري التحميل...
                              </span>
                            ) : (
                              '⬇️ تحميل'
                            )}
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <div className="mt-6 flex items-center justify-between">
                <div className="text-sm text-gray-700">
                  عرض {documents.length} من {pagination.totalDocuments} مستند
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => fetchDocuments(pagination.currentPage - 1, statusFilter)}
                    disabled={!pagination.hasPrevPage}
                    className="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    السابق
                  </button>
                  <span className="px-3 py-2 text-sm text-gray-700">
                    صفحة {pagination.currentPage} من {pagination.totalPages}
                  </span>
                  <button
                    onClick={() => fetchDocuments(pagination.currentPage + 1, statusFilter)}
                    disabled={!pagination.hasNextPage}
                    className="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    التالي
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default MyApprovedDocuments;
