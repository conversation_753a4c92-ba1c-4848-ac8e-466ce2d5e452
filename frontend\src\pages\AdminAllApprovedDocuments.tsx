import React, { useState, useEffect } from 'react';
import { documentAPI } from '../services/api';

interface ApprovedDocument {
  pending_id: number;
  original_filename: string;
  file_size: number;
  notes?: string;
  status: 'signed' | 'rejected';
  uploaded_at: string;
  signed_at?: string;
  rejected_at?: string;
  rejection_reason?: string;
  signed_document_id?: number;
  serial_number?: string;
  digital_signature?: string;
  file_path?: string;
  signer_email?: string;
  rejector_email?: string;
  user_email?: string; // Added for admin view to see which user owns the document
  user_id?: number;
}

interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalDocuments: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

const AdminAllApprovedDocuments: React.FC = () => {
  // const { user } = useAuth(); // Commented out as not currently used
  const [documents, setDocuments] = useState<ApprovedDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [pagination, setPagination] = useState<PaginationInfo>({
    currentPage: 1,
    totalPages: 1,
    totalDocuments: 0,
    hasNextPage: false,
    hasPrevPage: false
  });
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [userFilter, setUserFilter] = useState<string>('');
  const [downloadingId, setDownloadingId] = useState<number | null>(null);

  // Admin-specific fetch function to get all users' approved documents
  const fetchAllApprovedDocuments = async (page: number = 1, status?: string, userEmail?: string) => {
    try {
      setLoading(true);
      setError('');
      
      // This would be a new API endpoint for admins to fetch all approved documents
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10'
      });
      
      if (status) params.append('status', status);
      if (userEmail) params.append('user_email', userEmail);
      
      // Admin endpoint to fetch all approved documents
      const response = await fetch(`/api/documents/admin/all-approved?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch approved documents');
      }

      const data = await response.json();
      setDocuments(data.documents || []);
      setPagination(data.pagination || {
        currentPage: 1,
        totalPages: 1,
        totalDocuments: 0,
        hasNextPage: false,
        hasPrevPage: false
      });
    } catch (err) {
      console.error('Error fetching approved documents:', err);
      setError('فشل في تحميل المستندات المعتمدة');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAllApprovedDocuments(1, statusFilter, userFilter);
  }, [statusFilter, userFilter]);

  const handleDownload = async (documentId: number, filename: string) => {
    try {
      setDownloadingId(documentId);
      setError('');

      const response = await documentAPI.downloadSignedDocument(documentId.toString());

      // Create blob and download
      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `signed_${filename}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err: any) {
      console.error('Download error:', err);
      setError(err.response?.data?.error || 'فشل في تحميل المستند');
    } finally {
      setDownloadingId(null);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600 font-['Almarai']">جاري تحميل المستندات...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8" dir="rtl">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 font-['Almarai']">
            جميع المستندات المعتمدة
          </h1>
          <p className="mt-2 text-gray-600 font-['Almarai']">
            عرض وإدارة جميع المستندات المعتمدة لجميع المستخدمين
          </p>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 font-['Almarai'] mb-2">
                حالة المستند
              </label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-['Almarai']"
              >
                <option value="">جميع الحالات</option>
                <option value="signed">موقع</option>
                <option value="rejected">مرفوض</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 font-['Almarai'] mb-2">
                البحث بالمستخدم
              </label>
              <input
                type="text"
                value={userFilter}
                onChange={(e) => setUserFilter(e.target.value)}
                placeholder="البريد الإلكتروني للمستخدم"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-['Almarai']"
              />
            </div>
            
            <div className="flex items-end">
              <button
                onClick={() => fetchAllApprovedDocuments(1, statusFilter, userFilter)}
                className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors font-['Almarai']"
              >
                تحديث
              </button>
            </div>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <p className="text-red-800 font-['Almarai']">{error}</p>
          </div>
        )}

        {/* Documents Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900 font-['Almarai']">
              المستندات ({pagination.totalDocuments})
            </h2>
          </div>
          
          {documents.length === 0 ? (
            <div className="text-center py-12">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900 font-['Almarai']">لا توجد مستندات</h3>
              <p className="mt-1 text-sm text-gray-500 font-['Almarai']">لم يتم العثور على مستندات معتمدة</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider font-['Almarai']">
                      المستند
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider font-['Almarai']">
                      المستخدم
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider font-['Almarai']">
                      الحالة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider font-['Almarai']">
                      التاريخ
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider font-['Almarai']">
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {documents.map((doc) => (
                    <tr key={doc.pending_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                              <svg className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                            </div>
                          </div>
                          <div className="mr-4">
                            <div className="text-sm font-medium text-gray-900 font-['Almarai']">
                              {doc.original_filename}
                            </div>
                            <div className="text-sm text-gray-500 font-['Almarai']">
                              {formatFileSize(doc.file_size)}
                            </div>
                            {doc.serial_number && (
                              <div className="text-xs text-blue-600 font-['Almarai']">
                                الرقم التسلسلي: {doc.serial_number}
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 font-['Almarai']">
                          {doc.user_email || 'غير محدد'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full font-['Almarai'] ${
                          doc.status === 'signed' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {doc.status === 'signed' ? 'موقع' : 'مرفوض'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-['Almarai']">
                        {formatDate(doc.status === 'signed' ? doc.signed_at! : doc.rejected_at!)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        {doc.status === 'signed' && doc.signed_document_id && (
                          <button
                            onClick={() => handleDownload(doc.signed_document_id!, doc.original_filename)}
                            disabled={downloadingId === doc.signed_document_id}
                            className="text-blue-600 hover:text-blue-900 font-['Almarai'] disabled:opacity-50"
                          >
                            {downloadingId === doc.signed_document_id ? 'جاري التحميل...' : 'تحميل'}
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="mt-6 flex items-center justify-between">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => fetchAllApprovedDocuments(pagination.currentPage - 1, statusFilter, userFilter)}
                disabled={!pagination.hasPrevPage}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 font-['Almarai']"
              >
                السابق
              </button>
              <button
                onClick={() => fetchAllApprovedDocuments(pagination.currentPage + 1, statusFilter, userFilter)}
                disabled={!pagination.hasNextPage}
                className="mr-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 font-['Almarai']"
              >
                التالي
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700 font-['Almarai']">
                  عرض{' '}
                  <span className="font-medium">{((pagination.currentPage - 1) * 10) + 1}</span>
                  {' '}إلى{' '}
                  <span className="font-medium">
                    {Math.min(pagination.currentPage * 10, pagination.totalDocuments)}
                  </span>
                  {' '}من{' '}
                  <span className="font-medium">{pagination.totalDocuments}</span>
                  {' '}نتيجة
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <button
                    onClick={() => fetchAllApprovedDocuments(pagination.currentPage - 1, statusFilter, userFilter)}
                    disabled={!pagination.hasPrevPage}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    <span className="sr-only">السابق</span>
                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </button>
                  
                  {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((page) => (
                    <button
                      key={page}
                      onClick={() => fetchAllApprovedDocuments(page, statusFilter, userFilter)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium font-['Almarai'] ${
                        page === pagination.currentPage
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      {page}
                    </button>
                  ))}
                  
                  <button
                    onClick={() => fetchAllApprovedDocuments(pagination.currentPage + 1, statusFilter, userFilter)}
                    disabled={!pagination.hasNextPage}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    <span className="sr-only">التالي</span>
                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminAllApprovedDocuments;
