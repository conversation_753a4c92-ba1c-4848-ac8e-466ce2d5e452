const bcrypt = require('bcryptjs');
const { query } = require('../models/database');

/**
 * Password History Service
 * Manages password history and prevents password reuse
 */

class PasswordHistoryService {
  constructor() {
    this.historyLimit = 5; // Number of previous passwords to remember
    this.minPasswordAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
  }

  /**
   * Check if a password has been used recently
   */
  async checkPasswordReuse(userId, newPassword) {
    try {
      // Get user's current password and recent password history
      const userResult = await query(
        'SELECT password_hash FROM users WHERE id = $1',
        [userId]
      );

      if (userResult.rows.length === 0) {
        throw new Error('المستخدم غير موجود');
      }

      const currentPasswordHash = userResult.rows[0].password_hash;

      // Check against current password
      const isCurrentPassword = await bcrypt.compare(newPassword, currentPasswordHash);
      if (isCurrentPassword) {
        return {
          isReused: true,
          message: 'لا يمكن استخدام كلمة المرور الحالية'
        };
      }

      // Get password history
      const historyResult = await query(
        `SELECT password_hash, created_at 
         FROM password_history 
         WHERE user_id = $1 
         ORDER BY created_at DESC 
         LIMIT $2`,
        [userId, this.historyLimit]
      );

      // Check against historical passwords
      for (const historyEntry of historyResult.rows) {
        const isReusedPassword = await bcrypt.compare(newPassword, historyEntry.password_hash);
        if (isReusedPassword) {
          const daysSinceUsed = Math.floor(
            (new Date() - new Date(historyEntry.created_at)) / (1000 * 60 * 60 * 24)
          );
          
          return {
            isReused: true,
            message: `تم استخدام كلمة المرور هذه من قبل (منذ ${daysSinceUsed} يوم). يرجى اختيار كلمة مرور جديدة`
          };
        }
      }

      return {
        isReused: false,
        message: 'كلمة المرور جديدة ومقبولة'
      };

    } catch (error) {
      console.error('Error checking password reuse:', error);
      throw new Error('فشل في التحقق من تاريخ كلمات المرور');
    }
  }

  /**
   * Check if enough time has passed since last password change
   */
  async checkPasswordAge(userId) {
    try {
      const result = await query(
        'SELECT password_changed_at FROM users WHERE id = $1',
        [userId]
      );

      if (result.rows.length === 0) {
        throw new Error('المستخدم غير موجود');
      }

      const lastChange = new Date(result.rows[0].password_changed_at);
      const now = new Date();
      const timeSinceChange = now - lastChange;

      const canChange = timeSinceChange >= this.minPasswordAge;
      const hoursRemaining = canChange ? 0 : Math.ceil((this.minPasswordAge - timeSinceChange) / (1000 * 60 * 60));

      return {
        canChange,
        hoursRemaining,
        lastChange,
        message: canChange 
          ? 'يمكن تغيير كلمة المرور'
          : `يجب الانتظار ${hoursRemaining} ساعة قبل تغيير كلمة المرور مرة أخرى`
      };

    } catch (error) {
      console.error('Error checking password age:', error);
      throw new Error('فشل في التحقق من عمر كلمة المرور');
    }
  }

  /**
   * Get password history information for a user
   */
  async getPasswordHistoryInfo(userId) {
    try {
      const result = await query(
        'SELECT * FROM get_password_history_info($1)',
        [userId]
      );

      if (result.rows.length === 0) {
        return {
          totalChanges: 0,
          lastChange: null,
          historyCount: 0
        };
      }

      const info = result.rows[0];
      return {
        totalChanges: info.total_changes || 0,
        lastChange: info.last_change,
        historyCount: info.history_count || 0,
        historyLimit: this.historyLimit,
        minPasswordAge: this.minPasswordAge
      };

    } catch (error) {
      console.error('Error getting password history info:', error);
      throw new Error('فشل في الحصول على معلومات تاريخ كلمات المرور');
    }
  }

  /**
   * Validate password change request
   */
  async validatePasswordChange(userId, currentPassword, newPassword) {
    try {
      // 1. Verify current password
      const userResult = await query(
        'SELECT password_hash FROM users WHERE id = $1',
        [userId]
      );

      if (userResult.rows.length === 0) {
        throw new Error('المستخدم غير موجود');
      }

      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, userResult.rows[0].password_hash);
      if (!isCurrentPasswordValid) {
        return {
          isValid: false,
          message: 'كلمة المرور الحالية غير صحيحة'
        };
      }

      // 2. Check password age
      const ageCheck = await this.checkPasswordAge(userId);
      if (!ageCheck.canChange) {
        return {
          isValid: false,
          message: ageCheck.message
        };
      }

      // 3. Check password reuse
      const reuseCheck = await this.checkPasswordReuse(userId, newPassword);
      if (reuseCheck.isReused) {
        return {
          isValid: false,
          message: reuseCheck.message
        };
      }

      return {
        isValid: true,
        message: 'يمكن تغيير كلمة المرور'
      };

    } catch (error) {
      console.error('Error validating password change:', error);
      throw new Error('فشل في التحقق من صحة تغيير كلمة المرور');
    }
  }

  /**
   * Clean up old password history entries
   */
  async cleanupOldHistory(daysToKeep = 365) {
    try {
      const result = await query(
        'SELECT cleanup_old_password_history($1)',
        [daysToKeep]
      );

      const deletedCount = result.rows[0].cleanup_old_password_history;
      
      console.log(`Password history cleanup completed: ${deletedCount} entries removed`);
      
      return {
        deletedCount,
        message: `تم حذف ${deletedCount} إدخال من تاريخ كلمات المرور`
      };

    } catch (error) {
      console.error('Error cleaning up password history:', error);
      throw new Error('فشل في تنظيف تاريخ كلمات المرور');
    }
  }

  /**
   * Get password strength recommendations based on history
   */
  async getPasswordRecommendations(userId) {
    try {
      const historyInfo = await this.getPasswordHistoryInfo(userId);
      
      const recommendations = [
        'استخدم مزيجاً من الأحرف الكبيرة والصغيرة',
        'أضف أرقاماً ورموزاً خاصة',
        'اجعل كلمة المرور 12 حرفاً على الأقل',
        'تجنب استخدام معلومات شخصية',
        'لا تستخدم كلمات مرور سابقة'
      ];

      if (historyInfo.totalChanges > 0) {
        recommendations.push(`تم تغيير كلمة المرور ${historyInfo.totalChanges} مرة`);
        
        if (historyInfo.lastChange) {
          const daysSinceChange = Math.floor(
            (new Date() - new Date(historyInfo.lastChange)) / (1000 * 60 * 60 * 24)
          );
          recommendations.push(`آخر تغيير كان منذ ${daysSinceChange} يوم`);
        }
      }

      return {
        recommendations,
        historyInfo,
        securityTips: [
          'غيّر كلمة المرور بانتظام',
          'لا تشارك كلمة المرور مع أحد',
          'استخدم مدير كلمات مرور موثوق',
          'فعّل المصادقة الثنائية إذا كانت متاحة'
        ]
      };

    } catch (error) {
      console.error('Error getting password recommendations:', error);
      throw new Error('فشل في الحصول على توصيات كلمة المرور');
    }
  }

  /**
   * Set password history limit
   */
  setHistoryLimit(limit) {
    if (limit < 1 || limit > 20) {
      throw new Error('حد تاريخ كلمات المرور يجب أن يكون بين 1 و 20');
    }
    this.historyLimit = limit;
  }

  /**
   * Set minimum password age
   */
  setMinPasswordAge(hours) {
    if (hours < 0 || hours > 168) { // Max 1 week
      throw new Error('الحد الأدنى لعمر كلمة المرور يجب أن يكون بين 0 و 168 ساعة');
    }
    this.minPasswordAge = hours * 60 * 60 * 1000;
  }
}

module.exports = new PasswordHistoryService();
