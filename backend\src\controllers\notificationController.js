const { 
  testNotificationSystem,
  getUserNotificationPreferences 
} = require('../services/notificationService');

const {
  getNotificationStats,
  getRecentFailures,
  getNotificationHealth,
  cleanupOldLogs,
  getUserNotificationHistory,
  retryFailedNotifications
} = require('../services/notificationMonitoringService');

const { 
  WHATSAPP_CONFIG,
  validatePhoneNumber 
} = require('../services/emailNotificationService');

const { query } = require('../models/database');

/**
 * Test WhatsApp notification system
 */
const testNotification = async (req, res) => {
  try {
    const { userId } = req.user;
    const { message } = req.body;

    console.log('🧪 Testing notification system for user:', userId);

    const result = await testNotificationSystem(userId, message);

    if (result.success) {
      res.json({
        success: true,
        message: 'تم إرسال رسالة الاختبار بنجاح',
        data: {
          totalSent: result.totalSent,
          totalFailed: result.totalFailed,
          results: result.results
        }
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'فشل في إرسال رسالة الاختبار',
        reason: result.reason,
        error: result.error
      });
    }

  } catch (error) {
    console.error('Test notification error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في اختبار النظام',
      error: error.message
    });
  }
};

/**
 * Get notification configuration and status
 */
const getNotificationConfig = async (req, res) => {
  try {
    const { userId } = req.user;

    // Get user preferences
    const userPrefs = await getUserNotificationPreferences(userId);
    
    // Get system health
    const health = await getNotificationHealth();
    
    // Get user's notification history (last 10)
    const history = await getUserNotificationHistory(userId, 10);

    res.json({
      success: true,
      data: {
        systemConfig: {
          enabled: WHATSAPP_CONFIG.enabled,
          fromNumber: WHATSAPP_CONFIG.fromNumber,
          adminNumbers: WHATSAPP_CONFIG.adminNumbers.length,
          retryAttempts: WHATSAPP_CONFIG.retryAttempts,
          retryDelay: WHATSAPP_CONFIG.retryDelay
        },
        userPreferences: userPrefs,
        systemHealth: health,
        recentHistory: history
      }
    });

  } catch (error) {
    console.error('Get notification config error:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في تحميل إعدادات الإشعارات',
      error: error.message
    });
  }
};

/**
 * Get notification statistics (admin only)
 */
const getNotificationStatistics = async (req, res) => {
  try {
    const { timeframe = '24h' } = req.query;

    const stats = await getNotificationStats(timeframe);
    const health = await getNotificationHealth();
    const recentFailures = await getRecentFailures(20);

    res.json({
      success: true,
      data: {
        statistics: stats,
        health: health,
        recentFailures: recentFailures
      }
    });

  } catch (error) {
    console.error('Get notification statistics error:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في تحميل إحصائيات الإشعارات',
      error: error.message
    });
  }
};

/**
 * Retry failed notifications (admin only)
 */
const retryFailedNotificationsEndpoint = async (req, res) => {
  try {
    const { maxAge = '1h', maxRetries = 3 } = req.body;

    console.log('🔄 Admin initiated retry of failed notifications');

    const result = await retryFailedNotifications(maxAge, maxRetries);

    if (result.success) {
      res.json({
        success: true,
        message: 'تم إعادة المحاولة بنجاح',
        data: result
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'فشل في إعادة المحاولة',
        error: result.error
      });
    }

  } catch (error) {
    console.error('Retry failed notifications error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إعادة المحاولة',
      error: error.message
    });
  }
};

/**
 * Cleanup old notification logs (admin only)
 */
const cleanupNotificationLogs = async (req, res) => {
  try {
    const { retentionDays = 90 } = req.body;

    console.log(`🧹 Admin initiated cleanup of notification logs older than ${retentionDays} days`);

    const result = await cleanupOldLogs(retentionDays);

    if (result.success) {
      res.json({
        success: true,
        message: `تم حذف ${result.deletedCount} سجل قديم بنجاح`,
        data: result
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'فشل في تنظيف السجلات',
        error: result.error
      });
    }

  } catch (error) {
    console.error('Cleanup notification logs error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تنظيف السجلات',
      error: error.message
    });
  }
};

/**
 * Validate phone number format
 */
const validatePhoneNumberEndpoint = async (req, res) => {
  try {
    const { phoneNumber } = req.body;

    if (!phoneNumber) {
      return res.status(400).json({
        success: false,
        message: 'رقم الهاتف مطلوب'
      });
    }

    const isValid = validatePhoneNumber(phoneNumber);

    res.json({
      success: true,
      data: {
        phoneNumber,
        isValid,
        message: isValid ? 'رقم الهاتف صالح' : 'رقم الهاتف غير صالح - يجب أن يكون بالتنسيق الدولي (+966501234567)'
      }
    });

  } catch (error) {
    console.error('Validate phone number error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في التحقق من رقم الهاتف',
      error: error.message
    });
  }
};

/**
 * Get user notification history
 */
const getUserNotificationHistoryEndpoint = async (req, res) => {
  try {
    const { userId } = req.user;
    const { limit = 20 } = req.query;

    const history = await getUserNotificationHistory(userId, parseInt(limit));

    res.json({
      success: true,
      data: {
        history,
        count: history.length
      }
    });

  } catch (error) {
    console.error('Get user notification history error:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في تحميل تاريخ الإشعارات',
      error: error.message
    });
  }
};

/**
 * Health check endpoint for notifications
 */
const notificationHealthCheck = async (req, res) => {
  try {
    const health = await getNotificationHealth();
    
    const statusCode = health.status === 'healthy' ? 200 : 
                      health.status === 'warning' ? 200 : 503;

    res.status(statusCode).json({
      success: health.isHealthy,
      status: health.status,
      data: health
    });

  } catch (error) {
    console.error('Notification health check error:', error);
    res.status(503).json({
      success: false,
      status: 'error',
      message: 'فشل في فحص حالة النظام',
      error: error.message
    });
  }
};

module.exports = {
  testNotification,
  getNotificationConfig,
  getNotificationStatistics,
  retryFailedNotificationsEndpoint,
  cleanupNotificationLogs,
  validatePhoneNumberEndpoint,
  getUserNotificationHistoryEndpoint,
  notificationHealthCheck
};
