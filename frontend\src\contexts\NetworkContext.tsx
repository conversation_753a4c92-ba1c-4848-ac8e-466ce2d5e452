import React, { createContext, useContext, ReactNode } from 'react';
import { useNetworkRecovery, UseNetworkRecoveryOptions } from '../hooks/useNetworkRecovery';

interface NetworkContextType {
  isOnline: boolean;
  isConnecting: boolean;
  connectionQuality: 'good' | 'poor' | 'offline';
  retryCount: number;
  lastConnected: Date | null;
  manualRetry: () => Promise<boolean>;
}

const NetworkContext = createContext<NetworkContextType | undefined>(undefined);

export const useNetworkStatus = () => {
  const context = useContext(NetworkContext);
  if (context === undefined) {
    throw new Error('useNetworkStatus must be used within a NetworkProvider');
  }
  return context;
};

interface NetworkProviderProps {
  children: ReactNode;
  options?: UseNetworkRecoveryOptions;
}

export const NetworkProvider: React.FC<NetworkProviderProps> = ({ 
  children, 
  options = {} 
}) => {
  const networkState = useNetworkRecovery({
    enableAutoRetry: true,
    retryConfig: {
      maxRetries: 3,
      baseDelay: 2000,
      maxDelay: 10000,
      backoffFactor: 1.5
    },
    ...options
  });

  return (
    <NetworkContext.Provider value={networkState}>
      {children}
    </NetworkContext.Provider>
  );
};
