const { initializeEmailService } = require('../services/emailNotificationService');
const { getNotificationHealth } = require('../services/notificationMonitoringService');
const { query } = require('../models/database');

/**
 * Initialize and test the Email notification system
 */
async function initializeNotificationSystem() {
  console.log('🚀 Initializing Email Notification System...\n');

  try {
    // 1. Check environment variables
    console.log('1️⃣ Checking environment configuration...');

    const requiredEnvVars = [
      'EMAIL_HOST',
      'EMAIL_USER',
      'EMAIL_PASS'
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

    if (missingVars.length > 0) {
      console.warn('⚠️ Missing optional email environment variables:');
      missingVars.forEach(varName => {
        console.warn(`   - ${varName}`);
      });
      console.warn('\nEmail notifications will be disabled. Add these to your .env file to enable.\n');
      console.log('✅ System will continue without email notifications');
    } else {
      console.log('✅ Email environment variables configured');
    }

    // 2. Initialize Email service
    console.log('\n2️⃣ Initializing Email service...');

    const emailService = initializeEmailService();

    if (!emailService && missingVars.length === 0) {
      console.error('❌ Failed to initialize Email service');
      console.error('   Check your EMAIL_HOST, EMAIL_USER, and EMAIL_PASS\n');
      return false;
    } else if (!emailService) {
      console.log('⚠️ Email service not initialized (missing configuration)');
    } else {
      console.log('✅ Email service initialized successfully');
    }

    // 3. Test Email connection (if configured)
    if (emailService) {
      console.log('\n3️⃣ Testing Email connection...');

      try {
        await emailService.verify();
        console.log('✅ Email connection test successful');
      } catch (emailError) {
        console.error('❌ Email connection test failed:');
        console.error(`   ${emailError.message}`);
        console.warn('   Email notifications will be disabled');
      }
    }

    // 4. Check database tables
    console.log('\n4️⃣ Checking database tables...');
    
    try {
      // Check if notification_logs table exists
      const tableCheck = await query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_name = 'notification_logs'
        );
      `);

      if (!tableCheck.rows[0].exists) {
        console.error('❌ notification_logs table not found');
        console.error('   Run database setup: npm run setup-db\n');
        return false;
      }

      // Check if users table has notification columns
      const columnCheck = await query(`
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'users'
        AND column_name IN ('email', 'email_notifications_enabled', 'notification_preferences');
      `);

      const expectedColumns = ['email', 'email_notifications_enabled', 'notification_preferences'];
      const existingColumns = columnCheck.rows.map(row => row.column_name);
      const missingColumns = expectedColumns.filter(col => !existingColumns.includes(col));

      if (missingColumns.length > 0) {
        console.warn('⚠️ Missing user table columns (will be created automatically):');
        missingColumns.forEach(col => console.warn(`   - ${col}`));
        console.log('✅ System will continue and create missing columns as needed');
      }

      console.log('✅ Database tables configured correctly');

    } catch (dbError) {
      console.error('❌ Database check failed:');
      console.error(`   ${dbError.message}\n`);
      return false;
    }

    // 5. Check notification health
    console.log('\n5️⃣ Checking notification system health...');
    
    const health = await getNotificationHealth();
    
    console.log(`✅ Notification system status: ${health.status.toUpperCase()}`);
    
    if (health.status !== 'healthy' && health.status !== 'warning') {
      console.warn('⚠️  System health check shows issues - this is normal for new installations');
    }

    // 6. Display configuration summary
    console.log('\n📋 Configuration Summary:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log(`Email Notifications: ${process.env.EMAIL_NOTIFICATIONS_ENABLED !== 'false' ? 'Enabled' : 'Disabled'}`);
    console.log(`Email Host: ${process.env.EMAIL_HOST || 'Not configured'}`);
    console.log(`From Email: ${process.env.EMAIL_FROM || process.env.EMAIL_USER || 'Not configured'}`);
    console.log(`Admin Emails: ${process.env.ADMIN_NOTIFICATION_EMAILS || 'None configured'}`);
    console.log(`Retry Attempts: ${process.env.EMAIL_RETRY_ATTEMPTS || '3'}`);
    console.log(`Retry Delay: ${process.env.EMAIL_RETRY_DELAY || '5000'}ms`);
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

    // 7. Next steps
    console.log('\n🎉 Email Notification System initialized successfully!\n');
    console.log('📝 Next Steps:');
    console.log('1. User emails are automatically used from their accounts');
    console.log('2. Test notifications via /api/notifications/test');
    console.log('3. Sign a document to trigger automatic notifications');
    console.log('4. Monitor system health via /api/notifications/health');
    console.log('5. View statistics via /api/notifications/admin/stats\n');

    console.log('📚 Email notifications work out of the box with user account emails\n');

    return true;

  } catch (error) {
    console.error('❌ Initialization failed:');
    console.error(`   ${error.message}\n`);
    return false;
  }
}

// Run initialization if this script is executed directly
if (require.main === module) {
  require('dotenv').config();
  
  initializeNotificationSystem()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Fatal error during initialization:', error);
      process.exit(1);
    });
}

module.exports = { initializeNotificationSystem };
