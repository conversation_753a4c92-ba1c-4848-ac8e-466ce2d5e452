const WebSocket = require('ws');
const { query } = require('../models/database');
const jwt = require('jsonwebtoken');

/**
 * Location Tracking WebSocket Service
 * Handles real-time location updates and user session monitoring
 */
class LocationTrackingService {
  constructor() {
    this.wss = null;
    this.clients = new Map(); // Map of userId -> WebSocket connection
    this.adminClients = new Set(); // Set of admin WebSocket connections
    this.sessionUpdateInterval = null;
    this.cleanupInterval = null;
  }

  /**
   * Initialize WebSocket server for location tracking
   */
  initializeWebSocket(server) {
    this.wss = new WebSocket.Server({ 
      server,
      path: '/ws/location-tracking'
    });

    this.wss.on('connection', (ws, req) => {
      console.log('📍 Location tracking client connected');
      this.handleConnection(ws, req);
    });

    // Start periodic tasks
    this.startSessionUpdates();
    this.startCleanupTasks();

    console.log('📍 Location tracking WebSocket service initialized');
  }

  /**
   * Handle new WebSocket connection
   */
  async handleConnection(ws, req) {
    try {
      // Extract token from query parameters or headers
      const url = new URL(req.url, `http://${req.headers.host}`);
      const token = url.searchParams.get('token') || req.headers.authorization?.replace('Bearer ', '');

      if (!token) {
        ws.close(1008, 'Authentication required');
        return;
      }

      // Verify JWT token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const userId = decoded.userId;

      // Get user info
      const userResult = await query('SELECT id, email, role FROM users WHERE id = $1', [userId]);
      if (userResult.rows.length === 0) {
        ws.close(1008, 'Invalid user');
        return;
      }

      const user = userResult.rows[0];
      ws.userId = userId;
      ws.userRole = user.role;
      ws.userEmail = user.email;

      // Store connection
      if (user.role === 'admin') {
        this.adminClients.add(ws);
      } else {
        this.clients.set(userId, ws);
      }

      // Send welcome message
      ws.send(JSON.stringify({
        type: 'connected',
        message: 'متصل بخدمة تتبع المواقع',
        userId: userId,
        role: user.role,
        timestamp: new Date().toISOString()
      }));

      // Handle messages from client
      ws.on('message', (message) => {
        this.handleClientMessage(ws, message);
      });

      // Handle client disconnect
      ws.on('close', () => {
        this.handleDisconnection(ws);
      });

      ws.on('error', (error) => {
        console.error('Location tracking WebSocket error:', error);
        this.handleDisconnection(ws);
      });

      console.log(`📍 User ${user.email} (${user.role}) connected to location tracking`);

    } catch (error) {
      console.error('Error handling location tracking connection:', error);
      ws.close(1011, 'Server error');
    }
  }

  /**
   * Handle messages from clients
   */
  async handleClientMessage(ws, message) {
    try {
      const data = JSON.parse(message);

      switch (data.type) {
        case 'location_update':
          await this.handleLocationUpdate(ws, data);
          break;
        case 'ping':
          ws.send(JSON.stringify({ type: 'pong', timestamp: new Date().toISOString() }));
          break;
        case 'request_user_locations':
          if (ws.userRole === 'admin') {
            await this.sendUserLocationsToAdmin(ws);
          }
          break;
        case 'subscribe_user_updates':
          if (ws.userRole === 'admin' && data.userId) {
            ws.subscribedUserId = data.userId;
          }
          break;
        default:
          console.log('Unknown message type:', data.type);
      }
    } catch (error) {
      console.error('Error handling client message:', error);
      ws.send(JSON.stringify({
        type: 'error',
        message: 'خطأ في معالجة الرسالة',
        timestamp: new Date().toISOString()
      }));
    }
  }

  /**
   * Handle location update from client
   */
  async handleLocationUpdate(ws, data) {
    try {
      const { latitude, longitude, accuracy, source = 'gps' } = data;

      if (!latitude || !longitude) {
        ws.send(JSON.stringify({
          type: 'error',
          message: 'الإحداثيات مطلوبة'
        }));
        return;
      }

      // Update session in database
      const updated = await query(
        'SELECT update_user_session_activity($1, $2, $3, $4, $5)',
        [ws.sessionToken || 'websocket', latitude, longitude, accuracy, source]
      );

      if (updated.rows[0].update_user_session_activity) {
        // Broadcast to admin clients
        this.broadcastToAdmins({
          type: 'user_location_update',
          userId: ws.userId,
          userEmail: ws.userEmail,
          latitude,
          longitude,
          accuracy,
          source,
          timestamp: new Date().toISOString()
        });

        // Send confirmation to client
        ws.send(JSON.stringify({
          type: 'location_updated',
          message: 'تم تحديث الموقع بنجاح',
          timestamp: new Date().toISOString()
        }));
      }

    } catch (error) {
      console.error('Error handling location update:', error);
      ws.send(JSON.stringify({
        type: 'error',
        message: 'خطأ في تحديث الموقع'
      }));
    }
  }

  /**
   * Send current user locations to admin
   */
  async sendUserLocationsToAdmin(ws) {
    try {
      const result = await query(`
        SELECT 
          us.user_id,
          u.email,
          u.full_name,
          us.status,
          us.current_latitude,
          us.current_longitude,
          us.current_accuracy,
          us.location_source,
          us.last_activity,
          us.country_name,
          us.city_name
        FROM user_sessions us
        JOIN users u ON us.user_id = u.id
        WHERE us.status IN ('active', 'idle')
        ORDER BY us.last_activity DESC
      `);

      ws.send(JSON.stringify({
        type: 'user_locations',
        data: result.rows,
        timestamp: new Date().toISOString()
      }));

    } catch (error) {
      console.error('Error sending user locations to admin:', error);
      ws.send(JSON.stringify({
        type: 'error',
        message: 'خطأ في جلب مواقع المستخدمين'
      }));
    }
  }

  /**
   * Broadcast message to all admin clients
   */
  broadcastToAdmins(message) {
    this.adminClients.forEach(adminWs => {
      if (adminWs.readyState === WebSocket.OPEN) {
        adminWs.send(JSON.stringify(message));
      }
    });
  }

  /**
   * Broadcast message to specific user
   */
  sendToUser(userId, message) {
    const userWs = this.clients.get(userId);
    if (userWs && userWs.readyState === WebSocket.OPEN) {
      userWs.send(JSON.stringify(message));
    }
  }

  /**
   * Handle client disconnection
   */
  async handleDisconnection(ws) {
    try {
      if (ws.userId) {
        console.log(`📍 User ${ws.userEmail} disconnected from location tracking`);
        
        if (ws.userRole === 'admin') {
          this.adminClients.delete(ws);
        } else {
          this.clients.delete(ws.userId);
          
          // Update session status to offline
          await query(
            'UPDATE user_sessions SET status = $1, logout_time = NOW() WHERE user_id = $2 AND status IN ($3, $4)',
            ['offline', ws.userId, 'active', 'idle']
          );

          // Notify admins of user going offline
          this.broadcastToAdmins({
            type: 'user_status_change',
            userId: ws.userId,
            userEmail: ws.userEmail,
            status: 'offline',
            timestamp: new Date().toISOString()
          });
        }
      }
    } catch (error) {
      console.error('Error handling disconnection:', error);
    }
  }

  /**
   * Start periodic session status updates
   */
  startSessionUpdates() {
    this.sessionUpdateInterval = setInterval(async () => {
      try {
        // Update session statuses
        await query('SELECT update_session_status()');

        // Get updated session statuses and broadcast to admins
        const result = await query(`
          SELECT user_id, status, last_activity
          FROM user_sessions 
          WHERE updated_at > NOW() - INTERVAL '2 minutes'
        `);

        if (result.rows.length > 0) {
          this.broadcastToAdmins({
            type: 'session_status_updates',
            updates: result.rows,
            timestamp: new Date().toISOString()
          });
        }

      } catch (error) {
        console.error('Error updating session statuses:', error);
      }
    }, 60000); // Every minute
  }

  /**
   * Start cleanup tasks
   */
  startCleanupTasks() {
    this.cleanupInterval = setInterval(async () => {
      try {
        // Clean up old location data
        const deletedCount = await query('SELECT cleanup_location_data()');
        if (deletedCount.rows[0].cleanup_location_data > 0) {
          console.log(`📍 Cleaned up ${deletedCount.rows[0].cleanup_location_data} old location records`);
        }

        // Remove dead WebSocket connections
        this.clients.forEach((ws, userId) => {
          if (ws.readyState !== WebSocket.OPEN) {
            this.clients.delete(userId);
          }
        });

        this.adminClients.forEach(ws => {
          if (ws.readyState !== WebSocket.OPEN) {
            this.adminClients.delete(ws);
          }
        });

      } catch (error) {
        console.error('Error in cleanup tasks:', error);
      }
    }, 300000); // Every 5 minutes
  }

  /**
   * Stop the service
   */
  stop() {
    if (this.sessionUpdateInterval) {
      clearInterval(this.sessionUpdateInterval);
    }
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    if (this.wss) {
      this.wss.close();
    }
  }
}

module.exports = new LocationTrackingService();
