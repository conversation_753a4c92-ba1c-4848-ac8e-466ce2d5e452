const express = require('express');
const {
  upload,
  uploadWithTracking,
  testPDFUpload,
  signDocument,
  getDocuments,
  getDocument,
  downloadDocument,
  viewDocument,
  verifyDocumentBySerial
} = require('../controllers/documentController');
const { authenticateToken } = require('../middleware/auth');
const { createChunkedUpload, handleChunkUpload, checkUploadStatus } = require('../middleware/chunkedUpload');
const {
  checkDocumentAccess,
  requireDocumentOwnership,
  filterUserDocuments,
  documentRateLimit
} = require('../middleware/documentAuth');

const router = express.Router();

// Public routes (no authentication required)
// Verify document by serial number (public endpoint for verification)
router.get('/verify/:serialNumber', verifyDocumentBySerial);

// All other document routes require authentication
router.use(authenticateToken);

// Test PDF upload endpoint for debugging
router.post('/test-upload', upload, (req, res) => {
  try {
    const file = req.file;

    if (!file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    // Get detailed file information
    const fileInfo = {
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      bufferLength: file.buffer ? file.buffer.length : 0,
      header: file.buffer ? file.buffer.slice(0, 50).toString('ascii') : 'No buffer',
      headerHex: file.buffer ? file.buffer.slice(0, 20).toString('hex') : 'No buffer'
    };

    console.log('Test upload file info:', fileInfo);

    res.json({
      message: 'File upload test successful',
      fileInfo
    });
  } catch (error) {
    console.error('Test upload error:', error);
    res.status(500).json({ error: 'Test upload failed', details: error.message });
  }
});

// Test PDF upload (for debugging) - authenticated with rate limiting
router.post('/test-pdf', authenticateToken, documentRateLimit(50), upload, testPDFUpload);

// Sign document with enhanced upload tracking and rate limiting
router.post('/sign', authenticateToken, documentRateLimit(20), uploadWithTracking, signDocument);

// Mail functionality routes (must be before parameterized routes)
const { requirePermission } = require('../middleware/roleAuth');
const { query } = require('../models/database');
const { generateSerialNumber } = require('../services/pdfService');

// Upload document for review
router.post('/upload-for-review', authenticateToken, upload, async (req, res) => {
  try {
    const { user } = req;
    const { notes } = req.body;

    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    if (req.file.mimetype !== 'application/pdf') {
      return res.status(400).json({ error: 'Only PDF files are allowed' });
    }

    // Save the file to disk since memoryStorage doesn't provide a path
    const fs = require('fs').promises;
    const path = require('path');
    const crypto = require('crypto');

    // Create uploads directory if it doesn't exist
    const uploadsDir = path.join(__dirname, '../../uploads/pending');
    await fs.mkdir(uploadsDir, { recursive: true });

    // Generate unique filename
    const fileExtension = path.extname(req.file.originalname);
    const uniqueFilename = `${user.userId}_${Date.now()}_${crypto.randomBytes(8).toString('hex')}${fileExtension}`;
    const filePath = path.join(uploadsDir, uniqueFilename);

    // Save file buffer to disk
    await fs.writeFile(filePath, req.file.buffer);

    // Store the document in pending_documents table
    const result = await query(
      `INSERT INTO pending_documents (
        original_filename, file_path, file_size, uploaded_by, uploader_email, notes, status
      ) VALUES ($1, $2, $3, $4, $5, $6, 'pending') RETURNING *`,
      [
        req.file.originalname,
        filePath,
        req.file.size,
        user.userId,
        user.email,
        notes || null
      ]
    );

    res.json({
      success: true,
      message: 'تم رفع المستند بنجاح للمراجعة',
      document: result.rows[0]
    });
  } catch (error) {
    console.error('Upload for review error:', error);
    res.status(500).json({ error: 'فشل في رفع المستند' });
  }
});

// Get pending documents (admin only)
router.get('/pending', authenticateToken, requirePermission('sign_documents'), async (req, res) => {
  try {
    const result = await query(
      `SELECT pd.*, u.email as uploader_email
       FROM pending_documents pd
       JOIN users u ON pd.uploaded_by = u.id
       ORDER BY pd.uploaded_at DESC`
    );

    res.json({
      success: true,
      documents: result.rows
    });
  } catch (error) {
    console.error('Get pending documents error:', error);
    res.status(500).json({ error: 'فشل في تحميل المستندات المعلقة' });
  }
});

// Sign pending document (admin only)
router.post('/:id/sign-pending', authenticateToken, requirePermission('sign_documents'), async (req, res) => {
  try {
    const { id } = req.params;
    const { user } = req;

    // Get the pending document
    const pendingResult = await query(
      'SELECT * FROM pending_documents WHERE id = $1 AND status = $2',
      [id, 'pending']
    );

    if (pendingResult.rows.length === 0) {
      return res.status(404).json({ error: 'المستند غير موجود أو تم توقيعه مسبقاً' });
    }

    const pendingDoc = pendingResult.rows[0];

    // Get admin's signature
    const signatureResult = await query(
      'SELECT * FROM signatures WHERE user_id = $1 ORDER BY upload_date DESC LIMIT 1',
      [user.userId]
    );

    if (signatureResult.rows.length === 0) {
      return res.status(400).json({ error: 'لا يوجد توقيع مرفوع للمدير' });
    }

    const signature = signatureResult.rows[0];

    // Generate serial number and digital signature
    const serialNumber = generateSerialNumber();

    // Create a simple digital signature for mail documents
    const crypto = require('crypto');
    const digitalSignature = crypto.createHash('sha256')
      .update(`${pendingDoc.original_filename}-${user.userId}-${serialNumber}-${Date.now()}`)
      .digest('hex');

    // Move to signed_documents table
    const signedResult = await query(
      `INSERT INTO signed_documents (
        original_filename, file_path, file_size, signed_date,
        user_id, user_email, serial_number, digital_signature
      ) VALUES ($1, $2, $3, NOW(), $4, $5, $6, $7) RETURNING *`,
      [
        pendingDoc.original_filename,
        pendingDoc.file_path, // Use original path for now
        pendingDoc.file_size,
        user.userId,
        user.email,
        serialNumber,
        digitalSignature
      ]
    );

    // Update pending document status
    await query(
      'UPDATE pending_documents SET status = $1, signed_at = NOW(), signed_by = $2 WHERE id = $3',
      ['signed', user.userId, id]
    );

    // Send notification to the original uploader
    try {
      // Get the original uploader's information
      const uploaderResult = await query(
        'SELECT * FROM users WHERE id = $1',
        [pendingDoc.uploaded_by]
      );

      if (uploaderResult.rows.length > 0) {
        const uploaderData = uploaderResult.rows[0];

        // Prepare document data for notification
        const documentData = {
          id: signedResult.rows[0].id,
          originalFilename: pendingDoc.original_filename,
          serialNumber: serialNumber,
          signedAt: new Date(),
          fileSize: pendingDoc.file_size
        };

        // Import notification service
        const { sendDocumentApprovedNotification } = require('../services/notificationService');

        // Send approval notification
        const notificationResult = await sendDocumentApprovedNotification(documentData, uploaderData);

        if (notificationResult.success) {
          console.log(`✅ Document approval notification sent to user ${uploaderData.email}`);
        } else {
          console.log(`⚠ Document approval notification failed: ${notificationResult.reason || notificationResult.error}`);
        }
      }
    } catch (notificationError) {
      // Log error but don't affect the main response
      console.error('Document approval notification error (non-critical):', notificationError);
    }

    res.json({
      success: true,
      message: 'تم توقيع المستند بنجاح',
      document: signedResult.rows[0]
    });
  } catch (error) {
    console.error('Sign pending document error:', error);
    res.status(500).json({ error: 'فشل في توقيع المستند' });
  }
});

// Reject pending document (admin only)
router.post('/:id/reject', authenticateToken, requirePermission('sign_documents'), async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    const { user } = req;

    const result = await query(
      'UPDATE pending_documents SET status = $1, rejection_reason = $2, rejected_at = NOW(), rejected_by = $3 WHERE id = $4 AND status = $5 RETURNING *',
      ['rejected', reason, user.userId, id, 'pending']
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'المستند غير موجود أو تم التعامل معه مسبقاً' });
    }

    const rejectedDoc = result.rows[0];

    // Send notification to the original uploader
    try {
      // Get the original uploader's information
      const uploaderResult = await query(
        'SELECT * FROM users WHERE id = $1',
        [rejectedDoc.uploaded_by]
      );

      if (uploaderResult.rows.length > 0) {
        const uploaderData = uploaderResult.rows[0];

        // Prepare document data for notification
        const documentData = {
          id: rejectedDoc.id,
          originalFilename: rejectedDoc.original_filename,
          rejectedAt: new Date(),
          fileSize: rejectedDoc.file_size
        };

        // Import notification service
        const { sendDocumentRejectedNotification } = require('../services/notificationService');

        // Send rejection notification
        const notificationResult = await sendDocumentRejectedNotification(documentData, uploaderData, reason);

        if (notificationResult.success) {
          console.log(`✅ Document rejection notification sent to user ${uploaderData.email}`);
        } else {
          console.log(`⚠ Document rejection notification failed: ${notificationResult.reason || notificationResult.error}`);
        }
      }
    } catch (notificationError) {
      // Log error but don't affect the main response
      console.error('Document rejection notification error (non-critical):', notificationError);
    }

    res.json({
      success: true,
      message: 'تم رفض المستند',
      document: rejectedDoc
    });
  } catch (error) {
    console.error('Reject document error:', error);
    res.status(500).json({ error: 'فشل في رفض المستند' });
  }
});

// Get user's approved documents from mail workflow
router.get('/my-approved', authenticateToken, async (req, res) => {
  try {
    const { userId } = req.user;
    const { page = 1, limit = 10, status } = req.query;

    const offset = (page - 1) * limit;

    let whereClause = 'WHERE pd.uploaded_by = $1';
    let queryParams = [userId];

    if (status && ['signed', 'rejected'].includes(status)) {
      whereClause += ' AND pd.status = $2';
      queryParams.push(status);
    } else {
      // Default to showing signed and rejected documents (not pending)
      whereClause += ' AND pd.status IN ($2, $3)';
      queryParams.push('signed', 'rejected');
    }

    // Get documents from pending_documents table with signed_documents info
    const result = await query(
      `SELECT
        pd.id as pending_id,
        pd.original_filename,
        pd.file_size,
        pd.notes,
        pd.status,
        pd.uploaded_at,
        pd.signed_at,
        pd.rejected_at,
        pd.rejection_reason,
        sd.id as signed_document_id,
        sd.serial_number,
        sd.digital_signature,
        sd.file_path,
        u_signer.email as signer_email,
        u_rejector.email as rejector_email
       FROM pending_documents pd
       LEFT JOIN signed_documents sd ON pd.id = sd.id AND pd.status = 'signed'
       LEFT JOIN users u_signer ON pd.signed_by = u_signer.id
       LEFT JOIN users u_rejector ON pd.rejected_by = u_rejector.id
       ${whereClause}
       ORDER BY
         CASE
           WHEN pd.signed_at IS NOT NULL THEN pd.signed_at
           WHEN pd.rejected_at IS NOT NULL THEN pd.rejected_at
           ELSE pd.uploaded_at
         END DESC
       LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}`,
      [...queryParams, limit, offset]
    );

    // Get total count
    const countResult = await query(
      `SELECT COUNT(*) as total FROM pending_documents pd ${whereClause}`,
      queryParams
    );

    const totalDocuments = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(totalDocuments / limit);

    res.json({
      success: true,
      documents: result.rows,
      pagination: {
        currentPage: parseInt(page),
        totalPages: totalPages,
        totalDocuments: totalDocuments,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    });
  } catch (error) {
    console.error('Get user approved documents error:', error);
    res.status(500).json({ error: 'فشل في جلب المستندات المعتمدة' });
  }
});

// Get all approved documents for all users (admin only)
router.get('/admin/all-approved', authenticateToken, requirePermission('view_history'), async (req, res) => {
  try {
    const { page = 1, limit = 10, status, user_email } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE pd.status IN ($1, $2)';
    let queryParams = ['signed', 'rejected'];

    if (status && ['signed', 'rejected'].includes(status)) {
      whereClause = 'WHERE pd.status = $1';
      queryParams = [status];
    }

    if (user_email) {
      whereClause += ` AND u.email ILIKE $${queryParams.length + 1}`;
      queryParams.push(`%${user_email}%`);
    }

    const result = await query(
      `SELECT pd.*, u.email as user_email, u.id as user_id,
              signer.email as signer_email, rejector.email as rejector_email
       FROM pending_documents pd
       JOIN users u ON pd.uploaded_by = u.id
       LEFT JOIN users signer ON pd.signed_by = signer.id
       LEFT JOIN users rejector ON pd.rejected_by = rejector.id
       ${whereClause}
       ORDER BY
         CASE
           WHEN pd.status = 'signed' THEN pd.signed_at
           WHEN pd.status = 'rejected' THEN pd.rejected_at
           ELSE pd.uploaded_at
         END DESC
       LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}`,
      [...queryParams, limit, offset]
    );

    // Get total count
    const countResult = await query(
      `SELECT COUNT(*) as total
       FROM pending_documents pd
       JOIN users u ON pd.uploaded_by = u.id
       ${whereClause}`,
      queryParams
    );

    const totalDocuments = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(totalDocuments / limit);

    res.json({
      success: true,
      documents: result.rows,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalDocuments,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    });
  } catch (error) {
    console.error('Get all approved documents error:', error);
    res.status(500).json({ error: 'فشل في تحميل المستندات المعتمدة' });
  }
});

// Get all documents (admin only)
router.get('/', authenticateToken, requirePermission('view_history'), getDocuments);

// Get specific document (with access control)
router.get('/:documentId', authenticateToken, checkDocumentAccess('VIEW'), getDocument);

// Download approved document from mail workflow
router.get('/approved/:pendingId/download', authenticateToken, async (req, res) => {
  try {
    const { userId } = req.user;
    const { pendingId } = req.params;

    // Get the pending document and verify ownership
    const pendingResult = await query(
      'SELECT * FROM pending_documents WHERE id = $1 AND uploaded_by = $2 AND status = $3',
      [pendingId, userId, 'signed']
    );

    if (pendingResult.rows.length === 0) {
      return res.status(404).json({ error: 'المستند غير موجود أو غير معتمد' });
    }

    const pendingDoc = pendingResult.rows[0];

    // Get the signed document info
    const signedResult = await query(
      'SELECT * FROM signed_documents WHERE original_filename = $1 AND file_size = $2',
      [pendingDoc.original_filename, pendingDoc.file_size]
    );

    if (signedResult.rows.length === 0) {
      return res.status(404).json({ error: 'الملف المعتمد غير موجود' });
    }

    const signedDoc = signedResult.rows[0];

    // Read the file
    const { readFile } = require('../services/encryptionService');
    const fileBuffer = await readFile(signedDoc.file_path);

    // Sanitize filename for download
    const safeFilename = `approved_${pendingDoc.original_filename}`.replace(/[^a-zA-Z0-9._-]/g, '_');

    // Log download activity
    const { logDocumentDownload } = require('../services/auditService');
    await logDocumentDownload(userId, signedDoc.id, {
      fileName: safeFilename,
      originalFileName: pendingDoc.original_filename,
      fileSize: signedDoc.file_size,
      downloadType: 'approved_mail_document'
    }, req);

    // Set appropriate headers
    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="${safeFilename}"`,
      'Content-Length': fileBuffer.length,
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });

    res.send(fileBuffer);
  } catch (error) {
    console.error('Download approved document error:', error);

    // Handle specific error types
    if (error.message.includes('ENOENT')) {
      return res.status(404).json({ error: 'ملف المستند غير موجود على الخادم' });
    }

    res.status(500).json({
      error: 'فشل في تحميل المستند المعتمد',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Download signed document (with access control and audit logging)
router.get('/:documentId/download', authenticateToken, checkDocumentAccess('DOWNLOAD'), downloadDocument);

// View document in browser (with access control and audit logging)
router.get('/:documentId/view', authenticateToken, checkDocumentAccess('VIEW'), viewDocument);

// Chunked upload for very large files
const chunkedUpload = createChunkedUpload({
  destination: './uploads/chunks',
  maxChunkSize: 10 * 1024 * 1024, // 10MB chunks
  allowedMimeTypes: ['application/pdf'],
  allowedExtensions: ['.pdf']
});

router.post('/sign/chunk', handleChunkUpload, chunkedUpload.single('chunk'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No chunk uploaded' });
    }

    if (req.file.isComplete) {
      // File assembly complete, proceed with signing
      // This would need to be integrated with the signDocument function
      res.json({
        success: true,
        isComplete: true,
        message: 'File uploaded and assembled successfully',
        filePath: req.file.path
      });
    } else {
      // Chunk uploaded successfully, but file not complete
      res.json({
        success: true,
        isComplete: false,
        uploadedChunks: req.file.uploadedChunks,
        totalChunks: req.file.totalChunks,
        message: `Chunk ${req.file.uploadedChunks}/${req.file.totalChunks} uploaded successfully`
      });
    }
  } catch (error) {
    console.error('Chunked upload error:', error);
    res.status(500).json({ error: 'Chunked upload failed' });
  }
});

// Check upload status
router.get('/upload-status/:fileId', checkUploadStatus);

module.exports = router;
