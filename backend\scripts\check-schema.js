const { query } = require('./src/models/database');

async function checkSchema() {
  try {
    console.log('🔍 Checking database schema...\n');
    
    // Check users table columns
    const usersColumns = await query(`
      SELECT column_name, data_type, is_nullable, column_default 
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      ORDER BY ordinal_position
    `);
    
    console.log('📋 Users table columns:');
    usersColumns.rows.forEach(row => {
      console.log(`  - ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });
    
    // Check if language column exists
    const hasLanguageColumn = usersColumns.rows.some(row => row.column_name === 'language');
    console.log(`\n🌐 Language column exists: ${hasLanguageColumn ? 'Yes' : 'No'}`);

    if (!hasLanguageColumn) {
      console.log('\n🔧 Adding missing language column...');
      await query(`
        ALTER TABLE users
        ADD COLUMN language VARCHAR(10) DEFAULT 'ar' NOT NULL
      `);
      console.log('✅ Language column added successfully');
    }

    // Check if text_direction column exists
    const hasTextDirectionColumn = usersColumns.rows.some(row => row.column_name === 'text_direction');
    console.log(`📝 Text direction column exists: ${hasTextDirectionColumn ? 'Yes' : 'No'}`);

    if (!hasTextDirectionColumn) {
      console.log('\n🔧 Adding missing text_direction column...');
      await query(`
        ALTER TABLE users
        ADD COLUMN text_direction VARCHAR(10) DEFAULT 'rtl' NOT NULL
      `);
      console.log('✅ Text direction column added successfully');
    }
    
    // Check sample user data
    const sampleUsers = await query('SELECT id, email, created_at FROM users LIMIT 3');
    console.log(`\n👥 Sample users (${sampleUsers.rows.length} found):`);
    sampleUsers.rows.forEach(user => {
      console.log(`  - ID: ${user.id}, Email: ${user.email}, Created: ${user.created_at}`);
    });
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Schema check failed:', error.message);
    process.exit(1);
  }
}

checkSchema();
