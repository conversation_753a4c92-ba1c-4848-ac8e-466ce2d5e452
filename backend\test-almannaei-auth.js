const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001/api';

async function testAlmannaeiAuth() {
  try {
    console.log('🔍 Testing <EMAIL> authentication...\n');

    // Step 1: Login with almannaei credentials
    console.log('1. Logging <NAME_EMAIL>...');
    
    // Try different passwords that might work
    const passwords = ['password123', 'admin123', 'Password123', 'admin', 'password'];
    let loginResponse = null;
    let workingPassword = null;

    for (const password of passwords) {
      try {
        console.log(`   Trying password: ${password}`);
        loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
          email: '<EMAIL>',
          password: password
        });
        workingPassword = password;
        console.log(`   ✅ Login successful with password: ${password}`);
        break;
      } catch (error) {
        console.log(`   ❌ Failed with password: ${password}`);
      }
    }

    if (!loginResponse) {
      console.log('❌ Could not login with any password. Let me check what passwords exist...');
      return;
    }

    const { token, user } = loginResponse.data;
    console.log(`✅ Login successful!`);
    console.log(`   User: ${user.email}`);
    console.log(`   Role: ${user.role}`);
    console.log(`   Token: ${token.substring(0, 20)}...`);

    // Step 2: Test getProfile endpoint
    console.log('\n2. Testing getProfile endpoint...');
    const profileResponse = await axios.get(`${API_BASE_URL}/auth/profile`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const profileUser = profileResponse.data.user;
    console.log(`✅ Profile fetch successful!`);
    console.log(`   User: ${profileUser.email}`);
    console.log(`   Role: ${profileUser.role}`);

    // Step 3: Test signatures endpoint (this is failing)
    console.log('\n3. Testing signatures endpoint...');
    try {
      const signaturesResponse = await axios.get(`${API_BASE_URL}/signatures`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log(`✅ Signatures fetch successful!`);
      console.log(`   Signatures count: ${signaturesResponse.data.signatures.length}`);
      
    } catch (sigError) {
      console.log(`❌ Signatures fetch failed:`);
      console.log(`   Status: ${sigError.response?.status}`);
      console.log(`   Message: ${sigError.response?.data?.message || sigError.message}`);
      console.log(`   Full error data:`, sigError.response?.data);
      
      if (sigError.response?.status === 403) {
        console.log(`   🔍 This indicates a permission issue`);
      }
    }

    // Step 4: Check user in database
    console.log('\n4. Checking user in database...');
    console.log(`   Working password was: ${workingPassword}`);
    console.log(`   User role from login: ${user.role}`);
    console.log(`   User role from profile: ${profileUser.role}`);

    console.log('\n🎉 Test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    }
  }
}

// Run the test
testAlmannaeiAuth();
