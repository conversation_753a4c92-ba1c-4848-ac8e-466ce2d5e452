interface LocationData {
  latitude: number;
  longitude: number;
  accuracy?: number;
  altitude?: number;
  heading?: number;
  speed?: number;
  source: 'gps' | 'ip' | 'manual';
}

interface LocationConsentStatus {
  consentGiven: boolean;
  consentType: string;
  allowAdminMonitoring: boolean;
  allowLocationHistory: boolean;
}

class LocationTrackingService {
  private ws: WebSocket | null = null;
  private watchId: number | null = null;
  private isTracking = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private updateInterval: NodeJS.Timeout | null = null;
  private lastKnownLocation: LocationData | null = null;
  private consentStatus: LocationConsentStatus | null = null;

  constructor() {
    this.checkConsentStatus();
  }

  /**
   * Check user's location consent status
   */
  async checkConsentStatus(): Promise<boolean> {
    try {
      const token = localStorage.getItem('token');
      if (!token) return false;

      const response = await fetch('/api/location/consent', {
        headers: {
          'Authorization': `<PERSON><PERSON> ${token}`
        }
      });

      if (response.ok) {
        const result = await response.json();
        const activeConsent = result.data?.find((consent: any) => consent.consentGiven);
        
        if (activeConsent) {
          this.consentStatus = {
            consentGiven: true,
            consentType: activeConsent.consentType,
            allowAdminMonitoring: activeConsent.allowAdminMonitoring,
            allowLocationHistory: activeConsent.allowLocationHistory
          };
          return true;
        }
      }
      
      this.consentStatus = null;
      return false;
    } catch (error) {
      console.error('Error checking consent status:', error);
      return false;
    }
  }

  /**
   * Start location tracking
   */
  async startTracking(): Promise<boolean> {
    if (this.isTracking) return true;

    // Check consent first
    const hasConsent = await this.checkConsentStatus();
    if (!hasConsent) {
      console.log('Location tracking requires user consent');
      return false;
    }

    this.isTracking = true;

    // Start WebSocket connection
    this.connectWebSocket();

    // Start GPS tracking if consent allows
    if (this.consentStatus?.consentType !== 'ip_tracking') {
      this.startGPSTracking();
    }

    // Start periodic updates
    this.startPeriodicUpdates();

    console.log('📍 Location tracking started');
    return true;
  }

  /**
   * Stop location tracking
   */
  stopTracking(): void {
    this.isTracking = false;

    // Stop GPS tracking
    if (this.watchId !== null) {
      navigator.geolocation.clearWatch(this.watchId);
      this.watchId = null;
    }

    // Close WebSocket
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    // Clear intervals
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    console.log('📍 Location tracking stopped');
  }

  /**
   * Connect to WebSocket for real-time updates
   */
  private connectWebSocket(): void {
    const token = localStorage.getItem('token');
    if (!token) return;

    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    const wsUrl = `${protocol}//${host}/ws/location-tracking?token=${token}`;

    try {
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log('📍 Connected to location tracking WebSocket');
        this.reconnectAttempts = 0;
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.handleWebSocketMessage(data);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      this.ws.onclose = () => {
        console.log('📍 Location tracking WebSocket disconnected');
        this.ws = null;
        
        // Attempt to reconnect if still tracking
        if (this.isTracking && this.reconnectAttempts < this.maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
          this.reconnectAttempts++;
          
          this.reconnectTimeout = setTimeout(() => {
            this.connectWebSocket();
          }, delay);
        }
      };

      this.ws.onerror = (error) => {
        console.error('Location tracking WebSocket error:', error);
      };

    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
    }
  }

  /**
   * Handle WebSocket messages
   */
  private handleWebSocketMessage(data: any): void {
    switch (data.type) {
      case 'connected':
        console.log('📍 Location tracking service connected');
        break;
      case 'location_updated':
        console.log('📍 Location update confirmed');
        break;
      case 'error':
        console.error('Location tracking error:', data.message);
        break;
      case 'pong':
        // Heartbeat response
        break;
    }
  }

  /**
   * Start GPS tracking
   */
  private startGPSTracking(): void {
    if (!navigator.geolocation) {
      console.warn('Geolocation is not supported by this browser');
      return;
    }

    const options: PositionOptions = {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 60000 // 1 minute
    };

    this.watchId = navigator.geolocation.watchPosition(
      (position) => {
        const locationData: LocationData = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          altitude: position.coords.altitude || undefined,
          heading: position.coords.heading || undefined,
          speed: position.coords.speed || undefined,
          source: 'gps'
        };

        this.updateLocation(locationData);
      },
      (error) => {
        console.error('GPS tracking error:', error);
        // Fallback to IP-based location
        this.getIPLocation();
      },
      options
    );
  }

  /**
   * Get location from IP address
   */
  private async getIPLocation(): Promise<void> {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await fetch('/api/location/current', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.data?.latitude && result.data?.longitude) {
          const locationData: LocationData = {
            latitude: result.data.latitude,
            longitude: result.data.longitude,
            source: 'ip'
          };

          this.updateLocation(locationData);
        }
      }
    } catch (error) {
      console.error('Error getting IP location:', error);
    }
  }

  /**
   * Update location data
   */
  private async updateLocation(locationData: LocationData): Promise<void> {
    this.lastKnownLocation = locationData;

    // Send via WebSocket if connected
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'location_update',
        ...locationData
      }));
    }

    // Also send via HTTP API for reliability
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      await fetch('/api/location/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(locationData)
      });
    } catch (error) {
      console.error('Error updating location via API:', error);
    }
  }

  /**
   * Start periodic updates
   */
  private startPeriodicUpdates(): void {
    this.updateInterval = setInterval(() => {
      // Send heartbeat via WebSocket
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify({ type: 'ping' }));
      }

      // Re-send last known location if no GPS updates recently
      if (this.lastKnownLocation && this.consentStatus?.consentType === 'ip_tracking') {
        this.getIPLocation();
      }
    }, 60000); // Every minute
  }

  /**
   * Get current location status
   */
  getCurrentLocation(): LocationData | null {
    return this.lastKnownLocation;
  }

  /**
   * Check if tracking is active
   */
  isTrackingActive(): boolean {
    return this.isTracking;
  }

  /**
   * Get consent status
   */
  getConsentStatus(): LocationConsentStatus | null {
    return this.consentStatus;
  }
}

// Export singleton instance
export const locationTrackingService = new LocationTrackingService();
export default locationTrackingService;
